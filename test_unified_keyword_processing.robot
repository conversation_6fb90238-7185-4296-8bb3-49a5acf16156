*** Settings ***
Documentation    测试统一的制表符分割关键字处理
Library          SeleniumLibrary
Library          Collections
Library          String

*** Variables ***
${URL}           https://www.example.com
${USERNAME}      testuser
${PASSWORD}      testpass

*** Test Cases ***
测试统一关键字处理
    [Documentation]    测试内置、库、用户关键字的统一制表符分割处理
    [Tags]    关键字    制表符    统一处理
    
    # 内置关键字（应该显示为中蓝色）
    Log	这是内置Log关键字	INFO
    Set Variable	内置关键字测试值
    Should Be Equal	value1	value1
    Should Contain	Hello World	World
    Run Keyword	Log	运行关键字测试
    
    # 库关键字（应该显示为淡蓝色）
    Open Browser	about:blank	chrome
    Get Title
    Maximize Browser Window
    Create List	item1	item2	item3
    Get Length	${list}
    Convert To Uppercase	hello world
    Close Browser
    
    # 用户关键字（应该显示为紫色）
    自定义关键字示例	参数1	参数2
    Another Custom Keyword
    复杂参数关键字	${USERNAME}	${PASSWORD}

混合关键字类型测试
    [Documentation]    测试同一行中不同类型关键字的处理
    [Tags]    混合关键字
    
    # 同一行包含多种关键字类型
    Log	开始测试	INFO
    ${result}=	Set Variable	test_value
    Should Be Equal	${result}	test_value
    
    # 复杂的制表符分割场景
    Run Keyword If	True	Log	条件为真
    ${list}=	Create List	a	b	c
    ${length}=	Get Length	${list}
    Should Be Equal As Numbers	${length}	3

变量和关键字混合
    [Documentation]    测试变量和关键字在制表符分割中的正确识别
    [Tags]    变量    关键字
    
    # 变量应该保持橙色，关键字应该按类型高亮
    ${var1}=	Set Variable	value1
    ${var2}=	Create List	${var1}	value2
    ${var3}=	Get From List	${var2}	0
    Log	变量值: ${var1}, ${var2}, ${var3}
    
    # 复杂的变量和关键字组合
    ${dict}=	Create Dictionary	key1=${var1}	key2=${var3}
    ${keys}=	Get Dictionary Keys	${dict}
    Log	字典键: ${keys}

字符串和关键字混合
    [Documentation]    测试字符串和关键字的正确区分
    [Tags]    字符串    关键字
    
    # 字符串应该保持红色，关键字应该按类型高亮
    Log	"这是字符串参数"	INFO
    Set Variable	"string_value"
    Should Contain	"Hello World"	"World"
    
    # 单引号字符串
    Log	'单引号字符串'
    Should Be Equal	'test'	'test'
    
    # 混合引号
    Should Not Be Equal	"double"	'single'

设置项和关键字
    [Documentation]    测试设置项不被误识别为关键字
    [Tags]    设置项
    
    # 设置项应该保持靛蓝色，不被识别为关键字
    [Setup]	Log	测试设置
    [Teardown]	Log	测试清理
    
    Log	正常的关键字执行
    Should Be True	True

复杂嵌套场景
    [Documentation]    测试复杂的嵌套和多层制表符场景
    [Tags]    复杂场景
    
    # 多层嵌套的关键字调用
    Run Keyword	Run Keyword	Log	嵌套关键字调用
    
    # FOR循环中的关键字
    FOR	${i}	IN RANGE	1	4
        Log	循环次数: ${i}
        ${result}=	Set Variable	loop_${i}
        Should Contain	${result}	${i}
    END
    
    # IF条件中的关键字
    IF	True
        Log	条件为真
        Set Variable	true_value
    ELSE
        Log	条件为假
        Set Variable	false_value
    END

*** Keywords ***
自定义关键字示例
    [Arguments]    ${param1}    ${param2}
    [Documentation]    用户自定义关键字，测试内部的关键字处理
    
    # 在用户关键字中使用各种类型的关键字
    Log	用户关键字执行: ${param1}, ${param2}
    ${result}=	Set Variable	${param1}_${param2}
    ${list}=	Create List	${param1}	${param2}	${result}
    ${length}=	Get Length	${list}
    Should Be Equal As Numbers	${length}	3
    RETURN	${result}

Another Custom Keyword
    [Documentation]    另一个用户关键字
    
    # 制表符分割的各种关键字
    Log	执行另一个用户关键字
    ${time}=	Get Time
    Log	当前时间: ${time}
    Should Be True	len('${time}') > 0

复杂参数关键字
    [Arguments]    ${username}    ${password}    ${optional}=default
    [Documentation]    带复杂参数的用户关键字
    
    # 复杂的参数处理和关键字调用
    Log	用户名: ${username}
    Log	密码: ****
    Log	可选参数: ${optional}
    
    # 条件处理
    Run Keyword If	'${optional}' != 'default'	Log	使用了自定义可选参数
    
    # 返回复合结果
    ${result}=	Create Dictionary	user=${username}	status=processed
    RETURN	${result}

带循环的关键字
    [Documentation]    包含循环的用户关键字
    
    ${results}=	Create List
    
    FOR	${i}	IN RANGE	1	6
        ${item}=	Set Variable	item_${i}
        Append To List	${results}	${item}
        Log	添加项目: ${item}
    END
    
    ${count}=	Get Length	${results}
    Log	总共处理了 ${count} 个项目
    Should Be Equal As Numbers	${count}	5
    
    RETURN	${results}
