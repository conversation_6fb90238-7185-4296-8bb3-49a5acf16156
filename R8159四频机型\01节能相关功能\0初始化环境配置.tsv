*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
带数据开站	合并开站文件并开站	${GNODEB}	D:/kaizhan/5818_eight_celll.xlsm				
	释放实例化单板_多模	${GNODEB}					
	释放实例化无线配置_多模	${GNODEB}					
	${XML_PATH}	导出基站数据_多模	${GNODEB}				
	实例化单板_多模	${GNODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
修改环境配置	修改NR载波son载波对象	${GNODEB}	1	9			
	修改NR载波son载波对象	${GNODEB}	2	2			
	修改NR载波son载波对象	${GNODEB}	3	3			
	修改NR载波son载波对象	${GNODEB}	4	5			
	修改NR载波son载波对象	${GNODEB}	5	4			
	修改NR载波son载波对象	${GNODEB}	6	6			
	修改NR载波son载波对象	${GNODEB}	7	7			
	同步规划区数据_多模	${GNODEB}					
	[Teardown]						
							
test	#计数器						
	${measureIdList}	查询基站已有测量任务_多模	${GNODEB}				
	同步基站测量任务_多模	${GNODEB}	${measureIdList}				
	#AauChannel 37060						
	${moIdList}	create list	101_9	101_10	101_11	101_12	
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	0<x<80	22	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	0<x<80	23	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	0<x<80	24	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	x<100	25	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	x>600	26	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	x<10	27	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	x<10	28	10
	...	14					
	获取并校验前一刻钟kpi是否正常	${GNODEB}	AauChannel	${moIdList}	x<10	29	10
	...	14					
	[Teardown]						
							
test2	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	打开所有频段符号关断开关						
	sleep	30					
	关闭所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Stop ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	[Teardown]						
							
test3	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	打开所有频段载波关断开关						
	sleep	30					
	关闭所有频段载波关断开关						
	sleep	90					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	UDP上下行灌包测试_多模						
	[Teardown]						
							
test4	${filterDict}	create dictionary	mocName=PositionAssistance	moId=1			
	${attrDict}	create dictionary	pollTime=4				
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=5				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	${attrDict}	create dictionary	moId=1	savingMode=2	refSentinelPrruList=Equipment=1,ReplaceableUnit=112	savingPolicy=1	deltaTLimit=30
	${keyMoPathDict}	create dictionary	SupportFunction=1				
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruPowerSaving	${keyMoPathDict}	
	检查激活配置	${GNODEB}					
	${attrDict}	create dictionary	moId=1	refReplaceableUnit=Equipment=1,ReplaceableUnit=111	weekdayEndTime=23:59	weekendEndTime=23:59	
	${keyMoPathDict}	create dictionary	SupportFunction=1				
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruSavingArray	${keyMoPathDict}	
	检查激活配置	${GNODEB}					
	清除PRRU定时下电标记_多模	${VSW}					
	[Teardown]						
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	Comment	导出基站数据_多模	${GNODEB}				
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	Comment	创建UE对象	${CPE4}				
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	Comment	实例化无线配置_多模	${ENODEB}	${XML_PATH}			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	Comment	删除UE对象	${CPE4}				
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
打开NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
打开NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
ITRAN-LTE-FDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-FDD-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
打开所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR符号关断开关	${cell}				
							
关闭所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR符号关断开关	${cell}				
							
打开ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR载波关断开关	${cell}				
							
关闭所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR载波关断开关	${cell}				
							
关闭所有LTE载波关断开关	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
打开所有LTE载波关断开关	打开ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
性能测量测试模板	[Arguments]	${nameList}	${queryModleDict}	${filterlayer}=me	${filterlayer2}=me		
	${time}	查询基站时间_多模	${GNODEB}				
	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	30		
	: FOR	${name}	IN	@{nameList}			
	\	创建测量任务_多模	${GNODEB}	${name}			
	sleep	35min					
	${filePathList}	create list					
	: FOR	${queryModle}	IN	@{queryModleDict}			
	\	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}		
	\	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}
	...	${endTime}	${filterlayer2}	900			
	\	append to list	${filePathList}	${filePath}			
	[Teardown]	清除测量任务	@{nameList}				
	[Return]	${filePathList}					
							
清除测量任务	[Arguments]	@{nameList}					
	: FOR	${name}	IN	@{nameList}			
	\	run keyword and continue on failure	删除测量任务_多模	${GNODEB}	${name}		
							
关闭所有频段符号关断开关	#关闭FDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	#关闭TDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	#关闭SON节能配置						
	${attr}	create dictionary	esDTXSwitch=0	notifyBBFlag=1	weekdayDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段符号关断开关	#FDD和TDD制式符号关断开关打开						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
关闭所有频段载波关断开关	#LTE制式载波关断总开关关闭						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段载波关断开关	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
合并开站文件并开站	[Arguments]	${enbAlias}	${filePath}				
	${version}	查询基站运行版本号_多模	${enbAlias}	SOFTWARE			
	${tarName}	查询TAR包名称_多模	${enbAlias}	${version}			
	${tarName}	run keyword if	'${tarName}'==''	set variable	UNI_${version}.tar		
	...	ELSE	set variable	${tarName}			
	${filePathNew}	导出开站模板文件_多模	${enbAlias}	${tarName}			
	${filePath}	合并开站模板文件_多模	${filePathNew}	${filePath}			
	按开站模板开站_多模	${enbAlias}	${filePath}	${version}			
	sleep	600					
							
修改NR载波son载波对象	[Arguments]	${gnbAlias}	${moId}	${nrCarrierId}			
	${filterDict}	create dictionary	mocName=NRCarrierObj	moId=${moId}			
	${attrDict}	create dictionary	nrCarrierId=${nrCarrierId}				
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}			
