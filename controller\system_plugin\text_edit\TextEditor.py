# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     TextEdit
   Description :
   Author :       10140129
   date：          2019/10/23
-------------------------------------------------
   Change Activity:
                   2019/10/23:
-------------------------------------------------
"""
import keyword
import os
import sys
import traceback
import re

from PyQt5.Qsci import QsciScintilla, QsciAPIs
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QCursor
from PyQt5.QtWidgets import QApplication, QVBoxLayout

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.text_edit.SearchArea import SearchArea
from iplatform.highlight.PythonHighlighter import <PERSON><PERSON><PERSON><PERSON><PERSON>
from iplatform.highlight.RobotHighlighter import Robot<PERSON>ighlighter
from model.CurrentItem import CurrentItem
from model.data_file.Repository import DataFileRepository, ProjectTreeRepository
from settings.UserSettings import UserSettings
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper
TEXT_EDIT = 1


class TextEditor(QsciScintilla):

    def __init__(self):
        super(TextEditor, self).__init__()
        self.setWindowTitle('TextEditor')
        self.setWrapMode(self.WrapNone)
        self._connect_signals()
        self.last_file_path = None
        self.last_file_content = ""
        self.setUtf8(True)
        self.setTabWidth(4)
        self._set_caret_line()
        self._set_auto_completion()
        # 根据文件类型选择高亮器
        self.lexer = PythonHighlighter(self)
        self.setLexer(self.lexer)
        self._prepare_keywords()
        self.setMarginsFont(QFont(UserSettings().get_value("FONT"), 10))
        self._set_scroll()

        # 初始化跳转功能相关属性
        self._ctrl_pressed = False
        self._underlined_indicators = []  # 存储下划线指示器
        self._current_hover_function = None  # 当前悬停的函数名
        self.setMouseTracking(True)  # 启用鼠标跟踪

    def _set_auto_completion(self):
        self.setAutoCompletionSource(QsciScintilla.AcsAll)
        self.setAutoCompletionCaseSensitivity(True)
        self.setAutoCompletionReplaceWord(False)
        self.setAutoCompletionThreshold(1)
        self.setAutoCompletionUseSingle(QsciScintilla.AcusExplicit)

    def _prepare_keywords(self):
        self.__api = QsciAPIs(self.lexer)
        autocompletions = keyword.kwlist + UserSettings().get_value('PYTHON_KEYWORDS')
        for ac in autocompletions:
            self.__api.add(ac)
        self.__api.prepare()

    def _connect_signals(self):
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.show_item.connect(self._show_content)
        self._signal_distributor.highlight_keyword.connect(self._highlight_py_keyword)
        self._signal_distributor.refresh_text_edit.connect(self._refresh_content)
        self.textChanged.connect(self.set_star)

    def _set_caret_line(self):
        self.setCaretLineVisible(True)
        lineColor = QColor(Qt.yellow).lighter(160)
        self.setCaretLineBackgroundColor(lineColor)

    def _set_scroll(self):
        self.SendScintilla(self.SCI_SETSCROLLWIDTH, 300)
        self.SendScintilla(self.SCI_SETSCROLLWIDTHTRACKING, True)

    def load(self):
        self._layout = QVBoxLayout()
        search_area = SearchArea(self)
        search_area.load()
        self._layout.addLayout(search_area.get_layout())
        self._layout.addWidget(self)

    def get_layout(self):
        return self._layout

    def set_last_file_path(self, path):
        self.last_file_path = path

    def load_file(self, path):
        if path:
            path = os.path.abspath(path)
            self._file_path = path
            self._data_file_obj = None
            if os.path.isdir(path):
                data_file_obj = ProjectTreeRepository().find(path).init_file
            else:
                data_file_obj = DataFileRepository().find(path)

            # 根据文件类型设置高亮器
            if path.endswith('.robot') or path.endswith('.tsv'):
                print('robot')
                self.lexer = RobotHighlighter(self)
            else:
                self.lexer = PythonHighlighter(self)

            # 设置高亮器
            self.setLexer(self.lexer)

            self.last_file_content = self.get()
            if data_file_obj:
                self.setReadOnly(False)
                self.setText(data_file_obj.get_content())
                # 再次确保高亮更新
                self.SendScintilla(QsciScintilla.SCI_COLOURISE, 0, -1)
            else:
                self.setText("")
                self.setReadOnly(True)
            self._data_file_obj = data_file_obj

    def get(self):
        return self.text()
#         return self.document().toPlainText()

#     def resizeEvent(self, *e):
#         cr = self.contentsRect()
#         rec = QRect(cr.left(), cr.top(), self.number_bar.getWidth(), cr.height())
#         self.number_bar.setGeometry(rec)

    def _show_content(self, item):
        tab = PluginRepository().find('EDIT_TAB')
        if tab.currentIndex() == TEXT_EDIT:
            path = CurrentItem().get().get("path")
            print(CurrentItem().get())
            print(path)
            if not path:
                return
            if not self.last_file_path or path != self.last_file_path:
                print(self.last_file_path)
                self.load_file(path)

            # 高亮并滚动到item名称
            text = CurrentItem().get().get("name")
            print(text)
            if not text:
                return
            if path.endswith('.robot') or path.endswith('.tsv'):
                line_number = self.find_line_number(self.text(), text)
                print(line_number)
                if line_number:
                    self.ensureLineVisible(line_number)
                    # 只定位光标，不选中文字（避免与Ctrl+点击跳转冲突）
                    self.setCursorPosition(line_number, 0)
                    # 确保清除任何选中的文字
                    self.clearSelection()
                    # 延迟清除，处理可能的异步选中
                    self._clear_selection_after_jump()

    def _highlight_py_keyword(self, text):
        line_number = self.find_py_keyword_line_number(self.text(), text)
        print(line_number)
        if line_number:
            self.ensureLineVisible(line_number)
            # 只定位光标，不选中文字（避免与Ctrl+点击跳转冲突）
            self.setCursorPosition(line_number, 8)
            # 确保清除任何选中的文字
            self.clearSelection()
            # 延迟清除，处理可能的异步选中
            self._clear_selection_after_jump()

    def find_line_number(self, text, target):
        lines = text.splitlines()
        for i, line in enumerate(lines):
            if line.startswith(target):  # 判断是否以目标字符串开头
                return i
        return -1  # 如果找不到，返回 -1

    def find_py_keyword_line_number(self, text, target):
        lines = text.splitlines()
        for i, line in enumerate(lines):
            if target in line and 'def ' in line:  # 判断是否以目标字符串开头
                return i
        return -1  # 如果找不到，返回 -1

    def _refresh_content(self, item):
        path = CurrentItem().get().get("path")
        if not path:
            return
        self.load_file(path)

    def set_star(self):
        if self._data_file_obj and self._file_path == CurrentItem().get().get("path"):
            SignalDistributor().text_editor_modify(CurrentItem().get_current_item())
        self.setMarginWidth(0, len(str(self.lines())) * 11)

        # 确保高亮更新
        self.SendScintilla(QsciScintilla.SCI_COLOURISE, 0, -1)

    def notice_update(self):
        if CurrentItem().name:
            current_item_path = CurrentItem().get().get("path")
            last_item = ProjectTreeItemRepository().query(self.last_file_path)
            tab_obj = PluginRepository().find('EDIT_TAB')
            if last_item and tab_obj.tabText(tab_obj.currentIndex()) == 'Text Edit' and \
                    self.last_file_path and self.last_file_path != current_item_path and last_item.text(0).startswith('*'):
                SignalDistributor().text_editor_update(last_item, {'text_edit': self.last_file_content})

    def mousePressEvent(self, event):
        """处理鼠标点击事件，实现Ctrl+左键跳转到Python函数定义"""
        # 先调用父类的事件处理
        super().mousePressEvent(event)

        # 检查是否是Ctrl+左键点击
        if (event.button() == Qt.LeftButton and
            QApplication.keyboardModifiers() == Qt.ControlModifier):
            self._handle_ctrl_click(event)

    def _handle_ctrl_click(self, event):
        """处理Ctrl+点击事件"""
        try:
            # 获取点击位置的文本
            function_name = self._get_function_name_at_position(event.pos())
            if function_name:
                # 尝试跳转到函数定义
                self._jump_to_function_definition(function_name)
        except Exception as e:
            print(f"跳转函数定义时出错: {e}")

    def _get_function_name_at_position(self, pos):
        """获取指定位置的函数名"""
        try:
            # 使用SendScintilla获取鼠标位置对应的文本位置
            # SCI_POSITIONFROMPOINT = 2022
            position = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINT, pos.x(), pos.y())
            if position < 0:
                return None

            # 从位置获取行号和列号
            # SCI_LINEFROMPOSITION = 2166
            line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, position)
            # SCI_GETCOLUMN = 2129
            column = self.SendScintilla(QsciScintilla.SCI_GETCOLUMN, position)

            if line < 0:
                return None

            # 获取当前行的文本
            line_text = self.text(line)
            if not line_text or column >= len(line_text):
                return None

            # 查找函数名的边界
            start = column
            end = column

            # 向前查找函数名的开始
            while start > 0 and self._is_identifier_char(line_text[start - 1]):
                start -= 1

            # 向后查找函数名的结束
            while end < len(line_text) and self._is_identifier_char(line_text[end]):
                end += 1

            # 提取函数名
            function_name = line_text[start:end]

            # 验证是否是有效的函数名（支持中文字符）
            if function_name and (function_name[0].isalpha() or function_name[0] == '_' or ord(function_name[0]) > 127):
                return function_name

            return None
        except Exception as e:
            print(f"获取函数名时出错: {e}")
            return None

    def _jump_to_function_definition(self, function_name):
        """跳转到函数定义"""
        try:
            # 首先在当前文件中查找函数定义
            line_number = self._find_function_definition_in_current_file(function_name)
            if line_number >= 0:
                # 跳转到函数定义行，但不选中文字
                self.ensureLineVisible(line_number)
                self.setCursorPosition(line_number, 0)
                # 确保清除任何选中的文字
                self.clearSelection()
                return

            # 如果在当前文件中没找到，尝试使用现有的关键字跳转机制
            self._try_keyword_jump(function_name)
            # 跳转后清除任何可能的文字选中
            self._clear_selection_after_jump()

        except Exception as e:
            print(f"跳转到函数定义时出错: {e}")

    def _clear_selection_after_jump(self):
        """跳转后清除文字选中"""
        try:
            # 使用多次延迟清除选中，确保跳转完成后再清除
            from PyQt5.QtCore import QTimer
            # 立即清除一次
            self.clearSelection()
            # 50毫秒后再清除一次
            QTimer.singleShot(50, self.clearSelection)
            # 100毫秒后再清除一次（处理异步信号）
            QTimer.singleShot(100, self.clearSelection)
            # 200毫秒后最后清除一次
            QTimer.singleShot(200, self.clearSelection)
        except Exception as e:
            print(f"清除选中文字时出错: {e}")

    def _find_function_definition_in_current_file(self, function_name):
        """在当前文件中查找函数定义"""
        try:
            text = self.text()
            lines = text.splitlines()

            # 查找函数定义的正则表达式
            # 匹配 "def function_name(" 或 "def function_name (" 的模式
            pattern = r'^\s*def\s+' + re.escape(function_name) + r'\s*\('

            for i, line in enumerate(lines):
                if re.match(pattern, line):
                    return i

            return -1
        except Exception as e:
            print(f"在当前文件中查找函数定义时出错: {e}")
            return -1

    def _try_keyword_jump(self, function_name):
        """尝试使用现有的关键字跳转机制"""
        try:
            # 使用现有的SpecifiedKeywordJumper进行跳转
            jumper = SpecifiedKeywordJumper()
            keyword_path = jumper.get_keyword_path_from_local_repository(function_name)
            if keyword_path:
                jumper.get_keyword_item(keyword_path, function_name)
            else:
                print(f"未找到函数定义: {function_name}")
        except Exception as e:
            print(f"使用关键字跳转时出错: {e}")

    def keyPressEvent(self, event):
        """处理键盘按下事件"""
        super().keyPressEvent(event)
        if event.key() == Qt.Key_Control:
            self._ctrl_pressed = True

    def keyReleaseEvent(self, event):
        """处理键盘释放事件"""
        super().keyReleaseEvent(event)
        if event.key() == Qt.Key_Control:
            self._ctrl_pressed = False
            self._clear_underlines()

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        super().mouseMoveEvent(event)

        if self._ctrl_pressed:
            # 获取鼠标位置的函数名
            function_name = self._get_function_name_at_position(event.pos())
            if function_name and self._can_jump_to_function(function_name):
                if function_name != self._current_hover_function:
                    self._clear_underlines()
                    self._add_underline_at_position(event.pos(), function_name)
                    self._current_hover_function = function_name
                    self.setCursor(QCursor(Qt.PointingHandCursor))
            else:
                if self._current_hover_function:
                    self._clear_underlines()
                    self._current_hover_function = None
                    self.setCursor(QCursor(Qt.IBeamCursor))
        else:
            if self._current_hover_function:
                self._clear_underlines()
                self._current_hover_function = None
                self.setCursor(QCursor(Qt.IBeamCursor))

    def _can_jump_to_function(self, function_name):
        """检查函数是否可以跳转"""
        try:
            # 检查当前文件中是否有函数定义
            if self._find_function_definition_in_current_file(function_name) >= 0:
                return True

            # 检查是否在关键字库中
            jumper = SpecifiedKeywordJumper()
            keyword_path = jumper.get_keyword_path_from_local_repository(function_name)
            return keyword_path is not None
        except:
            return False

    def _add_underline_at_position(self, pos, function_name):
        """在指定位置添加下划线"""
        try:
            # 获取位置信息
            position = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINT, pos.x(), pos.y())
            if position < 0:
                return

            line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, position)

            # 获取当前行的文本
            line_text = self.text(line)
            if not line_text:
                return

            # 获取行的起始位置
            line_start_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)

            # 计算在行内的字符偏移
            char_offset = position - line_start_pos

            # 确保偏移量在有效范围内
            if char_offset < 0 or char_offset >= len(line_text):
                return

            # 查找函数名的边界（使用字符索引）
            start = char_offset
            end = char_offset

            # 向前查找函数名的开始
            while start > 0 and self._is_identifier_char(line_text[start - 1]):
                start -= 1

            # 向后查找函数名的结束
            while end < len(line_text) and self._is_identifier_char(line_text[end]):
                end += 1

            # 验证函数名
            extracted_name = line_text[start:end]
            if extracted_name == function_name:
                # 使用两个指示器：一个用于字体颜色，一个用于下划线
                # 指示器1：字体颜色
                color_indicator_id = 8
                self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, color_indicator_id)
                # 使用INDIC_TEXTFORE改变字体颜色（如果支持）
                try:
                    self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, color_indicator_id, 22)  # INDIC_TEXTFORE = 22
                    self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, color_indicator_id, 0x0000FF)  # 蓝色
                except:
                    # 如果不支持INDIC_TEXTFORE，使用INDIC_ROUNDBOX作为替代
                    self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, color_indicator_id, QsciScintilla.INDIC_ROUNDBOX)
                    self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, color_indicator_id, 0xE6F3FF)  # 浅蓝色背景
                    self.SendScintilla(QsciScintilla.SCI_INDICSETALPHA, color_indicator_id, 100)  # 半透明

                # 指示器2：下划线
                underline_indicator_id = 9
                self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, underline_indicator_id)
                self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, underline_indicator_id, QsciScintilla.INDIC_PLAIN)
                self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, underline_indicator_id, 0x0000FF)  # 蓝色下划线

                # 使用字节位置计算，支持中文字符
                # 将字符索引转换为字节位置
                start_byte_pos = self._char_index_to_byte_position(line, start)
                end_byte_pos = self._char_index_to_byte_position(line, end)

                # 添加字体颜色指示器
                self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, color_indicator_id)
                self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_byte_pos, end_byte_pos - start_byte_pos)

                # 添加下划线指示器
                self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, underline_indicator_id)
                self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_byte_pos, end_byte_pos - start_byte_pos)

                # 记录指示器信息
                self._underlined_indicators.append({
                    'indicator_id': color_indicator_id,
                    'start_pos': start_byte_pos,
                    'length': end_byte_pos - start_byte_pos
                })
                self._underlined_indicators.append({
                    'indicator_id': underline_indicator_id,
                    'start_pos': start_byte_pos,
                    'length': end_byte_pos - start_byte_pos
                })

        except Exception as e:
            print(f"添加下划线时出错: {e}")

    def _is_identifier_char(self, char):
        """检查字符是否是标识符字符（支持中文）"""
        return char.isalnum() or char == '_' or ord(char) > 127  # 支持中文等非ASCII字符

    def _char_index_to_byte_position(self, line, char_index):
        """将字符索引转换为字节位置"""
        try:
            line_start_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)
            line_text = self.text(line)

            if char_index <= 0:
                return line_start_pos

            if char_index >= len(line_text):
                return line_start_pos + len(line_text.encode('utf-8'))

            # 获取到指定字符索引的文本部分
            text_part = line_text[:char_index]
            # 计算UTF-8编码的字节长度
            byte_length = len(text_part.encode('utf-8'))

            return line_start_pos + byte_length

        except Exception as e:
            print(f"字符索引转换字节位置时出错: {e}")
            # 回退到简单的字符位置计算
            line_start_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)
            return line_start_pos + char_index

    def _clear_underlines(self):
        """清除所有下划线"""
        try:
            for indicator_info in self._underlined_indicators:
                indicator_id = indicator_info['indicator_id']
                start_pos = indicator_info['start_pos']
                length = indicator_info['length']

                self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
                self.SendScintilla(QsciScintilla.SCI_INDICATORCLEARRANGE, start_pos, length)

            self._underlined_indicators.clear()
        except Exception as e:
            print(f"清除下划线时出错: {e}")

# def TextEditorChanged(self):
#     def decorator(func):
#         def inner(*arg, **kwargs):
#             text_edit = PluginRepository().find('TEXT_EDIT')
#             text_edit.notice_update()
#             return func(*arg, **kwargs)
#         return inner
#     return decorator


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = TextEditor()
#     ex.load_fine("D:/CODE/rf-ide/testcases/env/calc/Calc.py")
    ex.load_file("D:/calc.py")
    ex.show()
    sys.exit(app.exec_())
