*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-5533859 【Qcell通用】prru预下电阶段、真实下电阶段、上电恢复正常阶段，发起prru诊断__RAN-5533859	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	180				
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	240					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	prru上电阶段	${GNODEB}	900				
	PRRU诊断测试						
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5585006 所有频段NR LTE进入符号关断后，复位所有prru，载波节能先退再进__RAN-5585006	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	20	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	Run Keyword If	'${status}' == 'Normal'	复位PRRU_多模	${board}		
	sleep	30					
	Wait Until Keyword Succeeds	10min	10sec	确认小区节能状态	${cell1}	0	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Stop ES	
	Wait Until Keyword Succeeds	10min	10sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	1	
	Wait Until Keyword Succeeds	10min	10sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	3	
	Wait Until Keyword Succeeds	10min	10sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	10sec	确认PRRU非节能状态	301-instance		
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	sleep	120					
	Wait Until Keyword Succeeds	10min	10sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	10sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	10sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	[Teardown]	恢复环境					
							
RAN-5585004 所有频段NR LTE进入符号关断后，复位部分prru（每个小区保留1个prru不复位），TDL FDL TNR小区不退节能，prru运行正常后进节能__RAN-5585004	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	20	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	复位PRRU_多模	301-instance					
	sleep	30					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} == ${power}				
	[Teardown]	恢复环境					
							
RAN-5583947 所有频段NR LTE进入载波关断后，复位所有prru,载波节能先退再进__RAN-5583947	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Start ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Close	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	301-instance	Close	
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	Run Keyword If	'${status}' == 'Normal'	复位PRRU_多模	${board}		
	sleep	30					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Stop ES	50
	sleep	240					
	Wait Until Keyword Succeeds	15min	10sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	10sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	10sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	10sec	确认所有频段载波PA开关状态	101-instance	Close	
	Wait Until Keyword Succeeds	15min	10sec	确认所有频段载波PA开关状态	301-instance	Close	
	[Teardown]	恢复环境					
							
RAN-5583946 所有频段NR LTE进入载波关断后，复位部分prru（每个小区保留1个prru不复位），TDL小区会先退节能再重新进节能,TNR FDL小区不退节能__RAN-5583946	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Start ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	复位PRRU_多模	301-instance					
	sleep	30					
	Wait Until Keyword Succeeds	15min	10sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	30sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	sleep	480					
	Wait Until Keyword Succeeds	15min	30sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	30sec	确认TDL小区节能状态	${ENODEB}	4	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	[Teardown]	恢复环境					
							
RAN-5545197 闭塞小区：FDL单载波配置，进入符号关断后闭塞解闭塞小区__RAN-5545197	ITRAN-LTE-FDD符号关断开关	1					
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	@{fddCellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cell}	IN	@{fddCellList}			
	\	关断EUtran小区_多模	${cell}				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop
	...	20	1				
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094461	
	: FOR	${cell}	IN	@{fddCellList}			
	\	解关断EUtran小区_多模	${cell}				
	sleep	100					
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	[Teardown]	恢复环境					
							
RAN-5585168 闭塞小区：TDL多载波配置，进入符号关断后闭塞解闭塞1个小区和全部小区__RAN-5585168	ITRAN-LTE-TDD符号关断开关	1					
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	关断EUtran小区_多模	${tddCell3}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094858	
	解关断EUtran小区_多模	${tddCell3}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	#闭塞全部的两个双载波TDL小区						
	ITRAN-LTE-TDD符号关断开关	1					
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	关断EUtran小区_多模	${tddCell3}					
	关断EUtran小区_多模	${tddCell7}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	3	
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094858	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	解关断EUtran小区_多模	${tddCell3}					
	解关断EUtran小区_多模	${tddCell7}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Open
	[Teardown]	恢复环境					
							
RAN-5585166 闭塞小区：TNR双载波配置，进入符号关断后闭塞解闭塞1个小区和全部小区__RAN-5585166	关闭NR符号关断开关	${cell2}					
	关闭NR符号关断开关	${cell3}					
	关闭NR符号关断开关	${cell4}					
	关闭NR符号关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR符号关断开关	${cell2}					
	打开NR符号关断开关	${cell3}					
	打开NR符号关断开关	${cell4}					
	打开NR符号关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	5	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell3}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell5}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	闭塞NR小区_多模	${cell4}					
	闭塞NR小区_多模	${cell2}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Stop ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell3}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell5}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=2	Cellshutdown
	解闭塞NR小区_多模	${cell4}					
	解闭塞NR小区_多模	${cell2}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Start ES	100
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	#闭塞4.9G全部的两个小区和闭塞2.6G的全部两个小区						
	关闭NR符号关断开关	${cell4}					
	关闭NR符号关断开关	${cell5}					
	关闭NR符号关断开关	${cell2}					
	关闭NR符号关断开关	${cell3}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR符号关断开关	${cell4}					
	打开NR符号关断开关	${cell5}					
	打开NR符号关断开关	${cell2}					
	打开NR符号关断开关	${cell3}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	5	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell5}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell3}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	闭塞NR小区_多模	${cell4}	${False}				
	闭塞NR小区_多模	${cell5}	${False}				
	闭塞NR小区_多模	${cell2}	${False}				
	闭塞NR小区_多模	${cell3}	${False}				
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Stop ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	5	Symbol Shutdown	Stop ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Stop ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell5}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell3}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=2	Cellshutdown
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=3	Cellshutdown
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	3,4,5,6	Close
	解闭塞NR小区_多模	${cell4}	${False}				
	解闭塞NR小区_多模	${cell5}	${False}				
	解闭塞NR小区_多模	${cell2}	${False}				
	解闭塞NR小区_多模	${cell3}	${False}				
	同步规划区数据_多模	${GNODEB}					
	sleep	70					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	5	Symbol Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Symbol Shutdown	Start ES	100
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell5}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell3}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	3,4,5,6	Open
	[Teardown]	恢复环境					
							
RAN-5584522 闭塞小区：FDL单载波配置，进入载波关断后闭塞解闭塞小区（闭塞小区时PA变为打开态）__RAN-5584522	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	sleep	30					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	7,8	Close
	@{fddCellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cell}	IN	@{fddCellList}			
	\	关断EUtran小区_多模	${cell}				
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	30	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	30	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	7,8	Open
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094461	
	: FOR	${cell}	IN	@{fddCellList}			
	\	解关断EUtran小区_多模	${cell}				
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	200	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	200	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	7,8	Close
	[Teardown]	恢复环境					
							
RAN-5584497 闭塞小区：TDL多载波配置，进入载波关断后闭塞解闭塞1个小区和全部小区（闭塞小区时PA仍为关闭态）__RAN-5584497	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	30					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	120					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	关断EUtran小区_多模	${tddCell3}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094858	
	解关断EUtran小区_多模	${tddCell3}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	200	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	#闭塞全部的两个双载波TDL小区						
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	30					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	120					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	关断EUtran小区_多模	${tddCell3}					
	关断EUtran小区_多模	${tddCell7}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	30	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	30	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094858	
	解关断EUtran小区_多模	${tddCell3}					
	解关断EUtran小区_多模	${tddCell7}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	200	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	200	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	[Teardown]	恢复环境					
							
RAN-5545094 闭塞小区：TNR双载波配置，进入载波关断后闭塞解闭塞1个小区和全部小区（闭塞小区时PA仍为关闭态）__RAN-5545094	关闭NR载波关断开关	${cell4}					
	关闭NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	${cell4}					
	打开NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4	Carrier Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	5	Carrier Shutdown	Start ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	闭塞NR小区_多模	${cell4}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	1	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	解闭塞NR小区_多模	${cell4}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	#闭塞4.9G全部的两个小区						
	关闭NR载波关断开关	${cell4}					
	关闭NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	${cell4}					
	打开NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4	Carrier Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	5	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	闭塞NR小区_多模	${cell4}					
	闭塞NR小区_多模	${cell5}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4	Carrier Shutdown	Stop ES	50
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	5	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=5	Cellshutdown
	解闭塞NR小区_多模	${cell4}					
	解闭塞NR小区_多模	${cell5}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4	Carrier Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	5	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	[Teardown]	恢复环境					
							
RAN-5584925 通道上多载波配置，只有部分载波进入符号关断时，PA不关闭。全部载波进入关断时PA才关闭（其中2.3 TDL多载波+2.6G NR双载波+4.9G NR双载波配置）__RAN-5584925	关闭所有频段符号关断开关						
	sleep	30					
	打开NR符号关断开关	${cell4}					
	打开NR符号关断开关	${cell6}					
	同步规划区数据_多模	${GNODEB}					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Start ES	50
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	6	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell5}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell6}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell7}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	3,4,5,6,9,10,11,12	Open
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell6}	${CPE2}	${PDN}			
	sleep	60					
	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	20	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	打开所有频段符号关断开关						
	sleep	240					
	关闭所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Stop ES	30	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Stop ES	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5583938 通道上多载波配置，只有部分载波进入载波关断时，PA不关闭。全部载波进入关断时PA才关闭（其中2.3 TDL多载波+2.6G NR双载波+4.9G NR双载波配置）__RAN-5583938	关闭所有频段载波关断开关						
	sleep	30					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	打开NR载波关断开关	${cell2}					
	打开NR载波关断开关	${cell6}					
	同步规划区数据_多模	${GNODEB}					
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	30	4	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;0;0;0
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	2	Carrier Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	6	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell3}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell6}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,9,10,11,12	Open
	验证TDL小区	${tddCell7}	${CPE3}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	sleep	60					
	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	4	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;3;0;0;0
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	2	Carrier Shutdown	Start ES	100
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	7	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Close
	打开所有频段载波关断开关						
	sleep	30					
	关闭所有频段载波关断开关						
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	7	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585053 闭塞通道：进入符号关断后，闭塞所有prru通道，再解闭塞所有prru通道，关闭符号关断__RAN-5585053	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	: FOR	${board}	IN	@{boards}			
	\	关闭PRRU功放_多模	${board}				
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Stop ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	: FOR	${board}	IN	@{boards}			
	\	打开PRRU功放_多模	${board}				
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	打开所有频段符号关断开关						
	sleep	30					
	关闭所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Stop ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585049 闭塞通道：闭塞prru上每个频段的部分通道，开启符号关断，再解闭塞prru通道，关闭符号关断__RAN-5585049	闭塞PRRU部分通道	301-instance	1,3,4,7,9,10				
	sleep	45					
	Wait Until Keyword Succeeds	5min	30sec	确认PRRU的PA通道状态	301-instance	1,3,4,7,9,10	Close
	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Start ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	1;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	20	4	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	解闭塞PRRU部分通道	301-instance	1,3,4,7,9,10				
	sleep	45					
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,3,4,7,9,10	Open
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	打开所有频段符号关断开关						
	sleep	30					
	关闭所有频段符号关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown	Stop ES	30	
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	20	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	4	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名_5824						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
打开NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
打开NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
ITRAN-LTE-FDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-FDD-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
打开所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR符号关断开关	${cell}				
							
关闭所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR符号关断开关	${cell}				
							
打开ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR载波关断开关	${cell}				
							
关闭所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR载波关断开关	${cell}				
							
恢复环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	90					
							
恢复等待环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	600					
							
关闭所有LTE载波关断开关	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
打开所有LTE载波关断开关	打开ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
prru定时下电预下电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
	sleep	${delayTime}					
							
prru定时下电真实下电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
	sleep	${delayTime}					
							
prru上电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	0				
	sleep	${delayTime}					
							
PB诊断测试_上电	[Arguments]	${pbAlias}	${port}=1				
	#资源利用率信息查询						
	${result}	查询PB资源使用率_多模	${VSW}				
	should be true	0< ${result[0]} <80					
	${total}	evaluate	${result[4]}+${result[5]}				
	should be true	${total}==100					
	#PB单板运行时间						
	${result}	查询PB工作时长_多模	${pbAlias}				
	should be true	0 < ${result}					
	#PB光/电模块诊断						
	${result}	PB光电模块诊断_多模	${pbAlias}	${port}			
	should be true	'${result[1]}'=='Success'					
	should be true	'${result[3]}'=='SFP Transceiver'					
	#PB以太网口状态检测						
	${result}	PB以太网口状态检测_多模	${pbAlias}	${port}			
	Should contain	${result}	HalfDuplex				
	#PB以太网口误码率诊断						
	${result}	PB以太网口误码率诊断_多模	${pbAlias}	${port}			
	should be true	'${result}'=='0'					
	#PB以太网口通信检测						
	${result}	PB以太网口SNR诊断_多模	${pbAlias}	${port}			
	should be true	'${result}'=='0'					
	#光/电误码率诊断						
	${result}	PB光口误码率诊断_多模	${pbAlias}	${port}			
	should be true	'${result}'=='0'					
	#PB光口状态检测						
	${result}	PB光口状态诊断_多模	${pbAlias}	${port}			
	should be true	'${result[1]}'=='In Position'					
	#PB单板测试						
	${result}	PB诊断测试_多模	${pbAlias}				
	should be true	0< ${result} <90					
	#POE供电状态检测						
	${result}	PB供电状态检测_多模	${pbAlias}				
	should be true	'${result[1]}'=='Normal'					
	#PB光纤测距						
	${result}	PB光纤测距_多模	${pbAlias}				
	should be true	'${result[2]}'=='Shorter Than 200 Meters'					
							
PB诊断测试_下电	[Arguments]	${pbAlias}					
	#资源利用率信息查询						
	${result}	查询PB资源使用率_多模	${VSW}				
	should be true	0< ${result[0]} <80					
	${total}	evaluate	${result[4]}+${result[5]}				
	should be true	${total}==100					
	#PB单板运行时间						
	${result}	查询PB工作时长_多模	${pbAlias}				
	should be true	0 < ${result}					
	#PB光/电模块诊断						
	${result}	PB光电模块诊断_多模	${pbAlias}				
	should be true	'${result[1]}'=='Success'					
	should be true	'${result[3]}'=='SFP Transceiver'					
	#PB以太网口状态检测						
	${result}	PB以太网口状态检测_多模	${pbAlias}				
	Should contain	${result}	HalfDuplex				
	#PB以太网口误码率诊断						
	${result}	PB以太网口误码率诊断_多模	${pbAlias}				
	Should contain	${result}	No Light/Electricity in Optical/Electric Port				
	#PB以太网口通信检测						
	${result}	PB以太网口SNR诊断_多模	${pbAlias}				
	Should contain	${result}	No Light/Electricity in Optical/Electric Port				
	#光/电误码率诊断						
	${result}	PB光口误码率诊断_多模	${pbAlias}				
	should be true	'${result}'=='0'					
	#PB光口状态检测						
	${result}	PB光口状态诊断_多模	${pbAlias}				
	should be true	'${result[1]}'=='In Position'					
	#PB单板测试						
	${result}	PB诊断测试_多模	${pbAlias}				
	should be true	0< ${result} <90					
	#POE供电状态检测						
	${result}	PB供电状态检测_多模	${pbAlias}				
	should be true	'${result[1]}'=='Unknown'					
	#PB光纤测距						
	${result}	PB光纤测距_多模	${pbAlias}				
	should be true	'${result[2]}'=='Shorter Than 200 Meters'					
							
查询PRRU资产信息	[Arguments]	${gnbAlias}					
	重复执行_多模	3	同步基站资产信息_多模	${gnbAlias}			
	获取基站硬件资产信息_多模	${gnbAlias}					
	@{boards}	根据类型获取实例化单板别名_多模	${gnbAlias}	256			
	: FOR	${board}	IN	@{boards}			
	\	${result}	获取单板硬件资产信息_多模	${board}			
	\	确认硬件资产上报正常_多模	${board}	${result}			
							
PRRU电源功率测量C37016	[Arguments]	${gnbAlias}					
	同步基站时间_多模	${gnbAlias}					
	${nameList}	create list	PrruPwr				
	${queryModleDict}	create dictionary	5818_prrupower_query=me,Equipment,ReplaceableUnit				
	${resPathList}	性能测量测试模板	${nameList}	${queryModleDict}			
	${colList}	create list	13				
	读取csv数据平均值，判断有值	@{resPathList}[0]	${colList}				
							
性能测量测试模板	[Arguments]	${nameList}	${queryModleDict}	${filterlayer}=me	${filterlayer2}=me		
	${time}	查询基站时间_多模	${GNODEB}				
	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	30		
	: FOR	${name}	IN	@{nameList}			
	\	创建测量任务_多模	${GNODEB}	${name}			
	sleep	35min					
	${filePathList}	create list					
	: FOR	${queryModle}	IN	@{queryModleDict}			
	\	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}		
	\	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}
	...	${endTime}	${filterlayer2}	900			
	\	append to list	${filePathList}	${filePath}			
	[Teardown]	清除测量任务	@{nameList}				
	[Return]	${filePathList}					
							
清除测量任务	[Arguments]	@{nameList}					
	: FOR	${name}	IN	@{nameList}			
	\	run keyword and continue on failure	删除测量任务_多模	${GNODEB}	${name}		
							
读取csv数据平均值，判断有值	[Arguments]	${filePath}	${colList}				
	${resList}	create list					
	: FOR	${col}	IN	@{colList}			
	\	${ave}	读取csv列数据平均值_多模	${filePath}	${col}		
	\	should be true	${ave}				
	\	append to list	${resList}	${ave}			
	[Return]	${resList}					
							
创建并设置PRRU节能下电持续时间	[Arguments]	${gnbAlias}	${delay}				
	创建PRRU定时节电参数_多模	${gnbAlias}					
	${time}	查询基站时间_多模	${GNODEB}				
	Comment	${a}	set variable	${time}			
	${tmp}	split string	${time}	T			
	${tmp}	split string	${tmp[1]}	:			
	${timeStart}	set variable	${tmp[0]}:${tmp[1]}				
	${timeEnd}	获取延时后的时间点	${delay}				
	修改PRRU工作日节电时间_多模	${gnbAlias}	${timeStart}	${timeEnd}			
	修改PRRU休息日节电时间_多模	${gnbAlias}	${timeStart}	${timeEnd}			
	修改定时下电温差	30					
	同步基站时间_多模	${gnbAlias}					
	同步测试机时间_多模	${gnbAlias}					
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
							
关闭所有频段符号关断开关	#关闭FDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	#关闭TDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	#关闭SON节能配置						
	${attr}	create dictionary	esDTXSwitch=0	notifyBBFlag=1	weekdayDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段符号关断开关	#FDD和TDD制式符号关断开关打开						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
关闭所有频段载波关断开关	#LTE制式载波关断总开关关闭						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段载波关断开关	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
合并开站文件并开站	[Arguments]	${enbAlias}	${filePath}				
	${version}	查询基站运行版本号_多模	${enbAlias}	SOFTWARE			
	${tarName}	查询TAR包名称_多模	${enbAlias}	${version}			
	${tarName}	run keyword if	'${tarName}'==''	set variable	UNI_${version}.tar		
	...	ELSE	set variable	${tarName}			
	${filePathNew}	导出开站模板文件_多模	${enbAlias}	${tarName}			
	${filePath}	合并开站模板文件_多模	${filePathNew}	${filePath}			
	按开站模板开站_多模	${enbAlias}	${filePath}	${version}			
	sleep	600					
							
PRRU诊断测试	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	Run Keyword If	'${status}' == 'Normal'	PRRU诊断测试_多模	${board}		
	\	${result}	Run Keyword If	'${status}' == 'Normal'	查询PRRU温度_多模	${board}	
	\	should be true	0< ${result} <90				
	\	Run Keyword If	'${status}' == 'Normal'	确认PRRU发射功率正常_多模	${board}		
	\	Run Keyword If	'${status}' == 'Normal'	确认PRRURssi正常_多模	${board}		
