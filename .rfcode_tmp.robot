*** Settings ***
Resource	../../../../userkeywords/basic_multi/resource.tsv
Resource	../../../../userkeywords/resource.tsv



*** Keywords ***
AMC下行拉远拉近_FTP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${fileName}	${expectput}=350	${ftpTestSeconds}=80
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	sleep	10
	${trafficId}	FTP下载	${ueAlias}	${pdnAlias}	${fileName}	true	10
	...	1024K
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	FOR	${value}	IN RANGE	0	25
	设置UE信号衰减	${ueAlias}	${value}
	sleep	6
	FOR	${value}	IN RANGE	0	25
	设置UE信号衰减	${ueAlias}	${24-${value}}
	sleep	6
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	停止FTP任务	${trafficId}
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}
	Comment	${time}	get time
	Comment	${ueid}	查询环境设备属性值	${ueAlias}	ueId
	Comment	${paraDict}	创建有序字典	UeId	${ueid}	执行阶段	RT	执行领域	SPA	环境编号	8123	用例编号	${caseID}	用例名	${caseName}	提交数据时间	${time}	版本	${versioninfo}	flag	kpi	frame_width	NSA_100M_2.5MS
	Comment	${paras}	Create List	DATA	Sheet1
	Comment	${time}	get time
	Comment	run keyword and ignore error	解析mts数据并入库	${result}	${paraDict}	db	${None}	handle_amc_mts_dl_ul_mvp_data	${paras}

AMC上行拉远拉近_FTP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${fileName}	${expectput}=350	${ftpTestSeconds}=80
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	sleep	10
	${trafficId}	FTP上传	${ueAlias}	${pdnAlias}	${fileName}	true	10
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	FOR	${value}	IN RANGE	0	25
	设置UE信号衰减	${ueAlias}	${value}
	sleep	6
	FOR	${value}	IN RANGE	0	25
	设置UE信号衰减	${ueAlias}	${24-${value}}
	sleep	6
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	停止FTP任务	${trafficId}
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}
	Comment	${time}	get time
	Comment	${ueid}	查询环境设备属性值	${ueAlias}	ueId
	Comment	${paraDict}	创建有序字典	UeId	${ueid}	执行阶段	RT	执行领域	SPA	环境编号	8123	用例编号	${caseID}	用例名	${caseName}	提交数据时间	${time}	版本	${versioninfo}	flag	kpi	frame_width	NSA_100M_2.5MS
	Comment	${paras}	Create List	DATA	Sheet1
	Comment	${time}	get time
	Comment	run keyword and ignore error	解析mts数据并入库	${result}	${paraDict}	db	${None}	handle_amc_mts_dl_ul_mvp_data	${paras}

AMC上下行拉远拉近_FTP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${DownloadfileName}	${UploadfileName}	${expectput}=350	${ftpTestSeconds}=80
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	${dltrafficId}	FTP下载	${ueAlias}	${pdnAlias}	${DownloadfileName}	true	10
	...	1024K
	${ultrafficId}	FTP上传	${ueAlias}	${pdnAlias}	${UploadfileName}	true	10
	sleep	15
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	FOR	${value}	IN RANGE	0	25
	设置UE信号衰减	${ueAlias}	${value}
	sleep	6
	FOR	${value}	IN RANGE	0	25
	设置UE信号衰减	${ueAlias}	${24-${value}}
	sleep	6
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	停止FTP任务	${dltrafficId}
	run keyword and continue on failure	停止FTP任务	${ultrafficId}
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}
	Comment	${time}	get time
	Comment	${ueid}	查询环境设备属性值	${ueAlias}	ueId
	Comment	${paraDict}	创建有序字典	UeId	${ueid}	执行阶段	RT	执行领域	SPA	环境编号	8123	用例编号	${caseID}	用例名	${caseName}	提交数据时间	${time}	版本	${versioninfo}	flag	kpi	frame_width	NSA_100M_2.5MS
	Comment	${paras}	Create List	DATA	Sheet1
	Comment	${time}	get time
	Comment	run keyword and ignore error	解析mts数据并入库	${result}	${paraDict}	db	${None}	handle_amc_mts_dl_ul_mvp_data	${paras}

AMC下行拉远拉近_UDP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1
	[Documentation]	${caseID} ：用例编号
	...	${casename} ：用例名称
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	400*${flowRate}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	DL	${flow}m
	...	9999	1024	4
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	30
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	15
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}
	${maxAtt}	evaluate	int(106+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${value}/2.0) > 30
	sleep	6
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt2}/2.0-${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上行拉远拉近_UDP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	700*${flowRate}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	UL	${flow}m
	...	9999	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	30
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}
	${maxAtt}	evaluate	int(108+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${value}/2.0) > 30
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt2}/2.0-${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上下行拉远拉近_UDP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1	${attRate}=1
	[Documentation]	${caseID} ：用例编号
	...	${casename} ：用例名称
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	5
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	400*${flowRate}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	DL	${flow}m
	...	9999	1024	4
	sleep	5
	${flow}	evaluate	700*${flowRate}
	${trafficId1}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	UL	${flow}m
	...	9999	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	30
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	15
	${rsrp}	获取UE的NR侧rsrp值_多模	${ueAlias}
	${maxAtt1}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt1}
	设置UE信号衰减	${ueAlias}	${${attRate}*${value}/2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${attRate}*${value}/2.0) > ${maxAtt}
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${attRate}*${maxAtt2}/2.0-${attRate}*${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包	${trafficId1}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC下行拉远拉近_TCP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	sleep	10
	${trafficId}	开始TCP收灌包	${ueAlias}	${pdnAlias}	DL
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	30
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/2.0}
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${maxAtt}/2.0-${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束TCP收灌包	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}
	Comment	${time}	get time
	Comment	${ueid}	查询环境设备属性值	${ueAlias}	ueId
	Comment	${paraDict}	创建有序字典	UeId	${ueid}	执行阶段	RT	执行领域	SPA	环境编号	8123	用例编号	${caseID}	用例名	${caseName}	提交数据时间	${time}	版本	${versioninfo}	flag	kpi	frame_width	NSA_100M_2.5MS
	Comment	${paras}	Create List	DATA	Sheet1
	Comment	${time}	get time
	Comment	run keyword and ignore error	解析mts数据并入库	${result}	${paraDict}	db	${None}	handle_amc_mts_dl_ul_mvp_data	${paras}
	Comment	NR小区AMC曲线数据入库	${gnbAlias}	${cellAlias}	${ueAlias}	${result}	AMC曲线

AMC上行拉远拉近_TCP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${trafficId}	开始TCP收灌包	${ueAlias}	${pdnAlias}	UL
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/2.0}
	sleep	4
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${maxAtt}/2.0-${value}/2.0}
	sleep	4
	设置UE信号衰减	${ueAlias}	0
	sleep	40
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束TCP收灌包	${trafficId}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上下行拉远拉近_TCP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${versioninfo}	查询基站运行版本号_多模	${gnbAlias}
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	${dltrafficId}	开始TCP收灌包	${ueAlias}	${pdnAlias}	DL
	${ultrafficId}	开始TCP收灌包	${ueAlias}	${pdnAlias}	UL
	sleep	15
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}
	${maxAtt}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${value}/2.0) > 30
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt}/2.0-${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束TCP收灌包	${dltrafficId}	DL
	run keyword and continue on failure	结束TCP收灌包	${ultrafficId}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC下行拉远拉近_UDP_多模
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1	${txNum}=4	${hocNum}=2
	[Documentation]	${caseID} ：用例编号
	...	${casename} ：用例名称
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	400*${flowRate}
	${trafficId}	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL	${flow}m
	...	9999	1024	${txNum}
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}	${txNum}
	${maxAtt}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	${hocNum}	evaluate	int(${hocNum})
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/${hocNum}}
	sleep	3
	${maxAtt2}	set variable	${value}
	sleep	6
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt2}/${hocNum}-${value}/${hocNum}}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上行拉远拉近_UDP_多模
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1	${txNum}=4	${hocNum}=2
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	700*${flowRate}
	${trafficId}	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL	${flow}m
	...	9999	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${hocNum}	evaluate	int(${hocNum})
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}	${txNum}
	${maxAtt}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/${hocNum}}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${value}/${hocNum}) > 30
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt2}/${hocNum}-${value}/${hocNum}}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上下行拉远拉近_UDP_多模
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1	${attRate}=1	${txNum}=4	${hocNum}=2
	[Documentation]	${caseID} ：用例编号
	...	${casename} ：用例名称
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	5
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	400*${flowRate}
	${trafficId}	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL	${flow}m
	...	9999	1024	${txNum}
	sleep	5
	${flow}	evaluate	700*${flowRate}
	${trafficId1}	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL	${flow}m
	...	9999	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}	${txNum}
	${maxAtt1}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt1}
	设置UE信号衰减	${ueAlias}	${${attRate}*${value}/${hocNum}}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${attRate}*${value}/${hocNum}) > ${maxAtt}
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${attRate}*${maxAtt2}/${hocNum}-${attRate}*${value}/${hocNum}}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId1}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

UDP下行灌包测试
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=4	${expectput}=600
	[Documentation]	【功能说明】：
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP下行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectput}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	sleep	10
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	DL	400m
	...	1000	1024	4
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	20
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectput}
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	计算MTS的平均值_多模	${result}

UDP上行灌包测试
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=1	${expectput}=280
	[Documentation]	【功能说明】：
	...	特定功能关键字，修改配置，然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：configType：配置类型。255：基线配置。0-19：修改下行传输模式。20：BWP带宽20M，40：BWP带宽40M，60：BWP带宽60M，111：不修改配置。
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 255 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP上行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectput}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	sleep	10
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	UL	700m
	...	1000	1024	1
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	20
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	UL
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectput}
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	计算MTS的平均值_多模	${result}

UDP上下行灌包测试
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=4	${expectputDL}=350	${expectputUL}=200
	[Documentation]	【功能说明】：
	...	特定功能关键字，修改配置，然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：configType：配置类型。255：基线配置。0-19：修改下行传输模式。20：BWP带宽20M，40：BWP带宽40M，60：BWP带宽60M，111：不修改配置。
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 255 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP上下行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectputDL}	${expectputUL}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	${returnValue}	create dictionary
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP上下行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectputDL}	${expectputUL}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	解闭塞NR小区_多模	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}	120
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${trafficId}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL
	...	${threadFlow}	1000	1024	${threadNum}
	${trafficId1}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL
	...	700m	1000	1300	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI	CELL_BLER
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	20
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI	CELL_BLER
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)	ulDtxTbCnt
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	${testResultDl}	run keyword and ignore error	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectputDL}
	${testResultUl}	run keyword and ignore error	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectputUL}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId1}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	计算MTS的平均值_多模	${result}
	${testResultDl}	evaluate	${testResultDl}[0]
	${testResultUl}	evaluate	${testResultUl}[0]
	${TEST_STATUS}	evaluate	'${testResultDl}' == 'PASS' and '${testResultUl}'== 'PASS'
	set global variable	${TEST_STATUS}

UDP下行灌包测试_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=4	${expectput}=600
	[Documentation]	【功能说明】：
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP下行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectput}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	sleep	10
	${trafficId}	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL	400m
	...	1000	1024	4
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectput}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	计算MTS的平均值_多模	${result}

UDP上行灌包测试_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=1	${expectput}=280
	[Documentation]	【功能说明】：
	...	特定功能关键字，修改配置，然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：configType：配置类型。255：基线配置。0-19：修改下行传输模式。20：BWP带宽20M，40：BWP带宽40M，60：BWP带宽60M，111：不修改配置。
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 255 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP上行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectput}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	sleep	10
	${trafficId}	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL	700m
	...	1000	1024	1
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectput}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	计算MTS的平均值_多模	${result}

UDP上下行灌包测试_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=4	${expectputDL}=350	${expectputUL}=200
	[Documentation]	【功能说明】：
	...	特定功能关键字，修改配置，然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：configType：配置类型。255：基线配置。0-19：修改下行传输模式。20：BWP带宽20M，40：BWP带宽40M，60：BWP带宽60M，111：不修改配置。
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 255 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${returnValue}	builtin.run keyword and return if	'VBPc' in '${vbpName}'	UDP上下行灌包测试_KPI_多模	${cellAlias}	${ueAlias}	${pdnAlias}
	...	${vswAlias}	${threadFlow}	${threadNum}	${expectputDL}	${expectputUL}
	run keyword if	'VBPc' in '${vbpName}'	Return From Keyword
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${trafficId}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL
	...	${threadFlow}	1000	1024	${threadNum}
	${trafficId1}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL
	...	700m	1000	1300	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI	CELL_BLER
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI	CELL_BLER
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)	ulDtxTbCnt
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId1}	UL
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectputDL}
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectputUL}
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	计算MTS的平均值_多模	${result}

UDP上行灌包测试_KPI_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=1	${expectput}=280
	[Documentation]	【功能说明】：
	...	特定功能关键字，修改配置，然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：configType：配置类型。255：基线配置。0-19：修改下行传输模式。20：BWP带宽20M，40：BWP带宽40M，60：BWP带宽60M，111：不修改配置。
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 255 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${trafficId1}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL
	...	700m	1000	1300	1
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	# 创建监控
	${duID}	获取物理小区的moid	${cellAlias}
	${enbAlias}	根据小区别名获取基站别名_多模	${cellAlias}
	${paras}	create dictionary	taskName=${cellAlias}_${time}	moType=NRCellDU	counters=C613610012&C613610006	monitorObjMoIds=${duID}
	${counters}	create list	C613610012	C613610006
	${taskId}	创建实时KPI任务_多模	${enbAlias}	${cellAlias}	NRCellDU	${counters}	${None}
	...	PlmnInfoDU
	Comment	${taskId}	创建实时KPI监控任务	${enbAlias}	${paras}
	sleep	120
	${filterDict1}	create dictionary	Cell DU ID=${duID}
	${filterDictList1}	create list	${filterDict1}
	${realKpiData1}	查询实时KPI监控数据	${enbAlias}	${taskId}	1	100	${filterDictList1}
	${value_list}	过滤实时KPI指定列数据	${realKpiData1}	8
	${valueUl}	计算实时KPI指定列平均值	${realKpiData1}	8
	${valueDl}	计算实时KPI指定列平均值	${realKpiData1}	9
	${result}	create dictionary	dlMACThroughput(Mbps)=${valueDl}	ulMACThroughput(Mbps)=${valueUl}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId1}	UL
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectput}
	${returnValue}	计算MTS的平均值_多模	${result}
	${taskIdList}	create list	${taskId}
	pm.挂起并删除实时KPI监控任务	${enbAlias}	${taskIdList}

UDP下行灌包测试_KPI_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=1	${expectput}=280
	[Return]	${returnValue}
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${trafficId}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL
	...	${threadFlow}	1000	1024	${threadNum}
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	# 创建监控
	${duID}	获取物理小区的moid	${cellAlias}
	${enbAlias}	根据小区别名获取基站别名_多模	${cellAlias}
	${paras}	create dictionary	taskName=${cellAlias}_${time}	moType=NRCellDU	counters=C613610012&C613610006	monitorObjMoIds=${duID}
	${counters}	create list	C613610012	C613610006
	Comment	${taskId}	创建实时KPI监控任务	${enbAlias}	${paras}
	${taskId}	创建实时KPI任务_多模	${enbAlias}	${cellAlias}	NRCellDU	${counters}	${None}
	...	PlmnInfoDU
	sleep	120
	${filterDict1}	create dictionary	Cell DU ID=${duID}
	${filterDictList1}	create list	${filterDict1}
	${realKpiData1}	查询实时KPI监控数据	${enbAlias}	${taskId}	1	100	${filterDictList1}
	${value_list}	过滤实时KPI指定列数据	${realKpiData1}	8
	${valueUl}	计算实时KPI指定列平均值	${realKpiData1}	8
	${valueDl}	计算实时KPI指定列平均值	${realKpiData1}	9
	${result}	create dictionary	dlMACThroughput(Mbps)=${valueDl}	ulMACThroughput(Mbps)=${valueUl}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectput}
	${returnValue}	计算MTS的平均值_多模	${result}
	${taskIdList}	create list	${taskId}
	pm.挂起并删除实时KPI监控任务	${enbAlias}	${taskIdList}

UDP上下行灌包测试_KPI_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=4	${expectputDL}=350	${expectputUL}=200
	[Return]	${returnValue}
	确认NR小区状态正常_多模	${cellAlias}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${trafficId}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	DL
	...	${threadFlow}	1000	1024	${threadNum}
	${trafficId1}	run keyword and continue on failure	开始UDP收灌包_多模	RANDOM	${pdnAlias}	${ueAlias}	UL
	...	700m	1000	1300	1
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	# 创建监控
	${duID}	获取物理小区的moid	${cellAlias}
	${enbAlias}	根据小区别名获取基站别名_多模	${cellAlias}
	${paras}	create dictionary	taskName=${cellAlias}_${time}	moType=NRCellDU	counters=C613610012&C613610006	monitorObjMoIds=${duID}
	${counters}	create list	C613610012	C613610006
	${taskId}	创建实时KPI任务_多模	${enbAlias}	${cellAlias}	NRCellDU	${counters}	${None}
	...	PlmnInfoDU
	sleep	120
	${filterDict1}	create dictionary	Cell DU ID=${duID}
	${filterDictList1}	create list	${filterDict1}
	${realKpiData1}	查询实时KPI监控数据	${enbAlias}	${taskId}	1	100	${filterDictList1}
	${value_list}	过滤实时KPI指定列数据	${realKpiData1}	8
	${valueUl}	计算实时KPI指定列平均值	${realKpiData1}	8
	${valueDl}	计算实时KPI指定列平均值	${realKpiData1}	9
	${result}	create dictionary	dlMACThroughput(Mbps)=${valueDl}	ulMACThroughput(Mbps)=${valueUl}
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包_多模	${trafficId1}	UL
	${testResultDl}	run keyword and ignore error	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectputDL}
	${testResultUl}	run keyword and ignore error	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectputUL}
	${returnValue}	计算MTS的平均值_多模	${result}
	${taskIdList}	create list	${taskId}
	run keyword and ignore error	pm.挂起并删除实时KPI监控任务	${enbAlias}	${taskIdList}
	${testResultDl}	evaluate	${testResultDl}[0]
	${testResultUl}	evaluate	${testResultUl}[0]
	${TEST_STATUS}	evaluate	'${testResultDl}' == 'PASS' and '${testResultUl}'== 'PASS'
	set global variable	${TEST_STATUS}

新网管UDP上下行灌包测试
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${threadFlow}=400m	${threadNum}=4	${expectputDL}=350	${expectputUL}=200
	[Documentation]	【功能说明】：
	...	特定功能关键字，修改配置，然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：configType：配置类型。255：基线配置。0-19：修改下行传输模式。20：BWP带宽20M，40：BWP带宽40M，60：BWP带宽60M，111：不修改配置。
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 255 400m 1 350
	...	【作者】：10124054
	[Return]	${returnValue}
	确认NR小区状态正常_多模	${cellAlias}
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_MAC_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB	CELL_AVG_NI
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	2
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	DL	${threadFlow}
	...	1000	1024	${threadNum}
	${trafficId1}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	UL	700m
	...	1000	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	5
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	60
	run keyword and ignore error	停止小区MTS任务_多模	${cellAlias}	${taskid}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包	${trafficId1}	UL
	run keyword and continue on failure	UE去附着	${ueAlias}
	${zipPath}	run keyword and continue on failure	导出小区MTS数据_多模	${cellAlias}	${taskid}
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_MAC_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_MAC_THROUGHPUT	CELL_AVG_NI
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlMACThroughput(Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulMACThroughput(Mbps)	niAvg(dBm)
	${result}	run keyword and continue on failure	解析MTS导出UE数据_多模	${cellAlias}	${ueAlias}	${zipPath}	${tableList}
	...	${elementList}
	run keyword and ignore error	删除小区MTS任务_多模	${cellAlias}	${taskid}	${False}
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	dlMACThroughput(Mbps)	${expectputDL}
	run keyword and continue on failure	判断多UE指标是否正常_多模	${result}	ulMACThroughput(Mbps)	${expectputUL}
	${returnValue}	计算MTS的平均值_多模	${result}

修改基站所有小区帧结构
	[Arguments]	${gnbAlias}=gnb	${frameType}=ms5	${TddConfig_frameType2Present}=0	${frameType1}=1;1;1;1;1;1;1;2;0;0
	[Documentation]	2.5ms单1D3U帧结构，frameType1要配：1;2;0;0;0
	...	2.5ms双周期帧结构，frameType1要配：1;1;1;2;0
	...	5ms单周期帧结构，frameType1要配：1;1;1;1;1;1;1;2;0;0
	@{cellList}	获取NR小区别名_多模	${gnbAlias}
	${attrDic}	create dictionary	TddConfig_dlULTransmissionPeriodicity1	${frameType}	TddConfig_frameType2Present	${TddConfig_frameType2Present}	TddConfig_frameType1
	...	${frameType1}
	FOR	${cell}	IN	@{cellList}
	模板修改NR小区参数_多模	${cell}	NRPhysicalCellDU	${attrDic}
	同步规划区数据_多模	${gnbAlias}

导出基站XML并备份
	[Arguments]	${gnbAlias}	${omcAlias}	${isOverWriteFile}=False	${dirName}=None
	[Documentation]	导出基站xml，并保存在D:/0基站配置备份 目录下，返回文件目录。如果该版本的xml存在，则不会覆盖。
	...	该文件用来在测试套结束时恢复环境用。
	[Return]	${xmlPath}
	${version}	查询基站运行版本号_多模	${gnbAlias}	SOFTWARE
	${meid}	查询环境设备属性值	${gnbAlias}	meId
	${xmlPath}	run keyword if	'${dirName}' != 'None'	set variable	D:/0基站配置备份/${dirName}/CfgBackup_${meid}_${version}.xml	ELSE	set variable
	...	D:/0基站配置备份/CfgBackup_${meid}_${version}.xml
	${xmlPathDir}	run keyword if	'${dirName}' != 'None'	set variable	D:/0基站配置备份/${dirName}	ELSE	set variable
	...	D:/0基站配置备份
	${isFile}	判断文件是否存在	${xmlPath}
	return from keyword if	('${isOverWriteFile}'=='False') & ('${isFile}' == 'True')	${xmlPath}
	${xml}	导出基站数据_多模	${gnbAlias}
	${xmlPath}	复制文件并重命名_多模	${xml}	${xmlPathDir}	CfgBackup_${meid}_${version}.xml	${isOverWriteFile}

接入成功率测试
	[Arguments]	${cellAlias}	${cpeAlias}	${totalNum}=30	${exceptSuccNum}=28
	[Documentation]	【功能说明】：
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试 400m 1 350
	...	【作者】：10124054
	[Return]	${sucess}
	${count}	Set Variable	${totalNum}
	${sucess}	Set Variable	0
	设置UE锁NR小区频点和pci_多模	${cpeAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	FOR	${i}	IN RANGE	${count}
	${status}	${result}	run keyword and ignore error	UE同步并接入NR小区成功_多模	${cpeAlias}	${cellAlias}
	${sucess}=	Run Keyword If	'${status}'=='PASS'	evaluate	${sucess}+1	ELSE	set variable
	...	${sucess}
	${rate}=	evaluate	float(${sucess})/${count}*100
	should be true	${sucess} >= ${exceptSuccNum}

使能1波束场景_多模
	[Arguments]	${cellAlias}	${isSynData}=True
	${attr}	create Dictionary	subBeamValid	true
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	2
	${attr}	create Dictionary	subBeamValid	false
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	1
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	3
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	4
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	5
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	6
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	7
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	8
	run keyword if	${isSynData}==True	同步规划区数据_多模	${GNODEB}

使能2波束场景_多模
	[Arguments]	${cellAlias}	${isSynData}=True
	${attr}	create Dictionary	subBeamValid	true
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	1
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	2
	${attr}	create Dictionary	subBeamValid	false
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	3
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	4
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	5
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	6
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	7
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	8
	run keyword if	${isSynData}==True	同步规划区数据_多模	${GNODEB}

使能4波束场景_多模
	[Arguments]	${gnbAlias}	${cellAlias}
	${attr}	create Dictionary	subBeamValid	true
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	1
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	2
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	3
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	4
	${attr}	create Dictionary	subBeamValid	false
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	5
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	6
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	7
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	8
	同步规划区数据_多模	${gnbAlias}

使能4SSB场景_多模
	[Arguments]	${gnbAlias}	${cellAlias}
	${attr}	create Dictionary	subBeamValid	true
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	1
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	2
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	5
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	6
	同步规划区数据_多模	${gnbAlias}

使能7波束场景_多模
	[Arguments]	${gnbAlias}	${cellAlias}
	${attr}	create Dictionary	subBeamValid	true
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	1
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	2
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	3
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	4
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	5
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	6
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	7
	${attr}	create Dictionary	subBeamValid	false
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	8
	同步规划区数据_多模	${gnbAlias}

使能8波束场景_多模
	[Arguments]	${gnbAlias}	${cellAlias}
	${attr}	create Dictionary	subBeamValid	true
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	1
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	2
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	3
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	4
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	5
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	6
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	7
	${attr}	create Dictionary	subBeamValid	false
	修改NR物理DU小区属性_多模	${cellAlias}	SubBeamInfo	${attr}	8
	同步规划区数据_多模	${gnbAlias}

测试结果邮件通知
	[Arguments]	${enbAlias}	${subject}	${envNum}	${scene}	${version}
	send_testcase_result_email_multi	${enbAlias}	${subject}	${envNum}	${scene}	${version}

NR小区流量数据入库
	[Arguments]	${gnbAlias}	${cellAlias}	${ueAlias}	${result}	${scene}	${testStatus}=PASS	${caseNo}=${None}	${caseName}=${None}	${otherInfo}=${None}
	${ueType}	查询环境设备属性值	${ueAlias}	ueType
	${refNRCarrier}	查询NR物理DU小区属性_多模	${cellAlias}	NRPhysicalCellDU	refNRCarrier
	${nrCarrierId}	evaluate	'${refNRCarrier}'.split('=')[-1]
	${duplexMode}	查询NR物理DU小区属性_多模	${cellAlias}	NRPhysicalCellDU	duplexMode
	${nrbandwidth}	查询小区节点信息	${cellAlias}	CarrierDL	nrbandwidth	ldn	NRRadioInfrastructure=1,NRCarrier=${nrCarrierId},CarrierDL=1
	${dlULTransmissionPeriodicity1}	查询NR物理DU小区属性_多模	${cellAlias}	TddConfig	dlULTransmissionPeriodicity1
	${frameType2Present}	查询NR物理DU小区属性_多模	${cellAlias}	TddConfig	frameType2Present
	${refBWP}	查询NR物理DU小区属性_多模	${cellAlias}	PhyResourceConfigforBWPDL	refBWP
	${bwpId}	evaluate	'${refBWP}'.split('=')[-1]
	${numberOfRBs}	查询小区节点信息	${cellAlias}	BWP	numberOfRBs	moId	${bwpId}
	${vswName}	查询单板信息_多模	${VSW}	name
	set to dictionary	${result}	CC=${vswName}	duplexMode=${duplexMode}	nrbandwidth=${nrbandwidth}	dlULTransmissionPeriodicity1=${dlULTransmissionPeriodicity1}	frameType2Present=${frameType2Present}
	...	numberOfRBs=${numberOfRBs}	result=${testStatus}	ueType=${ueType}
	${testInfo}	查询环境设备属性值	${gnbAlias}	testInfo
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${cellInfo}	evaluate	${testInfo}.get('${cellAlias}', {})
	set to dictionary	${cellInfo}	VBP=${vbpName}
	${result}	evaluate	dict(${result}.items() + ${cellInfo}.items())
	${otherInfo}	run keyword if	"${otherInfo}"=="${None}"	create dictionary	ELSE	set variable	${otherInfo}
	${result}	evaluate	dict(${result}.items() + ${otherInfo}.items())
	Run Keyword And Ignore Error	获取场景配置信息	${gnbAlias}
	run keyword and ignore error	测试数据入库_多模	${gnbAlias}	${result}	${scene}	${otherInfo}	${caseNo}
	...	${caseName}

NR小区AMC曲线数据入库
	[Arguments]	${gnbAlias}	${cellAlias}	${ueAlias}	${result}	${scene}	${testStatus}=PASS
	${ueType}	查询环境设备属性值	${ueAlias}	ueType
	${refNRCarrier}	查询NR物理DU小区属性_多模	${cellAlias}	NRPhysicalCellDU	refNRCarrier
	${nrCarrierId}	evaluate	'${refNRCarrier}'.split('=')[-1]
	${duplexMode}	查询NR物理DU小区属性_多模	${cellAlias}	NRPhysicalCellDU	duplexMode
	${nrbandwidth}	查询小区节点信息	${cellAlias}	CarrierDL	nrbandwidth	ldn	NRRadioInfrastructure=1,NRCarrier=${nrCarrierId},CarrierDL=1
	${dlULTransmissionPeriodicity1}	查询NR物理DU小区属性_多模	${cellAlias}	TddConfig	dlULTransmissionPeriodicity1
	${frameType2Present}	查询NR物理DU小区属性_多模	${cellAlias}	TddConfig	frameType2Present
	${refBWP}	查询NR物理DU小区属性_多模	${cellAlias}	PhyResourceConfigforBWPDL	refBWP
	${bwpId}	evaluate	'${refBWP}'.split('=')[-1]
	${numberOfRBs}	查询小区节点信息	${cellAlias}	BWP	numberOfRBs	moId	${bwpId}
	${paraDict}	create dictionary	duplexMode=${duplexMode}	nrbandwidth=${nrbandwidth}	dlULTransmissionPeriodicity1=${dlULTransmissionPeriodicity1}	frameType2Present=${frameType2Present}	numberOfRBs=${numberOfRBs}
	...	result=${testStatus}	ueType=${ueType}
	${paras}	Create List	DATA	Sheet1
	${paraDictTemp}	create dictionary
	${data}	解析mts数据并入库	${result}	${paraDictTemp}	${None}	${None}	handle_amc_mts_dl_ul_mvp_data
	...	${paras}
	${testInfo}	查询环境设备属性值	${gnbAlias}	testInfo
	${vbpName}	查询NR小区引用的基带板属性_多模	${cellAlias}	name
	${cellInfo}	evaluate	${testInfo}['${cellAlias}']
	set to dictionary	${cellInfo}	VBP=${vbpName}
	${paraDict}	evaluate	dict(${paraDict}.items() + ${data}.items() + ${cellInfo}.items())
	Run Keyword And Ignore Error	获取场景配置信息	${gnbAlias}
	run keyword and ignore error	测试数据入库_多模	${gnbAlias}	${testStatus}	${scene}	${paraDict}

优化上行功控参数_多模
	[Arguments]	${cellAlias}	${gnb}=gnb
	${version}	查询基站运行版本号_多模	${gnb}	SOFTWARE
	${result}	Evaluate	"5.45.10" \ in "${version}"
	${value}	run keyword if	${result}	set variable	29;37;26;35	ELSE	set variable
	...	29;37;26;35;21;21
	${value2}	run keyword if	${result}	set variable	28;36;25;33	ELSE	set variable
	...	28;36;25;33;20;20
	${value3}	run keyword if	${result}	set variable	22;29;19;27	ELSE	set variable
	...	22;29;19;27;15;15
	${attr}	create Dictionary	puschPowCtrlHighTargetSINR	${value}	puschPowCtrlMidTargetSINR	${value2}	puschPowCtrlLowTargetSINR
	...	${value3}
	修改NR物理DU小区属性_多模	${cellAlias}	PowerControlUL	${attr}

打开rsrp上报开关_多模
	[Arguments]	${cellAlias}
	${attr}	create Dictionary	csiRsEnable	true
	批量修改基站配置_多模	${cellAlias}	RSRPMeasureConfig	${attr}
	${attr}	create Dictionary	rsrpReportEnable	true
	修改NR物理DU小区属性_多模	${cellAlias}	PUCCHConfig	${attr}
	${attr}	create Dictionary	rsrpReportEnable	true
	修改NR物理DU小区属性_多模	${cellAlias}	PucchAlloc	${attr}
	${attr}	create Dictionary	reportQuantity	cri-rsrp
	修改NR物理DU小区属性_多模	${cellAlias}	RSRPMeasCfg	${attr}

UDP上下行灌包测试LTE_多模
	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${expectputDL}=350	${expectputUL}=200
	[Documentation]	【功能说明】：
	...	然后进行UE下行UDP灌包测试并判断极限流量
	...	【入参】：
	...	threadFlow：单线程流量
	...	threadNum：灌包线程数
	...	expectput：期望流量
	...	【返回】：
	...	【备注】：已实现
	...	
	...	【举例】：
	...	UDP下行灌包测试
	...	【作者】：10124054
	[Return]	${returnValue}
	确认EUtran小区状态正常_多模	${cellAlias}
	设置UE模式_多模	${ueAlias}	LTE
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${eventList}	create list	CELL_DL_BASE_INFO	CELL_UL_BASE_INFO
	sleep	5
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${ueAlias}	BI	400m
	...	9999	1024	4
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	10
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	40
	${tableListDl}	create list	CELL_DL_BASE_INFO	CELL_DL_BASE_INFO
	${elementListDl}	create list	PHYTput(Kbps)	Bler[CEU](%)
	${tableListUl}	create list	CELL_UL_BASE_INFO	CELL_UL_BASE_INFO
	${elementListUl}	create list	PHYTput(Kbps)	Bler[CEU](%)
	${resultDl}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableListDl}
	...	${elementListDl}
	${resultUl}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableListUl}
	...	${elementListUl}
	${dlPhyputValue}	获取UE指标_多模	${resultDl}	PHYTput(Kbps)
	${dlBler}	获取UE指标_多模	${resultDl}	Bler[CEU](%)
	${ulPhyputValue}	获取UE指标_多模	${resultUl}	PHYTput(Kbps)
	${ulBler}	获取UE指标_多模	${resultUl}	Bler[CEU](%)
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	BI
	run keyword and continue on failure	should be true	${dlPhyputValue} > ${expectputDL}
	run keyword and continue on failure	should be true	${ulPhyputValue} > ${expectputUL}
	run keyword and continue on failure	should be true	${dlBler} < 14	下行误码高
	run keyword and continue on failure	should be true	${ulBler} < 14	上行误码高
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}
	${returnValue}	create dictionary	PHYTputDL(Kbps)=${dlPhyputValue}	PHYTputUL(Kbps)=${ulPhyputValue}	dlBler=${dlBler}	ulBler=${ulBler}

AMC下行拉远拉近_白色可调_UDP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1
	[Documentation]	${caseID} ：用例编号
	...	${casename} ：用例名称
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	400*${flowRate}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	DL	${flow}m
	...	9999	1024	4
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	60
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}
	# 刘伟波
	Comment 刘伟波
	${maxAtt}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}*2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${value}*2.0) > 60
	sleep	6
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt2}*2.0-${value}*2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上行拉远拉近_白色可调_UDP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	700*${flowRate}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	UL	${flow}m
	...	9999	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}
	${maxAtt}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt}
	设置UE信号衰减	${ueAlias}	${${value}/2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${value}/2.0) > 30
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${maxAtt2}/2.0-${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

AMC上下行拉远拉近_白色可调_UDP
	[Arguments]	${caseID}	${caseName}	${gnbAlias}	${cellAlias}	${ueAlias}	${pdnAlias}	${vswAlias}	${maxAtt}=60	${flowRate}=1	${attRate}=1
	[Documentation]	${caseID} ：用例编号
	...	${casename} ：用例名称
	[Teardown]
	[Return]	${result}
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}
	确认NR小区状态正常_多模	${cellAlias}
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	UE去附着	${ueAlias}
	sleep	5
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}
	${versioninfo}	查询基站运行版本号_多模	${GNODEB}
	sleep	10
	${flow}	evaluate	400*${flowRate}
	${trafficId}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	DL	${flow}m
	...	9999	1024	4
	sleep	5
	${flow}	evaluate	700*${flowRate}
	${trafficId1}	开始UDP收灌包	RANDOM	${pdnAlias}	${ueAlias}	UL	${flow}m
	...	9999	1024	1
	从Huc容器里获取cpfUeId_多模	${vswAlias}	${ueAlias}
	sleep	10
	${eventList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_PDCCH_SCH_NUM	UE_PDSCH_PUSCH_BLER
	...	UE_PHY_THROUGHPUT	UE_PUSCH_SINR	UE_SCH_RB
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}
	sleep	15
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}
	sleep	10
	${rsrp}	获取UE的NR侧rsrp值_多模	${CPE}
	${maxAtt1}	evaluate	int(110+${rsrp})
	${maxAtt2}	set variable	0
	FOR	${value}	IN RANGE	0	${maxAtt1}
	设置UE信号衰减	${ueAlias}	${${attRate}*${value}/2.0}
	sleep	3
	${maxAtt2}	set variable	${value}
	exit for loop if	(${attRate}*${value}/2.0) > ${maxAtt}
	sleep	3
	FOR	${value}	IN RANGE	0	${maxAtt2}
	设置UE信号衰减	${ueAlias}	${${attRate}*${maxAtt2}/2.0-${attRate}*${value}/2.0}
	sleep	3
	设置UE信号衰减	${ueAlias}	0
	sleep	10
	${tableList}	create list	UE_RSRP_INFO	UE_CSI_CQI_PMI_INFO	UE_AVG_MCS	UE_AVG_MCS	UE_SCH_RB
	...	UE_PDSCH_PUSCH_BLER	UE_PDCCH_SCH_NUM	UE_PUSCH_SINR	UE_PUSCH_SINR	UE_PHY_THROUGHPUT	UE_SCH_RB	UE_PDSCH_PUSCH_BLER
	...	UE_PDCCH_SCH_NUM	UE_PHY_THROUGHPUT
	${elementList}	create list	maxRsrpIdx	ri	UeDlAvgMCS	UeUlAvgMCS	dlRb[0]
	...	pdschFail_bler(%)	pdcchDlSchNum	puschAvgSinr(dB)	puschAvgSinr[0](dB)	dlPhyThroughput[0](Mbps)	ulRb[0]	puschFail_bler(%)
	...	pdcchUlSchNum	ulPhyThroughput[0](Mbps)
	${result}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableList}
	...	${elementList}
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL
	run keyword and continue on failure	结束UDP收灌包	${trafficId1}	UL
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}

批量打开所有小区的rsrp上报开关_多模
	[Arguments]	${gnbAlias}
	${attr}	create Dictionary	csiRsEnable	true
	批量修改基站配置_多模	${gnbAlias}	RSRPMeasureConfig	${attr}
	${attr}	create Dictionary	rsrpReportEnable	true
	批量修改基站配置_多模	${gnbAlias}	PUCCHConfig	${attr}

SPA领域专用峰值流量测试及校验模板_多模
	[Arguments]	${gnbAlias}	${cellAlias}	${ume}	${pdn}	${ue}	${expUlSchNum}	${expDlSchNum}	${expUlRbNum}	${expDlRbNum}	${expUlRi}	${expDlRi}	${direction}	${bandwidth_ul}	${bandwidth_dl}	${isTNR}=True
	[Documentation]	${expUlSchNum}:上行满调的包数
	...	${expDlSchNum}:下行满调的包数
	...	${expUlRbNum}:上行满调的RB
	...	${expDlRbNum}:下行满调的RB
	...	${expUlRi}:上行流数RI
	...	${expDlRi}:下行流数RI
	...	${direction}:灌包方向、
	...	${bandwidth_ul}:上行灌包大小
	...	${bandwidth_dl}:下行灌包大小
	...	${isTNR}:是否是TNR（默认是True，如果是FNR填False）
	[Teardown]	Run Keywords	结束所有收灌包任务	${pdn}	${ue}	${direction}
	...	AND	停止小区跟踪任务	${subscriptionId}	${taskId}	${ume}
	...	AND	删除小区跟踪任务	${subscriptionId}	${taskId}	${ume}
	${DLdirection}	Run Keyword If	"${direction}"=="BI" or "${direction}"=="DL"	set variable	1	ELSE	set variable
	...	0
	${ULdirection}	Run Keyword If	"${direction}"=="BI" or "${direction}"=="UL"	set variable	1	ELSE	set variable
	...	0
	${counters_NRPhysicalDU}	create list	670055	670056	670131	670132
	${observerIdList}	create list	CELL_AVG_MCS	UE_PDSCH_SCH_NUM	UE_PUSCH_SCH_NUM	CELL_BLER	UE_PUSCH_SLOT_SCH_BLER_INFO
	...	UE_PDSCH_SLOT_SCH_BLER_INFO	UE_CSI_CQI_PMI_INFO
	设置UE锁NR小区频点和pci_多模	${ue}	${cellAlias}
	UE接入指定NR小区	${cellAlias}	${ue}
	run keyword if	${ULdirection}==1	开始UDP收灌包	RANDOM	${pdn}	${ue}	UL
	...	${bandwidth_ul}
	run keyword if	${DLdirection}==1	开始UDP收灌包	RANDOM	${pdn}	${ue}	DL
	...	${bandwidth_dl}
	${taskId_kpi}	创建NR小区KPI监控任务	${gnbAlias}	${cellAlias}	${counters_NRPhysicalDU}
	${subscriptionId}	${taskId}	创建NR小区MTS跟踪任务	${gnbAlias}	${cellAlias}	${observerIdList}
	set global variable	${taskId_kpi}
	set global variable	${subscriptionId}
	set global variable	${taskId}
	sleep	60
	${dlRi}	${ulRi}	${dl_rb}	${ul_rb}	查询NR小区指定列KPI数据_多模	${gnbAlias}	${taskId_kpi}
	...	${expUlRbNum}	${expDlRbNum}	${ULdirection}	${DLdirection}	${expDlRi}	${expUlRi}
	run keyword and continue on failure	获取MTS数据并校验_多模	${cellAlias}	${ume}	${taskId}	${expUlSchNum}	${expDlSchNum}
	...	${ULdirection}	${DLdirection}	${isTNR}
	run keyword and ignore error	结束所有收灌包任务	${pdn}	${ue}	${direction}
	run keyword and ignore error	UE去附着并执行成功	${ue}
	#MCS降到20错包需归零
	${NRPhysicalCellDUID}	获取小区的NRPhysicalCellDUid	${cellAlias}
	run keyword if	${ULdirection}==1	修改AmcUL中的特定参数	${cellAlias}	maxMCSUl	20	1
	...	${NRPhysicalCellDUID}
	run keyword if	${DLdirection}==1	修改AmcDL中的特定参数	${cellAlias}	maxMCSDl	20	1
	...	${NRPhysicalCellDUID}
	检查激活配置	${gnbAlias}
	sleep	10
	UE接入指定NR小区	${cellAlias}	${ue}
	run keyword if	${ULdirection}==1	开始UDP收灌包	RANDOM	${pdn}	${ue}	UL
	...	${bandwidth_ul}
	run keyword if	${DLdirection}==1	开始UDP收灌包	RANDOM	${pdn}	${ue}	DL
	...	${bandwidth_dl}
	${taskId_kpi}	创建NR小区KPI监控任务	${gnbAlias}	${cellAlias}	${counters_NRPhysicalDU}
	${subscriptionId}	${taskId}	创建NR小区MTS跟踪任务	${gnbAlias}	${cellAlias}	${observerIdList}
	sleep	90
	${dlRi}	${ulRi}	${dl_rb}	${ul_rb}	查询NR小区指定列KPI数据_多模	${gnbAlias}	${taskId_kpi}
	...	${expUlRbNum}	${expDlRbNum}	${ULdirection}	${DLdirection}	${expDlRi}	${expUlRi}
	${result_Mts}	获取完整MTS数据	${taskId}	60
	${cellId}	获取小区CellId	${cellAlias}
	${ulbler}	获取MTS任务的字段数据平均值	${result_Mts}	CELL_BLER	ulbler(%)
	${dlbler}	获取MTS任务的字段数据平均值	${result_Mts}	CELL_BLER	dlbler(%)
	${puschSchNum}	获取MTS数据中上下行调度包数	${result_Mts}	UE_PUSCH_SCH_NUM	puschSchNum[0][0]	puschSchNum[1][0]	puschSchNum[2][0]
	...	puschSchNum[3][0]	puschSchNum[4][0]	${cellId}
	${pdschSchNum}	获取MTS数据中上下行调度包数	${result_Mts}	UE_PDSCH_SCH_NUM	pdschSchNum[0][0]	pdschSchNum[1][0]	pdschSchNum[2][0]
	...	pdschSchNum[3][0]	pdschSchNum[4][0]	${cellId}
	${ulMcs}	获取MTS任务的字段数据平均值	${result_Mts}	CELL_AVG_MCS	CellUlAvgMCS
	${dlMcs}	获取MTS任务的字段数据平均值	${result_Mts}	CELL_AVG_MCS	CellDlAvgMCS
	${puschSchNum}	evaluate	int(${puschSchNum})
	${pdschSchNum}	evaluate	int(${pdschSchNum})
	${ulMcs}	evaluate	int(${ulMcs})
	${dlMcs}	evaluate	int(${dlMcs})
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	${ulbler}<=0.1
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	${dlbler}<=0.1
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	20>=${ulMcs}>=19
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	20>=${dlMcs}>=19
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	${puschSchNum}>=(${expUlSchNum}-1)
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	${pdschSchNum}>=(${expDlSchNum}-1)
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	${ul_rb}>=(${expUlRbNum}-1)
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	${dl_rb}>=(${expDlRbNum}-1)
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	${ulRi}==${expUlRi}
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	${dlRi}==${expDlRi}

查询NR小区指定列KPI数据_多模
	[Arguments]	${gnbAlias}	${taskId_kpi}	${expUlRbNum}	${expDlRbNum}	${ULdirection}	${DLdirection}	${expDlRi}	${expUlRi}
	[Teardown]	挂起并删除实时KPI任务	${taskId_kpi}
	[Return]	${dlRi}	${ulRi}	${dl_rb}	${ul_rb}
	${KpiData_DU}	查询实时KPI监控数据	${gnbAlias}	${taskId_kpi}	1	10
	${index_dl_rb}	通过列名查找所在文件列的索引	${gnbAlias}	${taskId_kpi}	下行PDSCH信道PRB占用平均数
	${index_ul_rb}	通过列名查找所在文件列的索引	${gnbAlias}	${taskId_kpi}	上行PUSCH信道PRB占用平均数
	${index_dl_ri}	通过列名查找所在文件列的索引	${gnbAlias}	${taskId_kpi}	基于PRB的下行SU用户平均RI
	${index_ul_ri}	通过列名查找所在文件列的索引	${gnbAlias}	${taskId_kpi}	基于PRB的上行SU用户平均RI
	${dl_rb}	计算实时KPI指定列平均值	${KpiData_DU}	${index_dl_rb}
	${ul_rb}	计算实时KPI指定列平均值	${KpiData_DU}	${index_ul_rb}
	${dl_ri}	计算实时KPI指定列平均值	${KpiData_DU}	${index_dl_ri}
	${ul_ri}	计算实时KPI指定列平均值	${KpiData_DU}	${index_ul_ri}
	${dlRb}	evaluate	${dl_rb}[0]
	${ulRb}	evaluate	${ul_rb}[0]
	${dlRi}	evaluate	${dl_ri}[0]
	${ulRi}	evaluate	${ul_ri}[0]
	${dl_rb}	evaluate	int(${dlRb})
	${ul_rb}	evaluate	int(${ulRb})

获取MTS数据并校验_多模
	[Arguments]	${cellAlias}	${ume}	${taskId}	${expUlSchNum}	${expDlSchNum}	${ULdirection}	${DLdirection}	${isTNR}=True
	[Teardown]	Run keywords	停止小区跟踪任务	${subscriptionId}	${taskId}	${ume}
	...	AND	删除小区跟踪任务	${subscriptionId}	${taskId}	${ume}
	${result}	获取完整MTS数据	${taskId}	60
	${cellId}	获取小区CellId	${cellAlias}
	log	${cellId}
	${puschSchNum}	获取MTS数据中上下行调度包数	${result}	UE_PUSCH_SCH_NUM	puschSchNum[0][0]	puschSchNum[1][0]	puschSchNum[2][0]
	...	puschSchNum[3][0]	puschSchNum[4][0]	${cellId}
	${pdschSchNum}	获取MTS数据中上下行调度包数	${result}	UE_PDSCH_SCH_NUM	pdschSchNum[0][0]	pdschSchNum[1][0]	pdschSchNum[2][0]
	...	pdschSchNum[3][0]	pdschSchNum[4][0]	${cellId}
	${ulbler}	获取MTS任务的字段数据平均值	${result}	CELL_BLER	ulbler(%)
	${dlbler}	获取MTS任务的字段数据平均值	${result}	CELL_BLER	dlbler(%)
	${ulMcs}	获取MTS任务的字段数据平均值	${result}	CELL_AVG_MCS	CellUlAvgMCS
	${dlMcs}	获取MTS任务的字段数据平均值	${result}	CELL_AVG_MCS	CellDlAvgMCS
	#满调度
	${puschSchNum}	evaluate	int(${puschSchNum})
	${pdschSchNum}	evaluate	int(${pdschSchNum})
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	${puschSchNum}>=(${expUlSchNum}-1)
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	${pdschSchNum}>=(${expDlSchNum}-1)
	#bler大于5%或者MCS小于26需要校验无固定slot错包
	${ul}	run keyword if	${ULdirection}==0	set variable	1	ELSE IF	${ulbler}<5 and ${ulMcs}>=26
	...	set variable	1	ELSE	set variable	0
	${dl}	run keyword if	${DLdirection}==0	set variable	1	ELSE IF	${dlbler}<5 and ${dlMcs}>=26
	...	set variable	1	ELSE	set variable	0
	#无固定Slot错包，一定时间内Bler无突变
	run keyword and continue on failure	run keyword if	'${isTNR}'=='True' and ${ul}*${dl}==0	获取MTS数据中每个slot的bler并校验_TNR_多模	${result}	${cellId}	${ULdirection}
	...	${DLdirection}	${expUlSchNum}	${expDlSchNum}	${ulbler}	${dlbler}	ELSE IF	'${isTNR}'=='false' and ${ul}*${dl}==0
	...	获取MTS数据中每个slot的bler并校验_FNR	${result}	${cellId}	${ULdirection}	${DLdirection}	${expUlSchNum}	${expDlSchNum}
	...	${ulbler}	${dlbler}

获取MTS数据中每个slot的bler并校验_FNR_多模
	[Arguments]	${result}	${cellId}	${ULdirection}	${DLdirection}	${expUlSchNum}	${expDlSchNum}	${ulbler}	${dlbler}
	#avg	#max	#min
	${ulBler[0]}	${max_ulBler[0]}	${min_ulBler[0]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[0].bler
	...	${cellId}
	${ulBler[1]}	${max_ulBler[1]}	${min_ulBler[1]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[1].bler
	...	${cellId}
	${ulBler[2]}	${max_ulBler[2]}	${min_ulBler[2]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[2].bler
	...	${cellId}
	${ulBler[3]}	${max_ulBler[3]}	${min_ulBler[3]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[3].bler
	...	${cellId}
	${ulBler[4]}	${max_ulBler[4]}	${min_ulBler[4]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[4].bler
	...	${cellId}
	${ulBler[5]}	${max_ulBler[5]}	${min_ulBler[5]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[5].bler
	...	${cellId}
	${ulBler[6]}	${max_ulBler[6]}	${min_ulBler[6]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[6].bler
	...	${cellId}
	${ulBler[7]}	${max_ulBler[7]}	${min_ulBler[7]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[7].bler
	...	${cellId}
	${ulBler[8]}	${max_ulBler[8]}	${min_ulBler[8]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[8].bler
	...	${cellId}
	${ulBler[9]}	${max_ulBler[9]}	${min_ulBler[9]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[9].bler
	...	${cellId}
	${dlBler[0]}	${max_dlBler[0]}	${min_dlBler[0]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[0].bler
	...	${cellId}
	${dlBler[1]}	${max_dlBler[1]}	${min_dlBler[1]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[1].bler
	...	${cellId}
	${dlBler[2]}	${max_dlBler[2]}	${min_dlBler[2]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[2].bler
	...	${cellId}
	${dlBler[3]}	${max_dlBler[3]}	${min_dlBler[3]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[3].bler
	...	${cellId}
	${dlBler[4]}	${max_dlBler[4]}	${min_dlBler[4]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[4].bler
	...	${cellId}
	${dlBler[5]}	${max_dlBler[5]}	${min_dlBler[5]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[5].bler
	...	${cellId}
	${dlBler[6]}	${max_dlBler[6]}	${min_dlBler[6]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[6].bler
	...	${cellId}
	${dlBler[7]}	${max_dlBler[7]}	${min_dlBler[7]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[7].bler
	...	${cellId}
	${dlBler[8]}	${max_dlBler[8]}	${min_dlBler[8]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[8].bler
	...	${cellId}
	${dlBler[9]}	${max_dlBler[9]}	${min_dlBler[9]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[9].bler
	...	${cellId}
	#无固定Slot错包，一定时间内Bler无突变
	FOR	${i}	IN RANGE	0	10
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	100*${dlBler[${i}]}<=${dlbler}*${expDlSchNum}*0.2
	run keyword and continue on failure	run keyword if	${DLdirection}==1 and ${dlBler[${i}]}>10	should be true	${max_dlBler[${i}]}<=${min_dlBler[${i}]}*2
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	100*${ulBler[${i}]}<=${ulbler}*${expUlSchNum}*0.2
	run keyword and continue on failure	run keyword if	${ULdirection}==1 and ${ulBler[${i}]}>10	should be true	${max_ulBler[${i}]}<=${min_ulBler[${i}]}*2

获取MTS数据中每个slot的bler并校验_TNR_多模
	[Arguments]	${result}	${cellId}	${ULdirection}	${DLdirection}	${expUlSchNum}	${expDlSchNum}	${ulbler}	${dlbler}
	#avg	#max	#min
	${ulBler[0]}	${max_ulBler[0]}	${min_ulBler[0]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[0].bler
	...	${cellId}
	${ulBler[1]}	${max_ulBler[1]}	${min_ulBler[1]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[1].bler
	...	${cellId}
	${ulBler[2]}	${max_ulBler[2]}	${min_ulBler[2]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[2].bler
	...	${cellId}
	${ulBler[3]}	${max_ulBler[3]}	${min_ulBler[3]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[3].bler
	...	${cellId}
	${ulBler[4]}	${max_ulBler[4]}	${min_ulBler[4]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[4].bler
	...	${cellId}
	${ulBler[5]}	${max_ulBler[5]}	${min_ulBler[5]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[5].bler
	...	${cellId}
	${ulBler[6]}	${max_ulBler[6]}	${min_ulBler[6]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[6].bler
	...	${cellId}
	${ulBler[7]}	${max_ulBler[7]}	${min_ulBler[7]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[7].bler
	...	${cellId}
	${ulBler[8]}	${max_ulBler[8]}	${min_ulBler[8]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[8].bler
	...	${cellId}
	${ulBler[9]}	${max_ulBler[9]}	${min_ulBler[9]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[9].bler
	...	${cellId}
	${ulBler[10]}	${max_ulBler[10]}	${min_ulBler[10]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[10].bler
	...	${cellId}
	${ulBler[11]}	${max_ulBler[11]}	${min_ulBler[11]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[11].bler
	...	${cellId}
	${ulBler[12]}	${max_ulBler[12]}	${min_ulBler[12]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[12].bler
	...	${cellId}
	${ulBler[13]}	${max_ulBler[13]}	${min_ulBler[13]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[13].bler
	...	${cellId}
	${ulBler[14]}	${max_ulBler[14]}	${min_ulBler[14]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[14].bler
	...	${cellId}
	${ulBler[15]}	${max_ulBler[15]}	${min_ulBler[15]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[15].bler
	...	${cellId}
	${ulBler[16]}	${max_ulBler[16]}	${min_ulBler[16]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[16].bler
	...	${cellId}
	${ulBler[17]}	${max_ulBler[17]}	${min_ulBler[17]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[17].bler
	...	${cellId}
	${ulBler[18]}	${max_ulBler[18]}	${min_ulBler[18]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[18].bler
	...	${cellId}
	${ulBler[19]}	${max_ulBler[19]}	${min_ulBler[19]}	获取MTS字段数据平均值和最值	${result}	UE_PUSCH_SLOT_SCH_BLER_INFO	slotPuschSchBlerInfo[19].bler
	...	${cellId}
	${dlBler[0]}	${max_dlBler[0]}	${min_dlBler[0]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[0].bler
	...	${cellId}
	${dlBler[1]}	${max_dlBler[1]}	${min_dlBler[1]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[1].bler
	...	${cellId}
	${dlBler[2]}	${max_dlBler[2]}	${min_dlBler[2]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[2].bler
	...	${cellId}
	${dlBler[3]}	${max_dlBler[3]}	${min_dlBler[3]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[3].bler
	...	${cellId}
	${dlBler[4]}	${max_dlBler[4]}	${min_dlBler[4]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[4].bler
	...	${cellId}
	${dlBler[5]}	${max_dlBler[5]}	${min_dlBler[5]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[5].bler
	...	${cellId}
	${dlBler[6]}	${max_dlBler[6]}	${min_dlBler[6]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[6].bler
	...	${cellId}
	${dlBler[7]}	${max_dlBler[7]}	${min_dlBler[7]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[7].bler
	...	${cellId}
	${dlBler[8]}	${max_dlBler[8]}	${min_dlBler[8]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[8].bler
	...	${cellId}
	${dlBler[9]}	${max_dlBler[9]}	${min_dlBler[9]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[9].bler
	...	${cellId}
	${dlBler[10]}	${max_dlBler[10]}	${min_dlBler[10]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[10].bler
	...	${cellId}
	${dlBler[11]}	${max_dlBler[11]}	${min_dlBler[11]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[11].bler
	...	${cellId}
	${dlBler[12]}	${max_dlBler[12]}	${min_dlBler[12]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[12].bler
	...	${cellId}
	${dlBler[13]}	${max_dlBler[13]}	${min_dlBler[13]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[13].bler
	...	${cellId}
	${dlBler[14]}	${max_dlBler[14]}	${min_dlBler[14]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[14].bler
	...	${cellId}
	${dlBler[15]}	${max_dlBler[15]}	${min_dlBler[15]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[15].bler
	...	${cellId}
	${dlBler[16]}	${max_dlBler[16]}	${min_dlBler[16]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[16].bler
	...	${cellId}
	${dlBler[17]}	${max_dlBler[17]}	${min_dlBler[17]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[17].bler
	...	${cellId}
	${dlBler[18]}	${max_dlBler[18]}	${min_dlBler[18]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[18].bler
	...	${cellId}
	${dlBler[19]}	${max_dlBler[19]}	${min_dlBler[19]}	获取MTS字段数据平均值和最值	${result}	UE_PDSCH_SLOT_SCH_BLER_INFO	slotPdschSchBlerInfo[19].bler
	...	${cellId}
	#无固定Slot错包，一定时间内Bler无突变
	FOR	${i}	IN RANGE	0	20
	run keyword and continue on failure	run keyword if	${DLdirection}==1	should be true	100*${dlBler[${i}]}<=${dlbler}*${expDlSchNum}*0.2
	run keyword and continue on failure	run keyword if	${DLdirection}==1 and ${dlBler[${i}]}>10	should be true	${max_dlBler[${i}]}<=${min_dlBler[${i}]}*2
	run keyword and continue on failure	run keyword if	${ULdirection}==1	should be true	100*${ulBler[${i}]}<=${ulbler}*${expUlSchNum}*0.2
	run keyword and continue on failure	run keyword if	${ULdirection}==1 and ${ulBler[${i}]}>10	should be true	${max_ulBler[${i}]}<=${min_ulBler[${i}]}*2

导出SDR基站配置并备份
	[Arguments]	${gnbAlias}	${omcAlias}	${isOverWriteFile}=False	${dirName}=None
	[Documentation]	导出基站xml，并保存在D:/0基站配置备份 目录下，返回文件目录。如果该版本的xml存在，则不会覆盖。
	...	该文件用来在测试套结束时恢复环境用。
	[Return]	${xmlPath}
	${version}	查询基站运行版本号_SDR_多模	${gnbAlias}
	${meid}	查询环境设备属性值	${gnbAlias}	meId
	${xmlPath}	run keyword if	'${dirName}' != 'None'	set variable	D:/0基站配置备份/${dirName}/SDR_CfgBackup_${meid}_${version}.zip	ELSE	set variable
	...	D:/0基站配置备份/SDR_CfgBackup_${meid}_${version}.zip
	${xmlPathDir}	run keyword if	'${dirName}' != 'None'	set variable	D:/0基站配置备份/${dirName}	ELSE	set variable
	...	D:/0基站配置备份
	${isFile}	判断文件是否存在	${xmlPath}
	return from keyword if	('${isOverWriteFile}'=='False') & ('${isFile}' == 'True')	${xmlPath}
	${xml}	备份基站数据_SDR_多模	${gnbAlias}
	${xmlPath}	复制文件并重命名_多模	${xml}	${xmlPathDir}	SDR_CfgBackup_${meid}_${version}.zip	${isOverWriteFile}

验证NR小区业务_多模
	[Arguments]	${ueAlias}	${cellMoid}	${dlThresh}	${ulThresh}	${caseNo}=${None}	${caseName}=${None}	${rruName}=${None}
	[Teardown]
	创建UE对象	${ueAlias}
	创建PDN	${PDN}
	${filterDic}	create dictionary	moId	${cellMoid}
	@{cellList}	获取NR小区别名_多模	${ENODEB}	${filterDic}
	${cell1}	set variable	@{cellList}[0]
	设置UE模式_多模	${ueAlias}	SA
	sleep	60
	${result}	UDP上下行灌包测试	${cell1}	${ueAlias}	${PDN}	${VSW}	400m
	...	4	${dlThresh}	${ulThresh}
	should be true	${TEST_STATUS}
	删除PDN	${PDN}
	删除UE对象	${CPE}

验证LTE小区业务_多模
	[Arguments]	${ueAlias}	${cellMoid}	${dlThresh}=1000	${ulThresh}=100	${cellType}=FDD
	${filterDic}	create dictionary	moId	${cellMoid}
	@{cellListFdd}	获取FDD小区别名_多模	${ENODEB}	${filterDic}
	@{cellListTdd}	获取TDD小区别名_多模	${ENODEB}	${filterDic}
	${cellList}	run keyword if	'${cellType}'=='FDD'	set variable	${cellListFdd}	ELSE	set variable
	...	${cellListTdd}
	return from keyword if	len(${cellList}) == 0
	${cell1}	set variable	@{cellList}[0]
	创建UE对象	${ueAlias}
	设置UE模式_多模	${ueAlias}	LTE
	sleep	3
	${result}	run keyword and continue on failure	UDP上下行灌包测试LTE_多模	${cell1}	${ueAlias}	${PDN}	${VSW}
	...	${dlThresh}	${ulThresh}
	删除UE对象	${ueAlias}

确认PRRU的PA通道状态
	[Arguments]	${prruAlias}	${channel}=1,2,3,4	${value}=Open
	[Documentation]	确认PRRU的PA通道状态 11-instance 1,2,3,4 Open 确认PRRU的PA通道状态 11-instance 5,6,7,8 Close
	${rlt}	PRRU诊断测试_多模	${prruAlias}
	${result}	set variable	${rlt['pa']}
	${channelList}	evaluate	'${channel}'.split(',')
	FOR	${i}	IN	@{channelList}
	${status}	evaluate	${result}[${i}-1][3]
	should contain	${status}	${value}

获取并校验前一刻钟kpi是否正常
	[Arguments]	${enbAlias}	${measureType}	${moIdList}	${expectStr}	${valueIndex}	${keyIndex}	${keyIndex2}=${None}
	[Documentation]	请事先在网管上创建性能测量任务。
	...	
	...	参数：
	...	1、${enbAlias}：基站别名
	...	2、${measureType}：测量类型，如：AauChannel，AauCarrier，RruPwr，在文件：testlib5g.domain.model_multi.im.kpi.MeasureTask里定义。
	...	3、${moIdList}：导出的kpi记录的id标识，比如小区id，rru的机架号，由后边的 ${keyIndex}和${keyIndex2}决定。
	...	4、${expectStr}：期望的校验表达式，如：<1x<10，x<40等。使用x作为通配符。
	...	5、${valueIndex}：kpi记录中要取的值的列号，即要取第几列的值。
	...	6、${keyIndex}：用来过滤的列的列号。这列一般是小区id，机架id，槽位id等。
	...	7、${keyIndex2}：用来过滤的列的列号。一般对于PRRU的通道号，辅助过滤。
	...	
	...	使用举例：
	...	${moIdList} create list 11_1 11_2 11_3 11_4
	...	获取并校验前一刻钟kpi是否正常 ${GNODEB} AauChannel ${moIdList} 10<x<60 23 10 14
	${measureIdList}	查询基站已有测量任务_多模	${enbAlias}
	同步基站测量任务_多模	${GNODEB}	${measureIdList}
	${endTime}	获取上一刻钟日期时间
	${time}	获取指定日期时间前的日期时间	${endTime}	0	15
	FOR	${i}	IN RANGE	15
	${kpi}	查询测量数据_多模	${enbAlias}	${measureType}	${None}	1	900
	${kpi}	查询指定时间内的测量数据_多模	${kpi}	${time}	${endTime}
	Run Keyword If	len(${kpi}) > 1	exit for loop
	sleep	60
	${kpiRlt}	将测量数据转换成字典_多模	${kpi}	${valueIndex}	${keyIndex}	${keyIndex2}
	FOR	${moId}	IN	@{moIdList}
	${rlt}	evaluate	float(((${kpiRlt}.get('${moId}'))[0]).replace(',',''))
	${result}	evaluate	eval('${expectStr}'.replace('x', '${rlt}'))
	should be true	${result}	KPI校验失败:${rlt}

打开所有小区rsrp上报开关_多模
	[Arguments]	${enbAlias}
	${attr}	create Dictionary	csiRsEnable	true
	批量修改基站配置_多模	${enbAlias}	RSRPMeasureConfig	${attr}	${False}
	${attr}	create Dictionary	rsrpReportEnable	true
	批量修改基站配置_多模	${enbAlias}	PUCCHConfig	${attr}	${False}
	${attr}	create Dictionary	rsrpReportEnable	true
	批量修改基站配置_多模	${enbAlias}	PucchAlloc	${attr}	${False}
	${attr}	create Dictionary	reportQuantity	cri-rsrp
	批量修改基站配置_多模	${enbAlias}	RSRPMeasCfg	${attr}

使用开站模板文件开站_多模
	[Arguments]	${enodebAlias}	${filePath}
	[Documentation]	【功能说明】：导入开站模板文件，修改模板文件版本号，完成数据制作和带数据开通全流程。
	...	【入参】：
	...	${gnodeb}：基站别名
	...	${filePath}：开站模板文件路径
	...	【返回】：
	...	【备注】：40min
	...	【举例】：
	...	【作者】：曲圣越 10184430
	${versionSoftware}	查询基站运行版本号_多模	${enodebAlias}	software
	${versionColdpatch}	查询基站运行版本号_多模	${enodebAlias}	coldpatch
	${tarNameSoftware}	查询TAR包名称_多模	${enodebAlias}	${versionSoftware}
	${tarNameColdpatch}	查询TAR包名称_多模	${enodebAlias}	${versionColdpatch}
	Comment	${tarNameSoftware}	run keyword if	'${tarNameSoftware}'==''	set variable	UNI_${version}.tar	ELSE	set variable	${tarNameSoftware}
	Comment	${tarNameColdpatch}	run keyword if	'${tarNameColdpatch}'==''	set variable	UNI_${version}.tar	ELSE	set variable	${tarNameColdpatch}
	写入XLSX单元格	${filePath}	site	${versionSoftware}	7	7
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarNameSoftware}	${dataPlanJobName}
	sleep	10min
	# 升级冷补丁
	run keyword if	'${versionColdpatch}' != 'None'	升级TAR包_多模	${enodebAlias}	${tarNameColdpatch}
	run keyword if	'${versionColdpatch}' != 'None'	sleep	10min
	# 开站后校验
	${versionSoftware_2}	查询基站运行版本号_多模	${enodebAlias}	software
	${versionColdpatch_2}	查询基站运行版本号_多模	${enodebAlias}	coldpatch
	should be true	'${versionSoftware}' == '${versionSoftware_2}'	开站后软件包版本不同
	should be true	'${versionColdpatch}' == '${versionColdpatch_2}'	开站后冷补丁包版本不同

UDP上下行灌包测试_KPI_多模 - copy1
	[Arguments]	${enbAlias}	${cellAlias}
	[Return]	${value}
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	# 创建监控
	${meId}	获取网元ID	${enbAlias}
	${paras}	create dictionary	taskName=${meId}_${time}	moType=NRCellDU	counters=C613610012	monitorObjMoIds=1
	${taskId}	创建实时KPI监控任务	${enbAlias}	${paras}
	sleep	120
	# 查询结果
	${filterDict1}	create dictionary	Cell DU ID=${Cell_DU_ID}
	${filterDictList1}	create list	${filterDict1}
	${realKpiData1}	查询实时KPI监控数据	${enbAlias}	${taskId}	1	100	${filterDictList1}
	# 计算结果平稳性
	${value_list}	过滤实时KPI指定列数据	${realKpiData1}	8
	Comment	${ulTputValMin}	求列表最小值	${value_list}
	Comment	${ulTputValMax}	求列表最大值	${value_list}
	Comment	run keyword and ignore error	should be true	${ulTputValMax}-${ulTputValMin}<20
	Comment	流量平稳校验	${value_list}	0.8
	# 计算结果平均值
	${value}	计算实时KPI指定列平均值	${realKpiData1}	8
	${value}	set variable	${value[0]}
	${taskIdList}	create list	${taskId}
	# 校验常规指标
	Comment	${value_UL_bler}	计算实时KPI指定列平均值	${realKpiData1}	9
	Comment	${value_UL_DTX}	计算实时KPI指定列平均值	${realKpiData1}	10
	Comment	${value_DL_DTX}	计算实时KPI指定列平均值	${realKpiData1}	11
	Comment	should be true	${value_UL_DTX}==0
	Comment	should be true	${value_DL_DTX}<=10
	Comment	should be true	${value_UL_bler}<15
	# 结束
	pm.挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}


