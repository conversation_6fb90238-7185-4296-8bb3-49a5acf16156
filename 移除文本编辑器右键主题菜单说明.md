# 移除文本编辑器右键主题菜单说明

## 修改目的

根据用户需求，移除文本编辑器右键菜单中的主题选项，保持主题切换功能只在以下两个位置：
1. **菜单栏** → 样式 → 主题选择
2. **偏好设置** → Theme → 主题选择

## 修改内容

### 1. 简化右键菜单 (`_show_context_menu` 方法)

#### 修改前
```python
def _show_context_menu(self, position):
    """显示右键菜单"""
    try:
        # 创建菜单
        menu = QMenu(self)

        # 添加标准编辑菜单选项
        self._add_standard_menu_actions(menu)

        # 添加分隔符
        menu.addSeparator()

        # 添加主题选项
        theme_menu = menu.addMenu("主题")

        # 白色背景选项
        white_action = QAction("白色背景", self)
        white_action.setCheckable(True)
        white_action.setChecked(self._current_theme == "white")
        white_action.triggered.connect(lambda: self._set_theme("white"))
        theme_menu.addAction(white_action)

        # 黑色背景选项
        dark_action = QAction("黑色背景", self)
        dark_action.setCheckable(True)
        dark_action.setChecked(self._current_theme == "dark")
        dark_action.triggered.connect(lambda: self._set_theme("dark"))
        theme_menu.addAction(dark_action)

        # 显示菜单
        menu.exec_(self.mapToGlobal(position))

    except Exception as e:
        print(f"显示右键菜单时出错: {e}")
```

#### 修改后
```python
def _show_context_menu(self, position):
    """显示右键菜单"""
    try:
        # 创建菜单
        menu = QMenu(self)

        # 添加标准编辑菜单选项
        self._add_standard_menu_actions(menu)

        # 显示菜单
        menu.exec_(self.mapToGlobal(position))

    except Exception as e:
        print(f"显示右键菜单时出错: {e}")
```

### 2. 更新 `_set_theme` 方法注释

将方法注释更新为明确说明这是内部方法，由主题管理器调用：

```python
def _set_theme(self, theme):
    """设置主题（内部方法，由主题管理器调用）"""
    try:
        if theme == self._current_theme:
            return

        self._current_theme = theme

        if theme == "white":
            self._apply_white_theme()
        elif theme == "dark":
            self._apply_dark_theme()

        print(f"文本编辑器已切换到{theme}主题")

    except Exception as e:
        print(f"设置主题时出错: {e}")
```

## 修改效果

### 1. 右键菜单简化

现在文本编辑器的右键菜单只包含标准编辑功能：
- ✅ 撤销
- ✅ 重做
- ✅ 剪切
- ✅ 复制
- ✅ 粘贴
- ✅ 全选
- ❌ ~~主题选项~~（已移除）

### 2. 主题切换方式

用户现在只能通过以下两种方式切换主题：

#### 方式一：菜单栏切换
1. 点击菜单栏中的"样式"菜单
2. 选择想要的主题（浅色主题/深色主题/深色橙色主题）
3. 主题立即应用到整个IDE，包括文本编辑器

#### 方式二：偏好设置切换
1. 点击菜单栏中的"工具" → "偏好设置"
2. 在左侧选择"Theme"
3. 选择想要的主题单选按钮
4. 主题立即应用到整个IDE，包括文本编辑器

### 3. 主题同步机制保持不变

- ✅ **全局同步**：通过菜单栏或偏好设置切换主题时，文本编辑器会自动同步
- ✅ **即时生效**：主题切换立即应用，无需重启
- ✅ **状态保存**：主题选择会自动保存，重启后恢复
- ✅ **多编辑器支持**：所有打开的文本编辑器都会同步应用新主题

## 技术实现

### 1. 保留的功能

- **主题管理器集成**：`_load_initial_theme()` 和 `_apply_theme_from_manager()` 方法保持不变
- **主题应用逻辑**：`_apply_white_theme()` 和 `_apply_dark_theme()` 方法保持不变
- **内部主题切换**：`_set_theme()` 方法保留，但仅供主题管理器调用

### 2. 移除的功能

- **右键主题菜单**：完全移除主题相关的菜单项
- **直接主题切换**：用户无法直接在编辑器中切换主题

### 3. 架构优势

这种设计带来了以下优势：

1. **统一的主题管理**：所有主题切换都通过主题管理器进行，避免了不一致的问题
2. **简化的用户界面**：右键菜单更加简洁，专注于编辑功能
3. **清晰的职责分离**：文本编辑器专注于编辑功能，主题管理由专门的组件负责
4. **更好的用户体验**：用户通过统一的入口进行主题设置，避免混淆

## 用户体验

### 1. 简化的操作

- **右键菜单**：更加简洁，只包含编辑相关功能
- **主题设置**：统一在设置区域进行，更符合用户习惯

### 2. 一致的体验

- **全局主题**：通过菜单栏或偏好设置切换的主题会应用到整个IDE
- **同步更新**：所有组件（包括文本编辑器）都会同步应用新主题

### 3. 专业的界面

- **功能分离**：编辑器专注于编辑，设置专注于配置
- **标准化**：符合现代IDE的设计模式

## 总结

这次修改实现了：

- ✅ **移除了文本编辑器右键菜单中的主题选项**
- ✅ **保持了主题管理器的完整功能**
- ✅ **简化了用户界面，提升了用户体验**
- ✅ **维护了代码的清晰性和可维护性**

现在用户可以通过菜单栏或偏好设置来统一管理IDE主题，文本编辑器会自动同步应用选择的主题，提供了更加专业和一致的用户体验！🎨
