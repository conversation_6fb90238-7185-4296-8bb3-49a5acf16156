# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     TextEdit
   Description :
   Author :       10140129
   date：          2019/10/23
-------------------------------------------------
   Change Activity:
                   2019/10/23:
-------------------------------------------------
"""
import keyword
import os
import sys
import traceback

from PyQt5.Qsci import <PERSON>sciScin<PERSON><PERSON>, QsciAPIs
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QCursor
from PyQt5.QtWidgets import QApplication, QVBoxLayout, QMenu, QAction

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.text_edit.SearchArea import SearchArea
from iplatform.highlight.PythonHighlighter import <PERSON><PERSON><PERSON><PERSON><PERSON>
from iplatform.highlight.RobotHighlighter import RobotHighlighter
from model.CurrentItem import CurrentItem
from model.data_file.Repository import DataFileRepository, ProjectTreeRepository, LocalKeyWordRepository, BuildInKeyWordRepository
from settings.UserSettings import UserSettings
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from controller.parser.subscriber.LocalRepoUpdater import LocalRepoUpdater
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper
TEXT_EDIT = 1


class TextEditor(QsciScintilla):

    def __init__(self):
        super(TextEditor, self).__init__()
        self.setWindowTitle('TextEditor')
        self.setWrapMode(self.WrapNone)
        self._connect_signals()
        self.last_file_path = None
        self.last_file_content = ""
        self.setUtf8(True)
        self.setTabWidth(4)
        self._set_caret_line()
        self._set_auto_completion()
        # 根据文件类型选择高亮器
        self.lexer = PythonHighlighter(self)
        self.setLexer(self.lexer)
        self._prepare_keywords()
        self.setMarginsFont(QFont(UserSettings().get_value("FONT"), 10))
        self._set_scroll()

        # 关键字跳转相关初始化
        self._ctrl_pressed = False
        self._current_keyword = None
        self._keyword_jumper = SpecifiedKeywordJumper()
        self._underlined_indicators = []  # 存储下划线指示器

        # 主题相关初始化
        self._current_theme = "white"  # 默认白色主题

        # 设置鼠标跟踪
        self.setMouseTracking(True)

        # 设置右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

    def _set_auto_completion(self):
        self.setAutoCompletionSource(QsciScintilla.AcsAll)
        self.setAutoCompletionCaseSensitivity(True)
        self.setAutoCompletionReplaceWord(False)
        self.setAutoCompletionThreshold(1)
        self.setAutoCompletionUseSingle(QsciScintilla.AcusExplicit)

    def _prepare_keywords(self):
        self.__api = QsciAPIs(self.lexer)
        autocompletions = keyword.kwlist + UserSettings().get_value('PYTHON_KEYWORDS')
        for ac in autocompletions:
            self.__api.add(ac)
        self.__api.prepare()

    def _connect_signals(self):
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.show_item.connect(self._show_content)
        self._signal_distributor.highlight_keyword.connect(self._highlight_py_keyword)
        self._signal_distributor.refresh_text_edit.connect(self._refresh_content)
        self.textChanged.connect(self.set_star)

    def _set_caret_line(self):
        self.setCaretLineVisible(True)
        lineColor = QColor(Qt.yellow).lighter(160)
        self.setCaretLineBackgroundColor(lineColor)

    def _set_scroll(self):
        self.SendScintilla(self.SCI_SETSCROLLWIDTH, 300)
        self.SendScintilla(self.SCI_SETSCROLLWIDTHTRACKING, True)

    def load(self):
        self._layout = QVBoxLayout()
        search_area = SearchArea(self)
        search_area.load()
        self._layout.addLayout(search_area.get_layout())
        self._layout.addWidget(self)

    def get_layout(self):
        return self._layout

    def set_last_file_path(self, path):
        self.last_file_path = path

    def load_file(self, path):
        if path:
            path = os.path.abspath(path)
            self._file_path = path
            self._data_file_obj = None
            if os.path.isdir(path):
                data_file_obj = ProjectTreeRepository().find(path).init_file
            else:
                data_file_obj = DataFileRepository().find(path)

            # 根据文件类型设置高亮器
            if path.endswith('.robot') or path.endswith('.tsv'):
                print('robot')
                self.lexer = RobotHighlighter(self)
            else:
                self.lexer = PythonHighlighter(self)

            # 设置高亮器
            self.setLexer(self.lexer)

            self.last_file_content = self.get()
            if data_file_obj:
                self.setReadOnly(False)
                self.setText(data_file_obj.get_content())
                # 再次确保高亮更新
                self.SendScintilla(QsciScintilla.SCI_COLOURISE, 0, -1)
            else:
                self.setText("")
                self.setReadOnly(True)
            self._data_file_obj = data_file_obj

    def get(self):
        return self.text()
#         return self.document().toPlainText()

#     def resizeEvent(self, *e):
#         cr = self.contentsRect()
#         rec = QRect(cr.left(), cr.top(), self.number_bar.getWidth(), cr.height())
#         self.number_bar.setGeometry(rec)

    def _show_content(self, item):
        LocalRepoUpdater(item).update()
        tab = PluginRepository().find('EDIT_TAB')
        if tab.currentIndex() == TEXT_EDIT:
            path = CurrentItem().get().get("path")
            print(CurrentItem().get())
            print(path)
            if not path:
                return
            if not self.last_file_path or path != self.last_file_path:
                print(self.last_file_path)
                self.load_file(path)

            # 高亮并滚动到item名称
            text = CurrentItem().get().get("name")
            print(text)
            if not text:
                return
            if path.endswith('.robot') or path.endswith('.tsv'):
                line_number = self.find_line_number(self.text(), text)
                print(line_number)
                if line_number != -1:
                    # 将目标行滚动到屏幕中间
                    self._scroll_to_center(line_number)
                    # 只定位光标，不选中文字（避免与Ctrl+点击跳转冲突）
                    self.setCursorPosition(line_number, 0)

    def _highlight_py_keyword(self, text):
        line_number = self.find_py_keyword_line_number(self.text(), text)
        print(line_number)
        if line_number:
            # 将目标行滚动到屏幕中间
            self._scroll_to_center(line_number)
            # 只定位光标，不选中文字（避免与Ctrl+点击跳转冲突）
            self.setCursorPosition(line_number, 8)

    def find_line_number(self, text, target):
        lines = text.splitlines()
        for i, line in enumerate(lines):
            if line.startswith(target):  # 判断是否以目标字符串开头
                return i
        return -1  # 如果找不到，返回 -1

    def find_py_keyword_line_number(self, text, target):
        lines = text.splitlines()
        for i, line in enumerate(lines):
            if target in line and 'def ' in line:  # 判断是否以目标字符串开头
                return i
        return -1  # 如果找不到，返回 -1

    def _refresh_content(self, item):
        path = CurrentItem().get().get("path")
        if not path:
            return
        self.load_file(path)

    def _scroll_to_center(self, target_line):
        """将目标行滚动到屏幕中间"""
        try:
            # 获取编辑器的可见区域信息
            lines_on_screen = self.SendScintilla(QsciScintilla.SCI_LINESONSCREEN)

            if lines_on_screen > 0:
                # 计算屏幕中间的行号
                center_offset = lines_on_screen // 2

                # 计算目标行应该滚动到的位置（目标行在屏幕中间）
                target_first_visible_line = target_line - center_offset

                # 确保滚动位置在合理范围内
                total_lines = self.lines()
                max_line = max(0, total_lines - lines_on_screen)
                target_first_visible_line = max(0, min(target_first_visible_line, max_line))

                # 执行滚动
                self.SendScintilla(QsciScintilla.SCI_SETFIRSTVISIBLELINE, target_first_visible_line)
                print(f"已将第 {target_line} 行滚动到屏幕中间")
            else:
                # 如果无法获取屏幕行数，使用默认滚动
                self.ensureLineVisible(target_line)

        except Exception as e:
            print(f"滚动到屏幕中间时出错: {e}")
            # 出错时使用默认滚动
            self.ensureLineVisible(target_line)

    def _clear_selection(self):
        """清除文字选中的具体实现"""
        try:
            # 使用QsciScintilla的正确方法清除选中
            self.SendScintilla(QsciScintilla.SCI_CLEARSELECTIONS)
        except Exception as e:
            print(f"清除选中时出错: {e}")

    def _clear_selection_after_jump(self):
        """跳转后清除文字选中并调整滚动位置"""
        try:
            # 使用多次延迟清除选中，确保跳转完成后再清除
            # 立即清除一次
            self._clear_selection()
            # 50毫秒后再清除一次
            QTimer.singleShot(50, self._clear_selection)
            # 100毫秒后再清除一次（处理异步信号）
            QTimer.singleShot(100, self._clear_selection)
            # 200毫秒后最后清除一次
            QTimer.singleShot(200, self._clear_selection)
        except Exception as e:
            print(f"清除选中文字时出错: {e}")

    def keyPressEvent(self, event):
        """处理键盘按下事件"""
        if event.key() == Qt.Key_Control:
            self._ctrl_pressed = True
            # 如果鼠标在关键字上，立即显示下划线
            self._check_keyword_under_cursor()
        super().keyPressEvent(event)

    def keyReleaseEvent(self, event):
        """处理键盘释放事件"""
        if event.key() == Qt.Key_Control:
            self._ctrl_pressed = False
            # 清除下划线
            self._clear_keyword_underline()
        super().keyReleaseEvent(event)

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        if self._ctrl_pressed:
            self._check_keyword_under_cursor()
        super().mouseMoveEvent(event)

    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        if event.button() == Qt.LeftButton and self._ctrl_pressed:
            keyword = self._get_keyword_at_cursor()
            if keyword and self._is_keyword_in_repository(keyword):
                self._jump_to_keyword(keyword)
                return  # 阻止默认的点击行为
        super().mousePressEvent(event)

    def set_star(self):
        if self._data_file_obj and self._file_path == CurrentItem().get().get("path"):
            SignalDistributor().text_editor_modify(CurrentItem().get_current_item())
        self.setMarginWidth(0, len(str(self.lines())) * 11)

        # 确保高亮更新
        self.SendScintilla(QsciScintilla.SCI_COLOURISE, 0, -1)

    def notice_update(self):
        if CurrentItem().name:
            current_item_path = CurrentItem().get().get("path")
            last_item = ProjectTreeItemRepository().query(self.last_file_path)
            tab_obj = PluginRepository().find('EDIT_TAB')
            if last_item and tab_obj.tabText(tab_obj.currentIndex()) == 'Text Edit' and \
                    self.last_file_path and self.last_file_path != current_item_path and last_item.text(0).startswith('*'):
                SignalDistributor().text_editor_update(last_item, {'text_edit': self.last_file_content})

    def _check_keyword_under_cursor(self):
        """检查光标下的关键字并显示下划线"""
        keyword = self._get_keyword_at_cursor()
        if keyword and self._is_keyword_in_repository(keyword):
            if self._current_keyword != keyword:
                self._clear_keyword_underline()
                self._add_keyword_underline(keyword)
                self._current_keyword = keyword
        else:
            if self._current_keyword:
                self._clear_keyword_underline()
                self._current_keyword = None

    def _get_keyword_at_cursor(self):
        """获取光标位置的关键字"""
        try:
            # 获取鼠标位置
            cursor_pos = QCursor.pos()
            widget_pos = self.mapFromGlobal(cursor_pos)

            # 使用 SendScintilla 获取鼠标位置对应的字符位置
            char_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINTCLOSE, widget_pos.x(), widget_pos.y())
            if char_pos < 0:
                return None

            # 获取行号和列号
            line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, char_pos)
            col = char_pos - self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)

            # 获取该行的文本
            line_text = self.text(line)
            if not line_text or col >= len(line_text):
                return None

            # 查找关键字边界
            start = col
            end = col

            # 向前查找关键字开始位置
            while start > 0 and (line_text[start - 1].isalnum() or line_text[start - 1] in '_'):
                start -= 1

            # 向后查找关键字结束位置
            while end < len(line_text) and (line_text[end].isalnum() or line_text[end] in '_'):
                end += 1

            # 提取关键字
            keyword = line_text[start:end].strip()
            return keyword if keyword else None

        except Exception as e:
            print(f"获取光标位置关键字时出错: {e}")
            return None

    def _is_keyword_in_repository(self, keyword):
        """检查关键字是否在仓库中"""
        try:
            # 检查内置关键字
            builtin_result = BuildInKeyWordRepository().query(keyword)
            if builtin_result and builtin_result.get(keyword):
                return True

            # 检查本地关键字（包括用户关键字和库关键字）
            local_result = LocalKeyWordRepository().query(keyword)
            if local_result and local_result.get(keyword):
                return True

            return False
        except Exception as e:
            print(f"检查关键字时出错: {e}")
            return False

    def _add_keyword_underline(self, keyword):
        """为关键字添加下划线"""
        try:
            # 获取鼠标位置
            cursor_pos = QCursor.pos()
            widget_pos = self.mapFromGlobal(cursor_pos)

            # 使用 SendScintilla 获取鼠标位置对应的字符位置
            char_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINTCLOSE, widget_pos.x(), widget_pos.y())
            if char_pos < 0:
                return

            # 获取行号和列号
            line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, char_pos)
            col = char_pos - self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)

            # 获取该行的文本
            line_text = self.text(line)
            if not line_text:
                return

            # 查找关键字在行中的位置
            start = col
            end = col

            # 向前查找关键字开始位置
            while start > 0 and (line_text[start - 1].isalnum() or line_text[start - 1] in '_'):
                start -= 1

            # 向后查找关键字结束位置
            while end < len(line_text) and (line_text[end].isalnum() or line_text[end] in '_'):
                end += 1

            # 设置蓝色下划线指示器
            indicator_id = 1  # 使用指示器ID 1
            self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
            self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, indicator_id, 1)  # 1 = INDIC_UNDERLINE
            self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, indicator_id, 0x0000FF)  # 蓝色
            self.SendScintilla(QsciScintilla.SCI_INDICSETALPHA, indicator_id, 255)  # 设置透明度

            # 计算字符位置
            start_pos = self.positionFromLineIndex(line, start)
            end_pos = self.positionFromLineIndex(line, end)

            # 添加下划线
            self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_pos, end_pos - start_pos)

            # 记录下划线位置
            self._underlined_indicators.append((start_pos, end_pos - start_pos))

        except Exception as e:
            print(f"添加关键字下划线时出错: {e}")

    def _clear_keyword_underline(self):
        """清除关键字下划线"""
        try:
            indicator_id = 1
            self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)

            # 清除所有下划线
            for start_pos, length in self._underlined_indicators:
                self.SendScintilla(QsciScintilla.SCI_INDICATORCLEARRANGE, start_pos, length)

            self._underlined_indicators.clear()

        except Exception as e:
            print(f"清除关键字下划线时出错: {e}")

    def _jump_to_keyword(self, keyword):
        """跳转到关键字定义"""
        try:
            # 获取关键字路径
            path = self._keyword_jumper.get_keyword_path_from_local_repository(keyword)
            if path:
                # 使用SpecifiedKeywordJumper进行跳转
                target_item = self._keyword_jumper.get_keyword_item(path, keyword)
                print(target_item)
                if target_item:
                    # 跳转成功后，延迟选中目标关键字
                    QTimer.singleShot(100, lambda: self._select_keyword_in_editor(keyword))
                    print(f"跳转到关键字: {keyword} at {path}")
            else:
                print(f"未找到关键字定义: {keyword}")

        except Exception as e:
            print(f"跳转到关键字时出错: {e}")

    def _select_keyword_in_editor(self, keyword):
        """在编辑器中选中目标关键字"""
        try:
            # 获取当前编辑器
            current_editor = self._get_current_editor()
            if not current_editor:
                return

            # 在当前编辑器中查找并选中关键字
            if hasattr(current_editor, 'findFirst'):
                # 查找关键字
                found = current_editor.findFirst(keyword, False, True, False, True)
                if found:
                    # 获取选中的文本位置
                    line_from, index_from, line_to, index_to = current_editor.getSelection()

                    # 确保关键字居中显示
                    current_editor.ensureLineVisible(line_from)
                    current_editor.setCursorPosition(line_from, index_from)

                    # 滚动到屏幕中间
                    self._scroll_to_center_for_editor(current_editor, line_from)

                    print(f"已选中关键字: {keyword} at line {line_from + 1}")
                else:
                    print(f"在编辑器中未找到关键字: {keyword}")

        except Exception as e:
            print(f"选中关键字时出错: {e}")

    def _get_current_editor(self):
        """获取当前活动的编辑器"""
        try:
            # 尝试获取文本编辑器
            text_edit = PluginRepository().find('TEXT_EDIT')
            if text_edit and hasattr(text_edit, 'isVisible') and text_edit.isVisible():
                return text_edit

            # 尝试获取编辑页签中的编辑器
            edit_plugin_controller = PluginRepository().find('edit_plugin_controller')
            if edit_plugin_controller and hasattr(edit_plugin_controller, '_editor'):
                editor = edit_plugin_controller._editor
                if editor and hasattr(editor, 'findFirst'):
                    return editor

            return None

        except Exception as e:
            print(f"获取当前编辑器时出错: {e}")
            return None

    def _scroll_to_center_for_editor(self, editor, line):
        """将指定行滚动到编辑器中间（用于其他编辑器）"""
        try:
            if hasattr(editor, 'SendScintilla'):
                # 获取可见行数
                visible_lines = editor.SendScintilla(editor.SCI_LINESONSCREEN)
                # 计算目标行应该在的位置（屏幕中间）
                target_top_line = max(0, line - visible_lines // 2)
                # 滚动到目标位置
                editor.SendScintilla(editor.SCI_SETFIRSTVISIBLELINE, target_top_line)
            elif hasattr(editor, 'verticalScrollBar'):
                # 备用方法：使用滚动条
                editor.ensureLineVisible(line)

        except Exception as e:
            print(f"滚动到中间时出错: {e}")

    def _show_context_menu(self, position):
        """显示右键菜单"""
        try:
            # 创建菜单
            menu = QMenu(self)

            # 添加标准编辑菜单选项
            self._add_standard_menu_actions(menu)

            # 添加分隔符
            menu.addSeparator()

            # 添加主题选项
            theme_menu = menu.addMenu("主题")

            # 白色背景选项
            white_action = QAction("白色背景", self)
            white_action.setCheckable(True)
            white_action.setChecked(self._current_theme == "white")
            white_action.triggered.connect(lambda: self._set_theme("white"))
            theme_menu.addAction(white_action)

            # 黑色背景选项
            dark_action = QAction("黑色背景", self)
            dark_action.setCheckable(True)
            dark_action.setChecked(self._current_theme == "dark")
            dark_action.triggered.connect(lambda: self._set_theme("dark"))
            theme_menu.addAction(dark_action)

            # 显示菜单
            menu.exec_(self.mapToGlobal(position))

        except Exception as e:
            print(f"显示右键菜单时出错: {e}")

    def _add_standard_menu_actions(self, menu):
        """添加标准编辑菜单选项"""
        try:
            # 撤销
            undo_action = QAction("撤销", self)
            undo_action.setEnabled(self.isUndoAvailable())
            undo_action.triggered.connect(self.undo)
            menu.addAction(undo_action)

            # 重做
            redo_action = QAction("重做", self)
            redo_action.setEnabled(self.isRedoAvailable())
            redo_action.triggered.connect(self.redo)
            menu.addAction(redo_action)

            # 分隔符
            menu.addSeparator()

            # 剪切
            cut_action = QAction("剪切", self)
            cut_action.setEnabled(self.hasSelectedText())
            cut_action.triggered.connect(self.cut)
            menu.addAction(cut_action)

            # 复制
            copy_action = QAction("复制", self)
            copy_action.setEnabled(self.hasSelectedText())
            copy_action.triggered.connect(self.copy)
            menu.addAction(copy_action)

            # 粘贴
            paste_action = QAction("粘贴", self)
            paste_action.setEnabled(QApplication.clipboard().mimeData().hasText())
            paste_action.triggered.connect(self.paste)
            menu.addAction(paste_action)

            # 分隔符
            menu.addSeparator()

            # 全选
            select_all_action = QAction("全选", self)
            select_all_action.triggered.connect(self.selectAll)
            menu.addAction(select_all_action)

        except Exception as e:
            print(f"添加标准菜单选项时出错: {e}")

    def _set_theme(self, theme):
        """设置主题"""
        try:
            if theme == self._current_theme:
                return

            self._current_theme = theme

            if theme == "white":
                self._apply_white_theme()
            elif theme == "dark":
                self._apply_dark_theme()

            print(f"已切换到{theme}主题")

        except Exception as e:
            print(f"设置主题时出错: {e}")

    def _apply_white_theme(self):
        """应用白色主题"""
        try:
            # 清除样式表，恢复默认样式
            self.setStyleSheet("")

            # 设置白色背景
            self.setColor(QColor("#000000"))  # 黑色文字
            self.setPaper(QColor("#FFFFFF"))  # 白色背景

            # 设置选中文本的颜色
            self.setSelectionBackgroundColor(QColor("#316AC5"))  # 蓝色选中背景
            self.setSelectionForegroundColor(QColor("#FFFFFF"))  # 白色选中文字

            # 设置光标颜色
            self.setCaretForegroundColor(QColor("#000000"))  # 黑色光标

            # 设置行号区域
            self.setMarginsBackgroundColor(QColor("#F0F0F0"))  # 浅灰色行号背景
            self.setMarginsForegroundColor(QColor("#666666"))  # 深灰色行号文字

            # 设置当前行高亮
            self.setCaretLineBackgroundColor(QColor("#F5F5F5"))  # 浅灰色当前行背景
            self.setCaretLineVisible(True)

            # 设置边距线颜色
            self.setEdgeColor(QColor("#C0C0C0"))  # 浅灰色边距线

            # 设置折叠区域颜色
            self.setFoldMarginColors(QColor("#F0F0F0"), QColor("#FFFFFF"))

            # 重新应用语法高亮
            if hasattr(self, 'lexer') and self.lexer:
                self.setLexer(self.lexer)

        except Exception as e:
            print(f"应用白色主题时出错: {e}")

    def _apply_dark_theme(self):
        """应用黑色主题"""
        try:
            # 设置整个编辑器的样式表
            self.setStyleSheet("""
                QsciScintilla {
                    background-color: #2B2B2B;
                    color: #E0E0E0;
                    border: none;
                }
            """)

            # 设置文本区域的背景和前景色
            self.setColor(QColor("#E0E0E0"))  # 浅灰色文字
            self.setPaper(QColor("#2B2B2B"))  # 深灰色背景

            # 设置选中文本的颜色
            self.setSelectionBackgroundColor(QColor("#4A90E2"))  # 蓝色选中背景
            self.setSelectionForegroundColor(QColor("#FFFFFF"))  # 白色选中文字

            # 设置光标颜色
            self.setCaretForegroundColor(QColor("#FFFFFF"))  # 白色光标

            # 设置行号区域
            self.setMarginsBackgroundColor(QColor("#3C3C3C"))  # 深灰色行号背景
            self.setMarginsForegroundColor(QColor("#A0A0A0"))  # 浅灰色行号文字

            # 设置当前行高亮
            self.setCaretLineBackgroundColor(QColor("#404040"))  # 深灰色当前行背景
            self.setCaretLineVisible(True)

            # 设置边距线颜色
            self.setEdgeColor(QColor("#555555"))  # 深灰色边距线

            # 设置折叠区域颜色
            self.setFoldMarginColors(QColor("#3C3C3C"), QColor("#2B2B2B"))

            # 应用黑色主题的语法高亮
            self._apply_dark_syntax_highlighting()

        except Exception as e:
            print(f"应用黑色主题时出错: {e}")

    def _apply_dark_syntax_highlighting(self):
        """应用黑色主题的语法高亮"""
        try:
            if hasattr(self, 'lexer') and self.lexer:
                # 为黑色主题设置特殊的语法高亮颜色
                if hasattr(self.lexer, 'setColor'):
                    # Python语法高亮颜色（黑色主题）
                    self.lexer.setColor(QColor("#E0E0E0"), 0)  # 默认文字 - 浅灰色
                    self.lexer.setColor(QColor("#75715E"), 1)  # 注释 - 灰色
                    self.lexer.setColor(QColor("#66D9EF"), 2)  # 关键字 - 青色
                    self.lexer.setColor(QColor("#A6E22E"), 3)  # 字符串 - 绿色
                    self.lexer.setColor(QColor("#AE81FF"), 4)  # 数字 - 紫色
                    self.lexer.setColor(QColor("#F92672"), 5)  # 操作符 - 红色
                    self.lexer.setColor(QColor("#FD971F"), 6)  # 函数名 - 橙色
                    self.lexer.setColor(QColor("#E6DB74"), 7)  # 类名 - 黄色

                # 重新设置词法分析器
                self.setLexer(self.lexer)

        except Exception as e:
            print(f"应用黑色主题语法高亮时出错: {e}")

# def TextEditorChanged(self):
#     def decorator(func):
#         def inner(*arg, **kwargs):
#             text_edit = PluginRepository().find('TEXT_EDIT')
#             text_edit.notice_update()
#             return func(*arg, **kwargs)
#         return inner
#     return decorator


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = TextEditor()
#     ex.load_fine("D:/CODE/rf-ide/testcases/env/calc/Calc.py")
    ex.load_file("D:/calc.py")
    ex.show()
    sys.exit(app.exec_())
