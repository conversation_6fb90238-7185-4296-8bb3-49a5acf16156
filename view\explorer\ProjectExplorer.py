# encoding=utf-8
'''
Create on  2019年10月14日

@author:10240349/10140129
'''
from _functools import partial
import os
from settings.SystemSettings import SystemSettings
from PyQt5 import QtCore
from PyQt5.Qt import Qt, QHeader<PERSON>iew, QThread, pyqtSignal
from PyQt5.QtWidgets import Q<PERSON><PERSON>Widget, QDockWidget, QAbstractItemView
from PyQt5.QtCore import QObject
from pathlib import Path

from controller.FileUpdater import FileUpdater
from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from model.CurrentItem import CurrentItem
from model.ProjectTree import DataFileParseThread
from settings.HistoryProject import HistoryProject
from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from utility.UIRepository import UIRepository
from view.common.MessageBox import MessageBox
from view.explorer.tree_item.LoadingProgressBar import LoadingProgressBar
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem
from view.explorer.tree_item.PyItem import PyItem
from view.explorer.tree_item.ResourceItem import ResourceItem
from view.explorer.tree_item.SuiteItem import SuiteItem
from view.explorer.tree_item.TestcaseItem import TestcaseItem
from view.explorer.tree_item.TreeItemEventListener import TreeItemEventListener
from view.explorer.tree_item.UserKeywordItem import UserKeywordItem
from view.explorer.tree_item.VariableItem import VariableItem

TEMP_PROJECT_PATH = 'D:/rfcode_project_path.ini'


class ProjectExplorer(QObject):
    node_loaded = pyqtSignal()

    def __init__(self, parent):
        super().__init__()
        self._project_explorer_dock = QDockWidget("Project Explorer", parent)
        self._project_explorer_dock.setFeatures(QDockWidget.DockWidgetMovable)
        self.create_context_menu()
        self.isExpandLatestDir = False
        self.lastClickItemPath = ''

    def load(self):
        if os.path.exists(TEMP_PROJECT_PATH):
            with open(TEMP_PROJECT_PATH, 'r', encoding='utf-8') as f:
                project_path = f.read()
                HistoryProject().write('PROJECT_PATH', project_path)
            if self._check_path_is_valid(project_path):
                self._load_history_project(project_path)
                self.node_loaded.emit()
                return
        project_path = HistoryProject.read('PROJECT_PATH')
        if project_path and self._check_path_is_valid(project_path):
            self._load_history_project(project_path)
        else:
            empty_tree = QTreeWidget()
            empty_tree.header().setVisible(False)
            self._project_explorer_dock.setWidget(empty_tree)
        self.node_loaded.emit()

    def _check_path_is_valid(self, project_path):
        if os.path.exists(project_path) and ProjectExplorer._is_valid_project(project_path):
            return True
        else:
            MessageBox().show_critical(LanguageLoader().get('PATH_ERROR'))
            return False

    @staticmethod
    def _is_valid_project(path):
        file_list = os.listdir(path)
        rfcode_file_is_exist = False
        for filename in file_list:
            if filename == '.rfcode':
                rfcode_file_is_exist = True
        return rfcode_file_is_exist

    def _load_history_project(self, path):
        self.load_root(path)

    def get_widget(self):
        return self._project_explorer_dock

    def create_context_menu(self):
        self._project_explorer_dock.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self._project_explorer_dock.customContextMenuRequested.connect(self.show_context_menu)

    def show_context_menu(self, pos):
        item = self._tree.currentItem()
        if not item:
            return
        elif isinstance(item, ProjectTreeItem):
            item.show_context_menu(self._tree, pos)
        elif isinstance(item, SuiteItem):
            item.show_context_menu(self._tree, pos)
        elif isinstance(item, VariableItem):
            item.show_context_menu(self._tree, pos)
        elif isinstance(item, TestcaseItem):
            item.show_context_menu(self._tree, pos)
        elif isinstance(item, UserKeywordItem):
            item.show_context_menu(self._tree, pos)
        elif isinstance(item, ResourceItem):
            item.show_context_menu(self._tree, pos)
        elif isinstance(item, PyItem):
            item.show_context_menu(self._tree, pos)

    def load_root(self, path='5GNR'):
        self._tree = TreeWidget()
        self._tree.setAnimated(True)
        self._tree.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self._set_tree_horizontal_scroll_bar()
        TreeItemEventListener().start()
        self._connect_signal()
        tree_item = ProjectTreeItem(self._tree, path=path)
        self.rootItem = tree_item
        self._store_repo(tree_item)
        self._set_tree_status(tree_item, path)
        self._project_explorer_dock.setWidget(self._tree)
        self._dt = DataFileParseThread(tree_item._data_file)
        loading_bar = LoadingProgressBar()
        self._dt.parse_finished.connect(partial(self.reload_tree, tree_item, loading_bar))
        self._dt.start()
        if isinstance(tree_item, ProjectTreeItem):
            loading_bar.run()

    def _on_click(self):
        # 检查是否是复选框点击，如果是则不触发用例选中事件
        if hasattr(self._tree, 'is_checkbox_clicked') and self._tree.is_checkbox_clicked():
            return

        # if not self.isExpandLatestDir:
        #     path = HistoryProject.read('LAST_CLICKED_PATH')
        #     if path:
        #         self.expand_item_by_path(path)
        #         self.isExpandLatestDir = True
        item = self._tree.currentItem()
        if not item:
            return
        SignalDistributor().show(item)
        text_edit = PluginRepository().find('TEXT_EDIT')
        text_edit.notice_update()
        FileUpdater(self).check(item)
        if isinstance(item, VariableItem):
            SignalDistributor().emit_varible_locate(item.parent().indexOfChild(item))
        self.lastClickItemPath = item.get_path()

    def _on_double_click(self):
        item = self._tree.currentItem()
        item.update_repository()
        if not item._is_clicked:
            self._dt = DataFileParseThread(item._data_file)
            loading_bar = LoadingProgressBar()
            self._dt.parse_finished.connect(partial(self.reload_tree, item, loading_bar))  # 解析完成开始加载树
            self._dt.start()
            if isinstance(item, ProjectTreeItem):
                loading_bar.run()

    def expand_item_by_path(self, file_path='D:/script_v2/5GNR/test/test.tsv', case_name=None):
        current_item = None
        try:
            # Python3兼容的路径处理
            file_path = str(file_path)  # 确保是字符串类型
            path = Path(file_path)
            pathList = [str(part) for part in path.parts]  # 确保所有部分都是字符串
            pathList = self._remove_list_before_name(pathList, str(self.rootItem.text(0)))

            item = self.rootItem
            for path_part in pathList:
                if not item:
                    break
                item = item.get_child_by_name(path_part)
                if item:
                    item.update_repository()
                    if not item._is_clicked:
                        self._dt = DataFileParseThread(item._data_file)
                        self._dt.run_in_main_thread()
                        self.reload_tree(item)
        except Exception as e:
            print(f"Error expanding path: {e}")
            return
        self._tree.scrollToItem(item, QAbstractItemView.PositionAtCenter)
        self._tree.horizontalScrollBar().setValue(0)
        current_item = None
        if case_name:
            for i in range(item.childCount()):
                if item.child(i).text(0) == case_name:
                    item.child(i).setSelected(True)
                    self._tree.scrollToItem(item.child(i), QAbstractItemView.PositionAtCenter)
                    self._tree.horizontalScrollBar().setValue(0)
                    current_item = item.child(i)
                    self._tree.setCurrentItem(current_item)
                    break
        else:
            self._tree.setCurrentItem(current_item)
        CurrentItem().set(current_item)

    def update_text_edit(self):
        self._on_click()

    def _connect_signal(self):
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.text_editor_save_item.connect(self._save_content)
        self._signal_distributor.text_editor_update_item.connect(self._update_content)
        self._tree.doubleClicked.connect(self._on_double_click)
        self._tree.clicked.connect(self._on_click)

    def _set_tree_status(self, tree_item, path):
        self._tree.insertTopLevelItem(0, tree_item)
        self._tree.header().setVisible(False)
        tree_item.setText(0, os.path.basename(path))

    def _store_repo(self, tree_item):
        ProjectTreeItemRepository().add("PROJECT_TREE_ITEM", tree_item)
        ProjectTreeRepository().add("PROJECT_TREE", self._tree)

    def _set_tree_horizontal_scroll_bar(self):
        header = self._tree.header()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setStretchLastSection(False)
        self._tree.setHorizontalScrollMode(QAbstractItemView.ScrollPerPixel)
        self._tree.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

    def reload_tree(self, item, loading_bar=None):
        item.reload_children_once()
        self._tree.resizeColumnToContents(0)
        item.setExpanded(True)
        if loading_bar:
            loading_bar.destroy()
        CurrentItem().set(item)
        UIRepository().update('testcaseitem_object', str(CurrentItem().get_current_item()))
        SignalDistributor().show(item)

    def _remove_list_before_name(self, lst, name):
        try:
            index = next(i for i, item in enumerate(lst) if item == name)
            return lst[index+1:]
        except StopIteration:
            return lst

    def destroy_loading_bar(self, loading_bar):
        loading_bar.destroy()

    def _update_content(self, item, modified_values):
        parsed_item = ItemParserFacory().create(type(item).__name__ + 'Parser')
        data_file_obj = parsed_item.get_cur_data_file(item)
        if data_file_obj:
            data_file_obj.update(modified_values['text_edit'])
            if isinstance(item, PyItem) or isinstance(item, SuiteItem) or \
                    isinstance(item, ResourceItem) or isinstance(item, ProjectTreeItem):
                item.refresh_children_without_parse()
            else:
                item.parent().refresh_children_without_parse()

    def _save_content(self, item, modified_values):
        if not item:
            MessageBox().show_warning('没有聚焦工程树节点')
        parsed_item = ItemParserFacory().create(type(item).__name__ + 'Parser')
        data_file_obj = parsed_item.get_cur_data_file(item)
        if data_file_obj:
            data_file_obj.save_content(modified_values['text_edit'])
            if isinstance(item, PyItem) or isinstance(item, SuiteItem) or \
                    isinstance(item, ResourceItem) or isinstance(item, ProjectTreeItem):
                item.set_name_without_star()
            else:
                item.parent().set_name_without_star()



class TreeWidget(QTreeWidget):

    def __init__(self):
        super().__init__()
        self._checkbox_clicked = False

    def mousePressEvent(self, event):
        """重写鼠标按下事件，检测是否点击了复选框"""
        self._checkbox_clicked = False

        # 获取点击位置的项目
        item = self.itemAt(event.pos())
        if item:
            # 获取复选框区域
            checkbox_rect = self.visualItemRect(item)
            # 复选框通常在项目的左侧，宽度约为20像素
            checkbox_rect.setWidth(20)

            # 检查点击是否在复选框区域内
            if checkbox_rect.contains(event.pos()):
                self._checkbox_clicked = True

        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """重写鼠标释放事件"""
        super().mouseReleaseEvent(event)
        # 重置复选框点击标志
        self._checkbox_clicked = False

    def is_checkbox_clicked(self):
        """检查最近的点击是否是复选框点击"""
        return self._checkbox_clicked

    def keyPressEvent(self, event):
        item = self.currentItem()
        super().keyPressEvent(event)
        if event.modifiers() == Qt.ControlModifier and event.key() == Qt.Key_Up:
            if isinstance(item, (TestcaseItem, UserKeywordItem)):
                item._move_up()
        if event.modifiers() == Qt.ControlModifier and event.key() == Qt.Key_Down:
            if isinstance(item, (TestcaseItem, UserKeywordItem)):
                item._move_down()
        if event.key() == Qt.Key_F2:
            if isinstance(item, (SuiteItem, ProjectTreeItem, ResourceItem)):
                item._set_edit_status()
            elif not isinstance(item, PyItem):
                item._set_edit_status(item.text(0))
