# coding=utf-8
'''
Created on 2019年11月1日

@author: 10240349
'''
import os

from PyQt5.Qt import QIcon, Qt
from PyQt5.QtWidgets import QMenu

from controller.system_plugin.SignalDistributor import SignalDistributor
from resources.Loader import Loader
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from utility.UIRepository import UIRepository
from view.common.MessageBox import MessageBox, message_box_dec
from view.common.dialog.DictVariableDialog import DictVariableDialog
from view.common.dialog.KeywordDialog import KeywordDialog
from view.common.dialog.ListVariableDialog import ListVariableDialog
from view.common.dialog.ScalarDialog import ScalarDialog
from view.common.dialog.SuiteDialog import SuiteDialog
from view.explorer.tree_item.CheckBoxApi import <PERSON><PERSON>ox<PERSON>pi
from view.explorer.tree_item.TestcaseItem import TestcaseItem
from view.explorer.tree_item.TreeItem import TreeItem
from view.explorer.tree_item.UserKeywordItem import User<PERSON>eywordItem
from view.explorer.tree_item.VariableItem import VariableItem


class SuiteItem(TreeItem, CheckBoxApi):

    def __init__(self, parent, data_file=None, path=None):
        self._tree = parent
        super(SuiteItem, self).__init__(parent, data_file, path)
        self.checked_testcase_num = 0
        self._testsuite_num = 0
        self._directory_num = 0
        self._resource_num = 0
        self._testcase_num = 0
        self._keyword_num = 0
        self._variable_num = 0

    def update_local_repository(self):
        pass

    def reload_children(self):
        if hasattr(self._data_file, 'variables'):
            for c in self._data_file.variables.content:
                tree_item = VariableItem(self, c)
                self.addChild(tree_item)
        if hasattr(self._data_file, 'testcases'):
            for c in self._data_file.testcases.content:
                tree_item = TestcaseItem(self, c)
                self._set_first_testcase_unchecked()  # 解决首次加载第一用例选中的bug
                self.set_unchecked(self)
                self.addChild(tree_item)
        if hasattr(self._data_file, 'keywords'):
            for c in self._data_file.keywords.content:
                tree_item = UserKeywordItem(self, c)
                self.addChild(tree_item)
        self._get_num()

    def _get_num(self):
        self._variable_num = self._get_item_num(self, VariableItem)
        self._testcase_num = self._get_item_num(self, TestcaseItem)
        self._keyword_num = self._get_item_num(self, UserKeywordItem)

    def _get_item_num(self, item, item_type):
        num = 0
        for i in range(item.childCount()):
            if isinstance(item.child(i), item_type):
                num += 1
        return num

    def _set_first_testcase_unchecked(self):
        self._set_all_unchecked()

    def set_ui(self):
        self._name = os.path.basename(self._data_file.get_path())
        self.setText(0, self._name)
        self.setIcon(0, QIcon(Loader().get_path('TESTSUITE')))
        self.setCheckState(0, Qt.Unchecked)

    def setData(self, *args, **kwargs):
        result = TreeItem.setData(self, *args, **kwargs)
        self._check_state = args[2]
        if self.is_check_box(args[1]):
            if self.is_checked(args[2]):
                self._set_all_checked()
            elif self.is_unchecked(args[2]):
                self._set_all_unchecked()
        return result

    def _set_all_checked(self):
        for i in range(self.childCount()):
            if isinstance(self.child(i), TestcaseItem):
                self.child(i).call_by_self = False
                self.set_checked(self.child(i))
                self.child(i).call_by_self = True

    def _set_all_unchecked(self):
        for i in range(self.childCount()):
            if isinstance(self.child(i), TestcaseItem):
                self.child(i).call_by_self = False
                self.set_unchecked(self.child(i))
                self.child(i).call_by_self = True

    def _set_check_state(self):
        if self.checked_testcase_num == 0:
            self.set_unchecked(self)
        elif self.checked_testcase_num == self._testcase_num:
            self.set_checked(self)
        else:
            self.set_partially_checked(self)

    def update_checked_status(self):
        status = self._get_status_to_be_set()
        if status == "ALL_CHECKED":
            self.set_checked(self)
        elif status == "PARTIALLY_CHECKED":
            self.set_partially_checked(self)
        else:
            self.set_unchecked(self)

    def _get_status_to_be_set(self):
        num = self._get_checked_testcases_num()
        if num == self._testcase_num:
            return "ALL_CHECKED"
        elif num == 0:
            return "UNCHECKED"
        else:
            return "PARTIALLY_CHECKED"

    def _get_checked_testcases_num(self):
        num = 0
        for i in range(self.childCount()):
            if isinstance(self.child(i), TestcaseItem):
                if self.child(i).is_checked(self.child(i).checkState(0)):
                    num += 1
        return num

    def get_path(self):
        return self._data_file.path

    def get_child_index(self, child):
        return self.indexOfChild(child)

    def show_context_menu(self, parent, pos):
        self._tree_parent = parent
        menu = self._add_action(QMenu(parent))
        menu.setToolTipsVisible(True)
        self._action = menu.exec_(parent.mapToGlobal(pos))
        self._execute_action()

    def _add_action(self, menu):
        self._set_home_page = menu.addAction(LanguageLoader().get('SET_HOME_PAGE'))
        self._new_testcase = menu.addAction(LanguageLoader().get('NEW_TESTCASE'))
        self._new_keyword = menu.addAction(LanguageLoader().get('NEW_KEYWORD'))
        self._new_scalar = menu.addAction(LanguageLoader().get('NEW_SCALAR'))
        self._new_list_variable = menu.addAction(LanguageLoader().get('NEW_LIST_VARIABLE'))
        self._new_dict_variable = menu.addAction(LanguageLoader().get('NEW_DICT_VARIABLE'))
        self._change_format = menu.addAction(LanguageLoader().get('CHANGE_FORMAT'))
        self._rename_item = menu.addAction(LanguageLoader().get('RENAME'))
        self._add_paste_action(menu)
        self._delete_item = menu.addAction(LanguageLoader().get('DELETE'))
        self._set_home_page.setToolTip(LanguageLoader().get('SET_HOME_PAGE_TOOLTIP'))
        return menu

    def _add_paste_action(self, menu):
        self._copyed_data_file = UIRepository().find('copyed_data_file')
        self._paste = None
        if self._copyed_data_file:
            self._paste = menu.addAction(LanguageLoader().get('PASTE'))

    def _execute_action(self):
        if self._action == self._new_testcase:
            self._add_new_testcase()
        if self._action == self._new_keyword:
            self._add_new_keyword()
        if self._action == self._new_scalar:
            self._add_new_scalar()
        if self._action == self._new_list_variable:
            self._add_new_list_variable()
        if self._action == self._new_dict_variable:
            self._add_new_dict_variable()
        if self._action == self._rename_item:
            self._set_edit_status()
        if self._paste and self._action == self._paste:
            self._paste_items()
        if self._action == self._delete_item:
            self._delete()
        if self._action == self._change_format:
            if PluginRepository().find('TESTCASE_RUNNING'):
                MessageBox().show_critical(LanguageLoader().get('RUNNING_UN_CHANGE_FORMAT'))
            else:
                self._change_item_format()
        if self._action == self._set_home_page:
            self._set_home_page_handler()

    def _add_new_testcase(self):
        self.testcase_dialog = SuiteDialog(LanguageLoader().get('ADD_TESTCASE'))
        self.testcase_dialog.show()
        self.testcase_dialog.ok_pressed.connect(self._set_testcase_contents)

    def _set_testcase_contents(self, info_list):
        name = info_list[0]
        if self.is_unique(name, TestcaseItem):
            testcases = self._data_file.add_testcase(name)
            item = TestcaseItem(self, (testcases.content)[-1])
            self.removeChild(item)
            index = self._testcase_num + self._variable_num
            self.insertChild(index, item)
            self.set_unchecked(self)
            self._tree_parent.setCurrentItem(item)  # 设置光标到新增项
            self._data_file.save()
            self._testcase_num += 1
            self._refresh_content(item)
        else:
            MessageBox().show_warning(LanguageLoader().get('REPEAT_INFO'))

    def _add_new_keyword(self):
        self.keyword_dialog = KeywordDialog(LanguageLoader().get('ADD_KEYWORD'))
        self.keyword_dialog.show()
        self.keyword_dialog.ok_pressed.connect(self._set_keyword_contents)

    def _set_keyword_contents(self, info_list):
        name = info_list[0]
        arguments = info_list[1]
        if self.is_unique(name, UserKeywordItem):
            keywords = self._data_file.add_keyword(name, arguments)
            item = UserKeywordItem(self, (keywords.content)[-1])
            self.removeChild(item)
            index = self._variable_num + self._testcase_num + self._keyword_num
            self.insertChild(index, item)
            self._tree_parent.setCurrentItem(item)  # 设置光标到新增项
            self._data_file.save()
            self._keyword_num += 1
            self._refresh_content(item)
        else:
            MessageBox().show_warning(LanguageLoader().get('REPEAT_INFO'))

    def _add_new_scalar(self):
        self.scalar_dialog = ScalarDialog(LanguageLoader().get('ADD_SCALAR'))
        self.scalar_dialog.show()
        self.scalar_dialog.ok_pressed.connect(self.set_scalar_contents)

    def set_scalar_contents(self, info_list):
        scalar_name_without_char = info_list[0][0]
        scalar_name = info_list[0][1]
        scalar_value = info_list[1]
        scalar_comments = info_list[2]
        _list = [scalar_name, scalar_value, scalar_comments]
        if self.is_unique(scalar_name, VariableItem):
            scalar = self._data_file.add_scalar(scalar_name_without_char, _list)
            self._add_new_variable(scalar.content[-1])
            SignalDistributor().refresh_text_edit_content(self)
        else:
            MessageBox().show_warning(LanguageLoader().get('REPEAT_INFO'))

    def _add_new_list_variable(self):
        self.list_dialog = ListVariableDialog(LanguageLoader().get('ADD_LIST'))
        self.list_dialog.show()
        self.list_dialog.ok_pressed.connect(self.set_list_contents)

    def set_list_contents(self, info_list):
        list_name_without_char = info_list[0][0]
        list_name = info_list[0][1]
        list_value = self._format_list_value(info_list[1])
        list_comments = info_list[2]
        _list = [list_name, list_value, list_comments]
        if self.is_unique(list_name, VariableItem):
            lists = self._data_file.add_list(list_name_without_char, _list)
            self._add_new_variable(lists.content[-1])
            SignalDistributor().refresh_text_edit_content(self)
        else:
            MessageBox().show_warning(LanguageLoader().get('REPEAT_INFO'))

    def _format_list_value(self, info_list):
        _list = []
        for row in info_list:
            if row == '':
                row = '${EMPTY}'
            _list.append(row)
        return _list

    def _add_new_variable(self, variable):
        parent = ProjectTreeRepository().find("PROJECT_TREE")
        item = VariableItem(self, variable)
        self.removeChild(item)
        index = self._variable_num
        self.insertChild(index, item)
        parent.setCurrentItem(item)  # 设置光标到新增项
        self._data_file.save()
        self._variable_num += 1
        SignalDistributor().show(item, True)

    def _add_new_dict_variable(self):
        self.list_dialog = DictVariableDialog(LanguageLoader().get('ADD_DICT'))
        self.list_dialog.show()
        self.list_dialog.ok_pressed.connect(self._set_dict_contents)

    def _set_dict_contents(self, info_list):
        list_name_without_char = info_list[0][0]
        list_name = info_list[0][1]
        list_value = self._format_list_value(info_list[1])
        list_comments = info_list[2]
        _list = [list_name, list_value, list_comments]
        if self.is_unique(list_name, VariableItem):
            lists = self._data_file.add_dict(list_name_without_char, _list)
            self._add_new_variable(lists.content[-1])
            SignalDistributor().refresh_text_edit_content(self)
        else:
            MessageBox().show_warning(LanguageLoader().get('REPEAT_INFO'))

    @message_box_dec(LanguageLoader().get('DELETE_TIPS'))
    def _delete(self):
        selected_items = self._tree_parent.selectedItems()
        deleted_items = []

        for item in selected_items:
            if isinstance(item, SuiteItem):
                parent = item.parent()
                index = parent.indexOfChild(item) - parent._variable_num - \
                    parent._keyword_num
                parent._data_file.del_child(index)
                parent._data_file.save()
                parent.removeChild(item)
                parent._testsuite_num -= 1
                deleted_items.append(item)

        # If no items were deleted, return
        if not deleted_items:
            return

        # Find the next item to focus on
        current = ProjectTreeRepository().find("PROJECT_TREE").currentItem()
        SignalDistributor().show(current)

    def _paste_items(self):
        for item in self._copyed_data_file:
            new_item, index = self._copy_item(item)
            if item.parent() == self:
                name = self._get_new_name(item, 1)
            else:
                name = self._get_new_name(item, 0)
            if new_item:
                new_item.modify('name', name)
                item = type(item)(self, new_item)
                self.removeChild(item)
                self.insertChild(index, item)
                self.set_unchecked(self.parent())
        self._refresh_content(item)
        self._data_file.save()

    def _copy_item(self, item):
        copyed_data_file = item._data_file
        if type(item) == TestcaseItem:
            self._data_file.copy_testcase(copyed_data_file)
            new_item = self._data_file.testcases.content[-1]
            index = self._testcase_num + self._variable_num
            self._testcase_num += 1
        elif type(item) == UserKeywordItem:
            self._data_file.copy_keyword(copyed_data_file)
            new_item = self._data_file.keywords.content[-1]
            index = self._testcase_num + self._variable_num + self._keyword_num
            self._keyword_num += 1
        else:
            return None, None
        return new_item, index

    def _get_new_name(self, item, n):
        new = item.text(0)
        if n > 0:
            new = new + ' - copy%s' % n
        if self.is_unique(new, type(item)):
            return new
        else:
            n += 1
            return self._get_new_name(item, n)

    def _set_edit_status(self):
        if ExecutiveTestCaseRepository().find('is_test_case_running'):
            MessageBox().show_warning('testcase is running, cannot be renamed')
            return
        name = self._data_file.path.split(os.path.sep)[-1]
        suffix = os.path.splitext(name)[-1]
        self._old_name = ProjectTreeRepository().find("PROJECT_TREE").currentItem().text(0)\
            .lstrip('*').split(suffix)[0]
        super()._set_edit_status(self._old_name)

    def _rename(self):
        new_name = self._name_line.text()
        if self._old_name != new_name:
            if self._is_file_name_legal(new_name):
                self._data_file.rename(new_name)
                self._name = self._data_file.path.split(os.path.sep)[-1]
                self._renamed_item.setText(0, os.path.split(self._data_file.path)[-1])
            else:
                self._renamed_item.setText(0, os.path.split(self._data_file.path)[-1])
            SignalDistributor().show(self._tree_item.currentItem())
        self._close_editor()

    def _rename_by_click(self):
        if self._tree_item.currentItem() != self._renamed_item:
            self._rename()
