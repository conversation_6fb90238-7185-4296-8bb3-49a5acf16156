*** Settings ***
Suite Setup	加载配置	ConfigRequest	${dataset}
Suite Teardown	删除配置
Variables	ConfigRequest.py
Resource	variable/resource.tsv
Resource	../../../../../../../testlib5g/infrastructure/resource/resource.tsv
Resource	../../../../../../userkeywords/basic_multi/resource.tsv
Resource	../../template.tsv
Resource	keywords.tsv


*** Test Cases ***
001 初始化环境配置-超级小区节能
	run keyword and ignore error	拆分超级小区
	删除并释放LV无线资源_多模	${GNODEB}
	run keyword and ignore error	修改基带板功能模式_多模	${GNODEB}	VBP_1_3-instance	16;32	12296
	run keyword and ignore error	修改基带板功能模式_多模	${GNODEB}	VBP_1_4-instance	16;8192	233480
	run keyword and ignore error	修改基带板功能模式_多模	${GNODEB}	VBP_1_6-instance	16;8192	36873
	run keyword and ignore error	修改基带板功能模式_多模	${GNODEB}	VBP_1_7-instance	8192	100696097
	run keyword and ignore error	修改基带板功能模式_多模	${GNODEB}	VBP_1_8-instance	8192	163877
	创建所有的基带资源池	${GNODEB}
	创建节能用例所有NR小区	${GNODEB}
	创建节能用例的所有LTE FDD小区	${ENODEB}
	sleep	1200
	${XML_PATH}	导出基站XML并备份	${ENODEB}	${UME}	${True}

RAN-3521056 [NR]超级小区场景：增强型符号关断生效后复位主辅CP全部prru__RAN-3521056
	获取所有小区别名
	配置超级小区
	Wait Until Keyword Succeeds	3min	60sec	确认NR小区状态正常_多模	${cell1}
	关闭NR符号关断开关	${cell1}
	关闭NR符号关断开关	${cell2}
	关闭NR符号关断开关	${cell3}
	同步规划区数据_多模	${GNODEB}
	sleep	120
	打开NR符号关断开关	${cell1}	1
	打开NR符号关断开关	${cell2}	1
	打开NR符号关断开关	${cell3}	1
	同步规划区数据_多模	${GNODEB}
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Enhanced Symbol Shutdown
	...	Start ES	30
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	${rruList}	evaluate	['11-instance', '21-instance']
	批量复位PRRU	${rruList}
	sleep	300
	${rruList}	evaluate	['33-instance', '32-instance','31-instance', '43-instance', '42-instance','41-instance']
	批量复位PRRU	${rruList}
	${rruList}	evaluate	['51-instance', '52-instance', '57-instance', '63-instance', '64-instance', '65-instance']
	批量复位PRRU	${rruList}
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	验证NR小区业务	${CPE1}	1	600	180

RAN-3521057 [NR]超级小区场景：增强型符号关断生效后闭塞辅CP__RAN-3521057
	获取所有小区别名
	配置超级小区
	关闭NR符号关断开关	${cell1}
	关闭NR符号关断开关	${cell2}
	关闭NR符号关断开关	${cell3}
	同步规划区数据_多模	${GNODEB}
	sleep	120
	打开NR符号关断开关	${cell1}	1
	打开NR符号关断开关	${cell2}	1
	打开NR符号关断开关	${cell3}	1
	同步规划区数据_多模	${GNODEB}
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Enhanced Symbol Shutdown
	...	Start ES	30
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	关断超级小区CP	${cell1}	1
	关断超级小区CP	${cell1}	2
	同步规划区数据_多模	${GNODEB}
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown
	解关断超级小区CP	${cell1}	1
	解关断超级小区CP	${cell1}	2
	同步规划区数据_多模	${GNODEB}
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	验证NR小区业务	${CPE1}	1	600	180

RAN-3521060 [NR]超级小区场景：辅cp异常时增强型符号关断启动和停止测试__RAN-3521060
	获取所有小区别名
	配置超级小区
	关闭NR符号关断开关	${cell1}
	关闭NR符号关断开关	${cell2}
	关闭NR符号关断开关	${cell3}
	同步规划区数据_多模	${GNODEB}
	sleep	120
	关断超级小区CP	${cell1}	2
	打开NR符号关断开关	${cell1}	1
	打开NR符号关断开关	${cell2}	1
	打开NR符号关断开关	${cell3}	1
	同步规划区数据_多模	${GNODEB}
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Enhanced Symbol Shutdown
	...	Start ES	30
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown
	验证NR小区业务	${CPE}	1	100	100

RAN-3521055 [NR]非节能态时拆组超级小区，超级小区增强型符号关断启动和停止测试__RAN-3521055
	获取所有小区别名
	配置超级小区
	关闭NR符号关断开关	${cell1}
	关闭NR符号关断开关	${cell2}
	关闭NR符号关断开关	${cell3}
	同步规划区数据_多模	${GNODEB}
	sleep	60
	打开NR符号关断开关	${cell1}	1
	打开NR符号关断开关	${cell2}	1
	打开NR符号关断开关	${cell3}	1
	同步规划区数据_多模	${GNODEB}
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Enhanced Symbol Shutdown
	...	Start ES	30
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	拆分超级小区
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown

RAN-3521086 [LV共框]超级小区场景：启动和退出NR LTE增强型符号关断测试__RAN-3521086
	获取所有小区别名
	配置超级小区
	${cellList}	create list	2	5
	组合LTE超级小区_多模	${GNODEB}	1
	同步规划区数据_多模	${GNODEB}
	关闭NR符号关断开关	${cell1}
	关闭NR符号关断开关	${cell2}
	关闭NR符号关断开关	${cell3}
	同步规划区数据_多模	${GNODEB}
	sleep	120
	打开NR符号关断开关	${cell1}	1
	打开NR符号关断开关	${cell2}	1
	打开NR符号关断开关	${cell3}	1
	同步规划区数据_多模	${GNODEB}
	sleep	60
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Enhanced Symbol Shutdown
	...	Start ES	30
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	3
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	11-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	21-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	33-instance	rfSymbolShutdown
	Wait Until Keyword Succeeds	15min	60sec	确认RRU节能状态	51-instance	rfSymbolShutdown
	验证LTE小区业务	${CPE1}	1
	拆分LTE超级小区_多模	${GNODEB}	1
	同步规划区数据_多模	${GNODEB}
	run keyword and continue on failure	验证NR小区业务	${CPE1}	1	500	100	R8159-0002


*** Keywords ***
加载配置
	[Arguments]	${scene}	${dataset}
	${params}	获取资源	${scene}	${dataset}
	创建基站_多模	${ENODEB}+${GNODEB}	${UME}	${FDDFUNCTION}
	${XML_PATH}	导出基站XML并备份	${ENODEB}	${UME}
	实例化单板_多模	${ENODEB}	${XML_PATH}
	实例化无线配置_多模	${ENODEB}	${XML_PATH}
	创建VSW_多模	${GNODEB}	${VSW}
	创建PDN	${PDN}
	批量删除基站配置_多模	${GNODEB}	PrruSavingArray
	批量删除基站配置_多模	${GNODEB}	PrruPowerSaving
	同步规划区数据_多模	${GNODEB}
	${attrDic}	create dictionary	enableEnergySavingService=1
	修改节能服务配置开关	${GNODEB}	${attrDic}
	关闭ITRAN-LTE-FDD载波关断总开关

删除配置
	释放实例化无线配置_多模	${ENODEB}
	释放实例化单板_多模	${ENODEB}
	删除基站_多模	${ENODEB}+${GNODEB}
	删除UE对象	${CPE}
	删除PDN	${PDN}

修改NR小区频点
	[Arguments]	${cellAlias}	${earfcn}
	${NRPhysicalCellDUid}	获取小区的NRPhysicalCellDUid	${cellAlias}
	${NRCarrier}	获取小区的NRCarrierid	${cellAlias}
	修改CarrierDL中参数	${cellAlias}	frequency	${earfcn}	1	NRCarrier	${NRCarrier}
	修改CarrierUL中参数	${cellAlias}	frequency	${earfcn}	1	NRCarrier	${NRCarrier}

删建部分NR小区
	[Arguments]	${gnbAlias}
	@{moidList}	获取保存NR小区MOID_多模
	FOR	${moid}	IN	@{moidList}
	删除指定NR小区_多模	${gnbAlias}	${moid}
	sleep	60
	FOR	${moid}	IN	@{moidList}
	创建指定NR小区_多模	${gnbAlias}	${moid}

删建部分LTE小区
	[Arguments]	${gnbAlias}
	@{moidList}	获取保存FDD LTE小区的所有MOID_多模
	FOR	${moid}	IN	@{moidList}
	删除指定FDD LTE小区_多模	${gnbAlias}	${moid}
	sleep	60
	FOR	${moid}	IN	@{moidList}
	创建指定FDD LTE小区_多模	${gnbAlias}	${moid}

修改FDD LTE小区频点
	[Arguments]	${gnbAlias}	${moId}	${earfcnDl}	${earfcnUl}
	${filterDict}	Create Dictionary	mocName	CUEUtranCellFDDLTE	moId	${moId}
	${attrDict}	Create Dictionary	earfcnDl	${earfcnDl}	earfcnUl	${earfcnUl}
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}

配置超级小区
	拆分NR超级小区_多模	${GNODEB}	1
	同步规划区数据_多模	${GNODEB}
	${cellList}	create list	2	3
	组合NR超级小区_多模	${GNODEB}	1	${cellList}
	同步规划区数据_多模	${GNODEB}

拆分超级小区
	拆分NR超级小区_多模	${GNODEB}	1
	同步规划区数据_多模	${GNODEB}


