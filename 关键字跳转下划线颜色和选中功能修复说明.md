# 关键字跳转下划线颜色和选中功能修复说明

## 修复内容

### 1. 下划线颜色修复
**问题**: 关键字下划线显示为红色而不是预期的蓝色
**解决方案**: 修正了指示器的样式和颜色设置

### 2. 跳转后选中功能
**新增功能**: 跳转到关键字定义后，自动选中目标关键字并居中显示

## 技术实现

### 1. 下划线颜色修复

#### 修复前的问题
```python
# 原始设置 - 可能导致红色下划线
self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, indicator_id, QsciScintilla.INDIC_PLAIN)
self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, indicator_id, 0x0000FF)  # 蓝色下划线
```

**问题分析**:
- `INDIC_PLAIN` 样式可能不支持自定义颜色
- 颜色格式可能不正确
- 缺少透明度设置

#### 修复后的实现
```python
# 修复后的设置 - 确保蓝色下划线
indicator_id = 1  # 使用指示器ID 1
self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, indicator_id, QsciScintilla.INDIC_UNDERLINE)
self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, indicator_id, 0xFF0000FF)  # 蓝色 ARGB格式
self.SendScintilla(QsciScintilla.SCI_INDICSETALPHA, indicator_id, 255)  # 设置透明度
```

#### 修复要点
1. **样式类型**: 使用 `INDIC_UNDERLINE` 而不是 `INDIC_PLAIN`
2. **颜色格式**: 使用 ARGB 格式 `0xFF0000FF` (Alpha=255, Red=0, Green=0, Blue=255)
3. **透明度设置**: 明确设置透明度为 255 (完全不透明)

### 2. 跳转后选中功能

#### 核心实现
```python
def _jump_to_keyword(self, keyword):
    """跳转到关键字定义"""
    try:
        # 获取关键字路径
        path = self._keyword_jumper.get_keyword_path_from_local_repository(keyword)
        if path:
            # 使用SpecifiedKeywordJumper进行跳转
            target_item = self._keyword_jumper.get_keyword_item(path, keyword)
            if target_item:
                # 跳转成功后，延迟选中目标关键字
                QTimer.singleShot(100, lambda: self._select_keyword_in_editor(keyword))
                print(f"跳转到关键字: {keyword} at {path}")
            else:
                print(f"跳转失败，未找到关键字项: {keyword}")
        else:
            print(f"未找到关键字定义: {keyword}")
            
    except Exception as e:
        print(f"跳转到关键字时出错: {e}")
```

#### 关键字选中实现
```python
def _select_keyword_in_editor(self, keyword):
    """在编辑器中选中目标关键字"""
    try:
        # 获取当前编辑器
        current_editor = self._get_current_editor()
        if not current_editor:
            return

        # 在当前编辑器中查找并选中关键字
        if hasattr(current_editor, 'findFirst'):
            # 查找关键字
            found = current_editor.findFirst(keyword, False, True, False, True)
            if found:
                # 获取选中的文本位置
                line_from, index_from, line_to, index_to = current_editor.getSelection()
                
                # 确保关键字居中显示
                current_editor.ensureLineVisible(line_from)
                current_editor.setCursorPosition(line_from, index_from)
                
                # 滚动到屏幕中间
                self._scroll_to_center(current_editor, line_from)
                
                print(f"已选中关键字: {keyword} at line {line_from + 1}")
            else:
                print(f"在编辑器中未找到关键字: {keyword}")
        
    except Exception as e:
        print(f"选中关键字时出错: {e}")
```

#### 编辑器获取逻辑
```python
def _get_current_editor(self):
    """获取当前活动的编辑器"""
    try:
        # 尝试获取文本编辑器
        text_edit = PluginRepository().find('TEXT_EDIT')
        if text_edit and hasattr(text_edit, 'isVisible') and text_edit.isVisible():
            return text_edit
        
        # 尝试获取编辑页签中的编辑器
        edit_plugin_controller = PluginRepository().find('edit_plugin_controller')
        if edit_plugin_controller and hasattr(edit_plugin_controller, '_editor'):
            editor = edit_plugin_controller._editor
            if editor and hasattr(editor, 'findFirst'):
                return editor
        
        return None
        
    except Exception as e:
        print(f"获取当前编辑器时出错: {e}")
        return None
```

#### 居中滚动实现
```python
def _scroll_to_center(self, editor, line):
    """将指定行滚动到编辑器中间"""
    try:
        if hasattr(editor, 'SendScintilla'):
            # 获取可见行数
            visible_lines = editor.SendScintilla(editor.SCI_LINESONSCREEN)
            # 计算目标行应该在的位置（屏幕中间）
            target_top_line = max(0, line - visible_lines // 2)
            # 滚动到目标位置
            editor.SendScintilla(editor.SCI_SETFIRSTVISIBLELINE, target_top_line)
        elif hasattr(editor, 'verticalScrollBar'):
            # 备用方法：使用滚动条
            editor.ensureLineVisible(line)
            
    except Exception as e:
        print(f"滚动到中间时出错: {e}")
```

## 功能特性

### 1. 蓝色下划线显示
- **正确颜色**: 现在下划线显示为蓝色而不是红色
- **清晰可见**: 使用 `INDIC_UNDERLINE` 样式确保下划线清晰可见
- **透明度控制**: 设置完全不透明确保颜色准确

### 2. 智能编辑器检测
- **多编辑器支持**: 支持文本编辑器和编辑页签中的编辑器
- **自动切换**: 根据当前活动的编辑器进行操作
- **兼容性检查**: 确保编辑器支持所需的方法

### 3. 精确关键字选中
- **全词匹配**: 使用 `findFirst` 进行精确的关键字查找
- **选中高亮**: 跳转后关键字被选中并高亮显示
- **位置记录**: 记录选中的行号和列号

### 4. 居中显示
- **屏幕中间**: 将目标关键字滚动到屏幕中间位置
- **可见性确保**: 确保关键字在可视区域内
- **平滑滚动**: 使用 Scintilla API 实现平滑滚动

## 用户体验改进

### 1. 视觉反馈
- ✅ **蓝色下划线**: 清晰标识可跳转的关键字
- ✅ **选中高亮**: 跳转后目标关键字被选中
- ✅ **居中显示**: 目标关键字显示在屏幕中间

### 2. 操作流程
1. **按住Ctrl键** → 激活关键字检测
2. **鼠标悬停** → 显示蓝色下划线
3. **点击关键字** → 跳转到定义
4. **自动选中** → 目标关键字被选中并居中显示

### 3. 多编辑器支持
- **文本编辑器**: 支持在文本编辑页签中跳转
- **编辑页签**: 支持在编辑页签中跳转
- **自动适配**: 根据当前活动编辑器自动选择

## 技术要点

### 1. 指示器配置
```python
# 关键配置参数
indicator_id = 1                                    # 指示器ID
INDIC_UNDERLINE                                     # 下划线样式
0xFF0000FF                                          # ARGB蓝色
255                                                 # 完全不透明
```

### 2. 延迟执行
```python
# 使用QTimer延迟执行选中操作
QTimer.singleShot(100, lambda: self._select_keyword_in_editor(keyword))
```
**原因**: 跳转操作需要时间完成，延迟100ms确保跳转完成后再执行选中

### 3. 查找参数
```python
# findFirst参数说明
current_editor.findFirst(
    keyword,    # 查找的文本
    False,      # 是否正则表达式
    True,       # 是否区分大小写
    False,      # 是否全词匹配
    True        # 是否从当前位置开始
)
```

### 4. 滚动计算
```python
# 居中滚动计算
visible_lines = editor.SendScintilla(editor.SCI_LINESONSCREEN)
target_top_line = max(0, line - visible_lines // 2)
```

## 错误处理

### 1. 编辑器检测
- 检查编辑器是否存在
- 检查编辑器是否可见
- 检查编辑器是否支持所需方法

### 2. 查找失败
- 关键字未找到时的提示
- 跳转失败时的错误处理
- 编辑器不支持时的降级处理

### 3. 异常捕获
- 所有关键操作都有异常处理
- 详细的错误日志输出
- 不影响其他功能的正常使用

## 总结

这次修复解决了两个重要问题：

1. **下划线颜色**: 从红色修正为蓝色，提供正确的视觉反馈
2. **跳转选中**: 跳转后自动选中目标关键字并居中显示

现在用户可以享受完整的关键字跳转体验：
- ✅ 蓝色下划线标识可跳转关键字
- ✅ Ctrl+点击跳转到定义
- ✅ 自动选中目标关键字
- ✅ 居中显示确保可见性

这大大提升了代码导航的用户体验！🎉
