*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-5623422 双上联场景下，复位基站__RAN-5623422	Comment	${almStart}	查询基站当前告警_多模	${GNODEB}			
	Comment	复位基站_多模	${GNODEB}				
	Comment	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	SYS
	...	BasestationInitial					
	Comment	sleep	1200				
	Comment	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5623421 双上联场景下，复位基带板__RAN-5623421	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	获取实例化单板别名_多模	${GNODEB}	VBP			
	: FOR	${board}	IN	@{boards}			
	\	复位VBP_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]						
							
RAN-5623423 双上联场景下，复位PB__RAN-5623423__RAN-5623053	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]						
							
RAN-5623052 掉电复位PB单板__RAN-5623052	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	掉电复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]						
							
RAN-5586686 硬复位PB单板__RAN-5586686__RAN-5586656	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	硬复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]						
							
RAN-5623424 双上联场景下，复位pRRU__RAN-5623424	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	: FOR	${board}	IN	@{boards}			
	\	复位PRRU_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5586698 级联场景下复位最后1级PB__RAN-5586698	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	PB_11-instance	PB_31-instance			
	: FOR	${board}	IN	@{boards}			
	\	复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5623056 级联场景下复位第1级PB__RAN-5623056	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	PB_10-instance	PB_30-instance			
	: FOR	${board}	IN	@{boards}			
	\	复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5623055 级联场景下掉电复位最后1级PB单板__RAN-5623055	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	PB_11-instance	PB_31-instance			
	: FOR	${board}	IN	@{boards}			
	\	掉电复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5623054 级联场景下掉电复位第1级PB单板__RAN-5623054	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	PB_10-instance	PB_30-instance			
	: FOR	${board}	IN	@{boards}			
	\	掉电复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5586682 级联场景下硬复位最后1级PB单板__RAN-5586682	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	PB_11-instance	PB_31-instance			
	: FOR	${board}	IN	@{boards}			
	\	硬复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]						
							
RAN-5586700 级联场景下硬复位第1级PB单板__RAN-5586700	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	PB_10-instance	PB_30-instance			
	: FOR	${board}	IN	@{boards}			
	\	硬复位PB_多模	${board}				
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardInitial		
	sleep	900					
	Wait Until Keyword Succeeds	8min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]						
							
RAN-5623418 双上联场景下，闭塞解闭塞小区__RAN-5623418	@{nrCellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{nrCellList}			
	\	闭塞NR小区_多模	${cell}				
	sleep	30					
	: FOR	${cell}	IN	@{nrCellList}			
	\	${status1}	查询NR小区管理状态_多模	${cell}			
	\	Should be true	'${status1}'=='Locked'				
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	: FOR	${cell}	IN	@{nrCellList}			
	\	解闭塞NR小区_多模	${cell}				
	sleep	30					
	: FOR	${cell}	IN	@{nrCellList}			
	\	${status2}	查询NR小区管理状态_多模	${cell}			
	\	Should be true	'${status2}'=='Unlocked'				
	sleep	300					
	: FOR	${cell}	IN	@{nrCellList}			
	\	确认NR小区状态正常_多模	${cell}				
	闭塞所有LTE小区						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	解闭塞所有LTE小区						
	sleep	300					
	@{lteCellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cell}	IN	@{lteCellList}			
	\	确认EUtran小区状态正常_多模	${cell}				
	[Teardown]	恢复环境					
							
RAN-5623427 双上联场景下，阻塞解阻塞基带板__RAN-5623427	${almStart}	查询基站当前告警_多模	${GNODEB}				
	@{boards}	create list	VBP_1_4-instance	VBP_1_8-instance			
	: FOR	${board}	IN	@{boards}			
	\	闭塞VBP_多模	${board}				
	\	sleep	20				
	\	${status}	查询单板信息_多模	${board}	adminState		
	\	should be true	'${status}' == 'Block'				
	\	Run Keyword And Continue On Failure	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardBlocked	
	\	sleep	10				
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	: FOR	${board}	IN	@{boards}			
	\	解闭塞VBP_多模	${board}				
	\	sleep	20				
	\	${status1}	查询单板信息_多模	${board}	adminState		
	\	should be true	'${status1}' == 'Unblock'				
	@{boards}	create list	VBP_1_3-instance	VBP_1_7-instance			
	: FOR	${board}	IN	@{boards}			
	\	闭塞VBP_多模	${board}				
	\	sleep	20				
	\	${status}	查询单板信息_多模	${board}	adminState		
	\	should be true	'${status}' == 'Block'				
	\	Run Keyword And Continue On Failure	确认告警在当前告警中_多模	${GNODEB}	${board}	BoardBlocked	
	\	sleep	10				
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	: FOR	${board}	IN	@{boards}			
	\	解闭塞VBP_多模	${board}				
	\	sleep	20				
	\	${status1}	查询单板信息_多模	${board}	adminState		
	\	should be true	'${status1}' == 'Unblock'				
	Wait Until Keyword Succeeds	10min	10sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	[Teardown]	恢复环境					
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建UE对象	${CPE4}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除UE对象	${CPE4}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
恢复环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	240					
