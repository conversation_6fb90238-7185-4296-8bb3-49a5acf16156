@startuml AI生成RF脚本系统框架

skinparam backgroundColor white
skinparam componentStyle rectangle
skinparam ArrowColor #666666
skinparam NoteBorderColor #999999
skinparam NoteFontSize 11

rectangle "用户界面层" as UI #LightSkyBlue {
  [用户输入模块] as UI1 #PaleTurquoise
  [结果展示模块] as UI2 #PaleTurquoise
  [配置管理模块] as UI3 #PaleTurquoise
}

rectangle "业务逻辑层" as BL #PaleGreen {
  [任务调度器] as BL1 #E0FFE0
  [用例分析器] as BL2 #E0FFE0
  [提示词生成器] as BL3 #E0FFE0
  [结果处理器] as BL4 #E0FFE0
}

rectangle "AI服务层" as AI #LightPink {
  [模型选择器] as AI1 #FFF0F5
  [AI请求构造器] as AI2 #FFF0F5
  [响应解析器] as AI3 #FFF0F5
}

rectangle "数据访问层" as DA #Khaki {
  [相似用例检索] as DA1 #FFFACD
  [关键字库访问] as DA2 #FFFACD
  [历史记录管理] as DA3 #FFFACD
}

rectangle "外部服务" as ES #Lavender {
  [星云大模型] as ES1 #F0E6FF
  [ZTE AI Studio] as ES2 #F0E6FF
  [MongoDB数据库] as ES3 #F0E6FF
}

' 连接关系
UI1 -[#3070B0]-> BL1 : 提交生成任务
BL1 -[#50A050]-> BL2 : 分析用例需求
BL2 -[#50A050]-> DA1 : 检索相似用例
BL2 -[#50A050]-> DA2 : 获取可用关键字
BL2 -[#50A050]-> BL3 : 传递分析结果
BL3 -[#50A050]-> AI1 : 选择合适模型
AI1 -[#D06080]-> AI2 : 构造AI请求
AI2 -[#D06080]-> ES1 : 调用星云模型
AI2 -[#D06080]-> ES2 : 调用ZTE AI Studio
AI3 <-[#D06080]- ES1 : 接收模型响应
AI3 <-[#D06080]- ES2 : 接收模型响应
AI3 -[#D06080]-> BL4 : 传递AI响应
BL4 -[#50A050]-> DA3 : 保存生成记录
BL4 -[#50A050]-> UI2 : 展示生成结果
UI3 <-[#3070B0]-> ES3 : 读写配置信息
DA1 -[#B0A040]-> ES3 : 查询用例库
DA2 -[#B0A040]-> ES3 : 查询关键字库
DA3 -[#B0A040]-> ES3 : 存储历史记录

note right of BL3 #FFFFD0
  根据用例分析结果和相似用例
  构造专业化提示词
  - 新手模式：直接复用相似用例
  - 专家模式：基于示例生成新脚本
end note

note right of AI2 #FFE0E0
  根据不同模型特点
  调整请求参数和格式
  - 温度参数控制生成随机性
  - 系统提示词引导模型行为
end note

note right of BL4 #E0FFE0
  处理AI响应，提取有效代码
  添加标识信息，格式化输出
  - 确保符合RF语法规范
  - 使用已有关键字库
end note

legend
  <b>颜色图例</b>
  <back:LightSkyBlue>   </back> 用户界面层
  <back:PaleGreen>   </back> 业务逻辑层
  <back:LightPink>   </back> AI服务层
  <back:Khaki>   </back> 数据访问层
  <back:Lavender>   </back> 外部服务
endlegend

@enduml