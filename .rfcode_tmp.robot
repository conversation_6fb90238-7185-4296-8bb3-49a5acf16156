*** Settings ***
Suite Setup	创建GSM小区配置
Suite Teardown	删除GSM配置
Test Teardown
Resource	../variable/resource.tsv
Resource	../../../../../userkeywords/basic_multi/resource.tsv
Resource	../../../../../userkeywords/feature_multi/resource.tsv
Resource	../../../../../userkeywords/resource.tsv
Resource	../../../../../userkeywords4g/basic/resource.tsv


*** Test Cases ***
GSM干扰测量
	${measureType}	Set Variable	2009
	${localFilePath}	Set Variable	D:\\measureGsmCellPM\\gsm${measureType}.csv
	GSM小区性能测量_多模	${measureType}	${localFilePath}

GSM全频段扫描
	${measureType}	Set Variable	2010
	${localFilePath}	Set Variable	D:\\measureGsmCellPM\\gsm${measureType}.csv
	GSM小区性能测量_多模	${measureType}	${localFilePath}

GSM小区测量查询
	#GSM干扰测量参数准备
	${measureType}	Set Variable	2009
	${localFilePath}	Set Variable	D:\\measureGsmCellPM\\gsm${measureType}.csv
	${ftpPath}	4G-查询GSM小区性能测量任务	${ENODEB}	${measureType}
	4G-下载GSM性能测量结果	${ENODEB}	${ftpPath}	${localFilePath}
	#GSM全频段扫描
	${measureType}	Set Variable	2010
	${localFilePath}	Set Variable	D:\\measureGsmCellPM\\gsm${measureType}.csv
	${ftpPath}	4G-查询GSM小区性能测量任务	${ENODEB}	${measureType}
	4G-下载GSM性能测量结果	${ENODEB}	${ftpPath}	${localFilePath}


*** Keywords ***
创建GSM小区配置
	创建GSM通道资源_多模	${ENODEB}	${RRU1}	1	${GCHANNEL1}	1	paNo=2
	创建GSM小区_多模	${ENODEB}	${GCELL1}	${GCHANNEL1}	1
	创建GSM载频_多模	${ENODEB}	${GCELL1}	${GTRX1}	1	943.8
	增量同步OMMR_多模	${ENODEB}	G
	4G-同步配置	${ENODEB}
	等待基站下所有GSM小区操作状态解闭_多模	${ENODEB}

删除GSM配置
	删除GSM载频_多模	${GTRX1}
	删除GSM小区_多模	${GCELL1}
	删除GSM通道资源_多模	${GCHANNEL1}
	增量同步OMMR_多模	${ENODEB}	G
	4G-同步配置	${ENODEB}

GSM小区性能测量_多模
	[Arguments]	${measureType}	${localFilePath}
	#GSM干扰测量参数准备
	${result}	4G-查询网元时间	${ENODEB}
	${startTime}	${endTime}	4G-获取GSM性能测量时间	${result}	1200
	#GSM干扰测量
	4G-GSM小区性能测量	${ENODEB}	1	${measureType}	0	${startTime}	${endTime}
	sleep	1230
	4G-GSM小区性能测量	${ENODEB}	1	${measureType}	1	${startTime}	${endTime}
	sleep	15
	${ftpPath}	4G-查询GSM小区性能测量任务	${ENODEB}	${measureType}
	4G-下载GSM性能测量结果	${ENODEB}	${ftpPath}	${localFilePath}


