# coding=utf-8
'''
Created on 2019年10月23日

@author: 10240349
'''
from _functools import partial

from PyQt5 import QtWidgets
from PyQt5.Qt import Qt, QCursor
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtWidgets import QPushButton, QHBoxLayout, QLabel, QLineEdit, \
    QVBoxLayout, QCheckBox

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.run.CmdGenerator import CmdGenerator
from controller.system_plugin.run.ExectiveTestcase import ExectiveTestcase
from controller.system_plugin.run.LogTextEditor import LogTextEditor
from controller.system_plugin.run.PrintTraceLogSwitch import PrintTraceLogSwitch
from controller.test_runner.BtnAction import BtnAction
from resources.Loader import Loader
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.ModifiedItemRepository import ModifiedItemRepository
from utility.PluginRepository import PluginRepository
from utility.UIRepository import UIRepository
from view.common.MessageBox import MessageBox


class RunPlugin(object):

    def __init__(self, parent):
        self._parent_window = parent
        self._action = BtnAction()

    def load(self):
        v_layout = QVBoxLayout()
        for layout in (self._set_button(),
                       self._set_arguments_area(),
                       self._set_tag_area(v_layout),
                       self._set_progress_bar_area(v_layout),
                       self._set_summary_log_window(v_layout),
                       self._set_trace_log_window(v_layout)):
            v_layout.addLayout(layout)
        return v_layout

    def _set_button(self):
        layout = QHBoxLayout()
        self._set_start_btn(layout)
        self._set_stop_btn(layout)
        self._set_pause_btn(layout)
        self._set_continue_btn(layout)
        self._set_log_btn(layout)
        self._set_details_btn(layout)
        layout.addStretch()
        self._set_pause_on_failure_area(layout)
        self._set_real_time_log_area(layout)
        PrintTraceLogSwitch()._set_trace_log_switch(layout)
        self._set_btn_init_status()
        return layout

    def _set_start_btn(self, layout):
        self._start_btn = QPushButton(LanguageLoader().get('START'), self._parent_window)
        self._start_btn.setProperty('name', 'succ') # 设置样式选择器
        self._start_btn.setFixedSize(80, 25)
        self._start_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._start_btn.setIcon(QIcon(Loader().get_path('START_BTN')))
        self._start_btn.clicked.connect(partial(RunPlugin.run, self))
        layout.addWidget(self._start_btn)

    def _set_stop_btn(self, layout):
        self._stop_btn = QPushButton(LanguageLoader().get('STOP'), self._parent_window)
        self._stop_btn.setProperty('name', 'danger') # 设置样式选择器
        self._stop_btn.setFixedSize(80, 25)
        self._stop_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._stop_btn.setIcon(QIcon(Loader().get_path('STOP_BTN')))
        self._stop_btn.clicked.connect(partial(self._action.stop, self))
        layout.addWidget(self._stop_btn)

    def _set_pause_btn(self, layout):
        self._pause_btn = QPushButton(LanguageLoader().get('PAUSE'), self._parent_window)
        self._pause_btn.setProperty('name', 'warn') # 设置样式选择器
        self._pause_btn.setFixedSize(80, 25)
        self._pause_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._pause_btn.setIcon(QIcon(Loader().get_path('PAUSE_BTN')))
        self._pause_btn.clicked.connect(partial(self._action.pause, self))
        layout.addWidget(self._pause_btn)

    def _set_continue_btn(self, layout):
        self._continue_btn = QPushButton(LanguageLoader().get('CONTINUE'), self._parent_window)
        self._continue_btn.setProperty('name', 'succ') # 设置样式选择器
        self._continue_btn.setFixedSize(80, 25)
        self._continue_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._continue_btn.setIcon(QIcon(Loader().get_path('CONTINUE_BTN')))
        self._continue_btn.clicked.connect(partial(self._action.continue_, self))
        layout.addWidget(self._continue_btn)

    def _set_log_btn(self, layout):
        self._log_btn = QPushButton(LanguageLoader().get('LOG'), self._parent_window)
        self._log_btn.setProperty('name', 'primary') # 设置样式选择器
        self._log_btn.setFixedSize(80, 25)
        self._log_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._log_btn.setIcon(QIcon(Loader().get_path('LOG_BTN')))
        self._log_btn.clicked.connect(self._action.get_log)
        layout.addWidget(self._log_btn)

    def _set_details_btn(self, layout):
        self._details_btn = QPushButton(LanguageLoader().get('DETAILS'), self._parent_window)
        self._details_btn.setProperty('name', 'primary') # 设置样式选择器
        self._details_btn.setFixedSize(90, 25)
        self._details_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._details_btn.setIcon(QIcon(Loader().get_path('DETAILS')))
        self._details_btn.clicked.connect(partial(ExectiveTestcase().show, self))
        layout.addWidget(self._details_btn)

    def _set_arguments_area(self):
        layout = QHBoxLayout()
        label = QLabel('arguments:')
        self._line = MyLineEdit()
        self._line.setText(SystemSettings().read('DEFAULT_ARGUMENT'))

        layout.addWidget(label)
        layout.addWidget(self._line)
        return layout

    def _set_tag_area(self, layout):
        layout = QHBoxLayout()
        self._only_run_check = QCheckBox()
        only_run_label = QLabel('only run tests with these tags:')
        self._only_run_line = QLineEdit()
        self._skip_check = QCheckBox()
        skip_label = QLabel('skip tests with these tags:')
        self._skip_line = QLineEdit()
        layout.addWidget(self._only_run_check)
        layout.addWidget(only_run_label)
        layout.addWidget(self._only_run_line)
        layout.addWidget(self._skip_check)
        layout.addWidget(skip_label)
        layout.addWidget(self._skip_line)
        return layout

    def _set_progress_bar_area(self, layout):
        layout = QHBoxLayout()
        self._log_text_editor = LogTextEditor()
        self._progress_editor = self._log_text_editor.get_progress_editor()
        self._progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")
        self._progress_editor.setFocusPolicy(Qt.NoFocus)
        UIRepository().add('progress_editor', self._progress_editor)
        layout.addWidget(self._progress_editor)
        return layout

    def _set_summary_log_window(self, layout):
        layout = QHBoxLayout()
        self._log_summary_editor = self._log_text_editor.get_log_summary_editor()
        self._log_summary_editor.setFont(QFont((SystemSettings().get_value('FONT')),
                                               SystemSettings().get_value('LOG_FONT_SIZE')))
        UIRepository().add('log_summary_editor', self._log_summary_editor)
        self._log_summary_editor.setFocusPolicy(Qt.NoFocus)
        layout.addWidget(self._log_summary_editor)
        return layout

    def _set_trace_log_window(self, layout):
        layout = QHBoxLayout()
        self._log_trace_editor = self._log_text_editor.get_log_trace_editor()
        self._log_trace_editor.setFont(QFont((SystemSettings().get_value('FONT')),
                                             SystemSettings().get_value('LOG_FONT_SIZE')))
        UIRepository().add('log_trace_editor', self._log_trace_editor)
        self._log_trace_editor.setFocusPolicy(Qt.NoFocus)
        layout.addWidget(self._log_trace_editor)
        return layout

    @staticmethod
    def run(this):
        if ModifiedItemRepository().find('MODIFIED_ITEM'):
            reply = MessageBox().show_warning(LanguageLoader().get('RUNING_SAVE_TIPS'))
            if reply == QtWidgets.QMessageBox.Yes: # 直接保存
                SignalDistributor().save_all()
                RunPlugin.run_testcase(this)
        else:
            RunPlugin.run_testcase(this)

    @staticmethod
    def run_testcase(this):
        cmd_generator, cmds = RunPlugin._get_cmds(this)
        if cmds:
            PluginRepository().add('SHOW_REAL_TIME_LOG', this._get_real_time_log_status())
            PluginRepository().add('PAUSE_ON_FAILURE', this._get_pause_on_failure_status())
            this._init_testcase_icon()
            cmd_generator.assemble_executive_testcase()
            btn_list = this._set_btn_status_in_run()
            this._action.run(cmds, btn_list)
            this._set_testcase_prepare_icon()
            PluginRepository().add('TESTCASE_RUNNING', True)

    @staticmethod
    def _get_cmds(this):
        ExectiveTestcase().set()
        cmd_generator = CmdGenerator(this._line.text(),
                                     this._set_only_run_tag(),
                                     this._set_skip_tag())
        return cmd_generator, cmd_generator.get()

    def _set_testcase_prepare_icon(self):
        item_dict = ExecutiveTestCaseRepository().find('EXECUTIVE_ITEM')
        for item in item_dict.values():
            item.setIcon(0, QIcon(Loader().get_path('PREPARE')))

    def _init_testcase_icon(self):
        item_dict = ExecutiveTestCaseRepository().find('OLD_EXECUTIVE_ITEM_')
        if item_dict:
            for item in item_dict.values():
                item.setIcon(0, QIcon(Loader().get_path('TESTCASE')))

    def _set_btn_init_status(self):
        self._start_btn.setEnabled(True)
        self._pause_btn.setEnabled(False)
        self._continue_btn.setEnabled(False)
        self._stop_btn.setEnabled(False)
        self._log_btn.setEnabled(False)
#         self._set_btn_disabled_style([self._pause_btn, self._continue_btn, self._stop_btn, self._log_btn])

    def _set_btn_status_in_run(self):
        self._start_btn.setEnabled(False)
        self._stop_btn.setEnabled(True)
        self._pause_btn.setEnabled(True)
        self._continue_btn.setEnabled(False)
#         self._set_btn_disabled_style([self._start_btn, self._continue_btn])
        self._log_btn.setEnabled(PluginRepository().find('SHOW_REAL_TIME_LOG'))
        return [self._start_btn, self._stop_btn,
                self._pause_btn, self._continue_btn, self._log_btn]

    def _set_btn_disabled_style(self, btnObjList):
        for btn in btnObjList:
            btn.setStyleSheet("border: 1px solid rgb(12 , 138 , 235);color:#909399")

    def _set_only_run_tag(self):
        if self._only_run_check.isChecked():
            return self._only_run_line.text()
        else:
            return None

    def _set_skip_tag(self):
        if self._skip_check.isChecked():
            return self._skip_line.text()
        else:
            return None

    def _set_real_time_log_area(self, layout):
        self._real_time_log_check = QCheckBox()
        
        layout.addWidget(self._real_time_log_check)
        label = QLabel(LanguageLoader().get('REAL_TIME_LOG_SWITCH'))
        layout.addWidget(label)
        self._real_time_log_check.setChecked(True)
#         self._real_time_log_check.setVisible(False)
#         label.setVisible(False)

    def _get_real_time_log_status(self):
        if self._real_time_log_check.isChecked():
            return True
        else:
            return False

    def _set_pause_on_failure_area(self, layout):
        self._pause_on_failure_check = QCheckBox()
        layout.addWidget(self._pause_on_failure_check)
        label = QLabel(LanguageLoader().get('PAUSE_ON_FAILURE'))
        layout.addWidget(label)

    def _get_pause_on_failure_status(self):
        if self._pause_on_failure_check.isChecked():
            return True
        else:
            return False


class MyLineEdit(QLineEdit):

    def __init__(self):
        super(MyLineEdit, self).__init__()

    @staticmethod
    def _text_changed(text):
        SignalDistributor().modify_table_item_event(text)

    def focusInEvent(self, event):
        return super().focusInEvent(event)

    def focusOutEvent(self, event):
        SystemSettings().write('DEFAULT_ARGUMENT', self.text())
        return super().focusOutEvent(event)
