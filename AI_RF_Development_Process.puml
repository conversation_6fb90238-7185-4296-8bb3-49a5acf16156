@startuml AI辅助RF新用例开发流程

skinparam backgroundColor white
skinparam activityBorderColor #666666
skinparam activityBackgroundColor #FAFAFA
skinparam activityDiamondBackgroundColor #E6F5FF
skinparam activityDiamondBorderColor #3070B0
skinparam noteBorderColor #999999
skinparam noteFontSize 11
skinparam ArrowColor #666666

title AI辅助RobotFramework新用例开发流程

' 定义颜色
!define ANALYSIS_COLOR #E1F5FE
!define IMPLEMENTATION_COLOR #E8F5E9
!define VERIFICATION_COLOR #FFF8E1

' 主流程
partition "1. 新用例分析" #ANALYSIS_COLOR {
  start
  
  :接收新用例需求;
  note right #E1F5FE
    包含:
    - 用例名称
    - 测试步骤
    - 预期结果
    - 预置条件
  end note
  
  partition "用例分析" #D4EBF9 {
    :分析测试用例意图;
    note right #D4EBF9
      使用5GNR和多模基站专业领域知识
      以及算力服务器领域知识分析
    end note
    
    :分析操作和预期结果;
    
    :识别环境设置和恢复需求;
    
    :识别预置条件中的测试步骤;
  }
  
  partition "代码块复用分析" #D4EBF9 {
    :查找相似测试步骤;
    
    :分析每个步骤的代码复用可能性;
    note right #D4EBF9
      步骤x分析:
      [操作]：Documentation中操作
      [预期结果]：Documentation中预期结果
      [分析结果]：代码复用的原因
      [操作拆解]：操作拆解步骤
      [可用关键字]:从示例代码中提取的关键字
    end note
    
    :提取可用关键字列表;
  }
}

partition "2. 用例实现" #IMPLEMENTATION_COLOR {
  partition "自动化开发" #D8ECD9 {
    :设计RF代码结构;
    note right #D8ECD9
      参考已有用例的风格
      设计合理的代码结构
    end note
    
    :复用已有代码块;
    
    :保持[Documentation]原状;
    
    :添加Log Step标记;
    note right #D8ECD9
      格式: Log    Step N <步骤|操作>
      与Documentation保持一致
    end note
    
    :添加合适的[Setup]和[Teardown];
  }
  
  partition "结果输出" #D8ECD9 {
    :确保符合RF3语法规范;
    note right #D8ECD9
      - FOR循环不能嵌套
      - 兼容Python3.7
    end note
    
    :生成最终RF代码;
  }
}

partition "3. 结果检查" #VERIFICATION_COLOR {
  partition "基础检查" #F5ECD1 {
    :过程检查;
    note right #F5ECD1
      回顾整个用例实现过程
      排查是否按照用例分析、代码复用分析后
      再生成代码
    end note
  }
  
  partition "关键字检查" #F5ECD1 {
    :检查关键字来源;
    note right #F5ECD1
      严格检查新用例引用的关键字
      在提供的脚本中有定义或使用
    end note
    
    if (所有关键字都已定义?) then (是)
      :输出最终验证通过的RF代码;
    else (否)
      :修正未定义关键字;
      note right #FFEBEE
        避免大模型幻觉
        不创造新关键字
      end note
      backward :返回修改;
    endif
  }
}

stop

legend
  <b>颜色图例</b>
  <back:ANALYSIS_COLOR>   </back> 新用例分析阶段
  <back:IMPLEMENTATION_COLOR>   </back> 用例实现阶段
  <back:VERIFICATION_COLOR>   </back> 结果检查阶段
endlegend

@enduml





