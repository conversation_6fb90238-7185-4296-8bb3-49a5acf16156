# encoding=utf-8
'''
Create on  2019年10月25日

@author:10240349
'''
ZH = \
    {'LOGO_TEXT': 'RFCode',
     'FILE': '文件',
     'TOOLS': '工具',
     'PLUGIN': '插件',
     'NEW_PROJECT': '新建工程',
     'OPEN_DIRECTORY': '打开工程',
     'CLEAR_HISTORY_PROJECT': '清除历史工程',
     'SAVE': '保存',
     'SAVE_ALL': '全部保存',
     'INTRODUCTION': '简介',
     # Tool标签
     'SEARCH_KEYWORDS': '搜索关键字',
     'SEARCH_TESTCASES': '搜索测试用例',
     'PREFERENCES': '偏好设置',
     'ABOUT_RFCODE': '关于RFCode',
     'PRODUCTION_INSTRUCTION': '软件使用说明书',
     'RESTART_INFO': '请重启软件，以便配置生效！',

     'STYLE': '风格',
     'MENU_BAR_DOC': '系统主窗口的菜单栏，支持对菜单栏的定制',
     'TESTCASE_INFO': '请勾选执行用例！',
     'LOGO_IS_NOT_EXIST': 'LOGO图片不存在，请检查！',
     'PATH_ERROR': '工程路径不存在，请重新选择。',
     'REPEAT_INFO': '新建项名字重复。',
     'LOADING': '加载中...',
     'COPY': '复制',
     'PASTE': '粘贴',
     'RENAME': '重命名',
     'REFRESH': '刷新',
     'DELETE': '删除',
     'CHANGE_FORMAT': '改变格式',
     'MOVE_UP': '上移                     Ctrl-Up',
     'MOVE_DOWN': '下移                 Ctrl-Down',
     'INFORMATION': '信息',
     'WARN': '警告',
     'QUESTION': '提问',
     'ERROR': '严重错误',
     'NEW_TESTCASE': '新建用例',
     'NEW_TESTSUITE': '新建测试套',
     'SET_HOME_PAGE': '设置为主工作区 （快速跳转）',
     'SET_HOME_PAGE_TOOLTIP': '设置主页后，点击主页按钮，可以快速跳转到主页。',
     'NEW_TESTSUITE_TOOLTIP': '新建测试套，可以将多个用例组合在一起，方便执行。',
     'NEW_TESTCASE_TOOLTIP': '新建用例，可以将多个关键字组合在一起，方便执行。',
     'NEW_RESOURCE_TOOLTIP': '新建资源文件，可以将多个变量和关键字组合在一起，方便使用。',
     'NEW_KEYWORD_TOOLTIP': '新建关键字，可以将多个关键字组合在一起，方便使用。',
     'NEW_SCALAR_TOOLTIP': '新建单值变量，可以将多个单值变量组合在一起，方便使用。',
     'NEW_LIST_VARIABLE_TOOLTIP': '新建多值变量，可以将多个多值变量组合在一起，方便使用。',
     'NEW_DICT_VARIABLE_TOOLTIP': '新建字典变量，可以将多个字典变量组合在一起，方便使用。',
     'NEW_DIRECTORY_TOOLTIP': '新建文件夹，可以将多个文件组合在一起，方便管理。',
     'EXPAND_ALL_TOOLTIP': '展开所有文件夹。',
     'COLLAPSE_ALL_TOOLTIP': '折叠所有文件夹。',
     'ADD_SUITE_TOOLTIP': '新增测试套，可以将多个用例组合在一起，方便执行。',
     'ADD_DIRECTORY_TOOLTIP': '新增目录，可以将多个文件组合在一起，方便管理。',
     'ADD_RESOURCE_TOOLTIP': '新增资源文件，可以将多个变量和关键字组合在一起，方便使用。',
     'ADD_TESTCASE_TOOLTIP': '新增用例，可以将多个关键字组合在一起，方便执行。',
     'ADD_KEYWORD_TOOLTIP': '新增关键字，可以将多个关键字组合在一起，方便使用。',
     'ADD_SCALAR_TOOLTIP': '新增单值变量，可以将多个单值变量组合在一起，方便使用。',
     'ADD_LIST_TOOLTIP': '新增多值变量，可以将多个多值变量组合在一起，方便使用。',
     'ADD_DICT_TOOLTIP': '新增字典变量，可以将多个字典变量组合在一起，方便使用。',
     'NEW_TESTSUITE': '新建测试套',
     'NEW_TESTCASE': '新建用例',
     'NEW_RESOURCE': '新建资源文件',
     'NEW_KEYWORD': '新建关键字',
     'NEW_SCALAR': '新建单值变量',
     'NEW_LIST_VARIABLE': '新建多值变量',
     'NEW_DICT_VARIABLE': '新建字典变量',
     'NEW_DIRECTORY': '新建文件夹',
     'EXPAND_ALL': '全部展开',
     'COLLAPSE_ALL': '全部折叠',
     'ADD_SUITE': '新增测试套',
     'ADD_DIRECTORY': '新增目录',
     'ADD_RESOURCE': '新增资源文件',
     'ADD_TESTCASE': '新增用例',
     'ADD_KEYWORD': '新增关键字',
     'ADD_SCALAR': '新增单值变量',
     'ADD_LIST': '新增多值变量',
     'ADD_DICT': '新增字典变量',
     'COPY_KEYWORD': '复制关键字',
     'CHANGE_FORMAT': '设置格式',
     'SCALAR_DIALOG_DES': '自定义变量名和变量值',
     'LIST_DIALOG_DES': '自定义变量名和值，请在单元格中输入列表元素。',
     'DICT_DIALOG_DES': '自定义变量名和值,将字典项输入到单独的单元格中。单项格式必须为“键=值”',
     'TESTCASE_DIALOG_DES': '自定义新用例名',
     'KEYWORD_DIALOG_DES': '自定义新关键字名称和参数',
     'KEYWORD_NAME': '关键字名',
     'TESTCASE_NAME': '用例名',
     'LIB_PATH': '路径',
     'KEYWORD_DES': '关键字描述',
     'TICK': '勾选',
     'UNTICK': '去勾选',
     'RUNING_SAVE_TIPS': '当前存在未保存内容,是否需要先保存再运行？',
     'CLOSE_TIPS': '请确认修改内容是否需要保存？',
     'ARGUMENTS': ("请使用管道字符分隔的参数，如'${arg1} | ${arg2}'.\n"
                   "默认值使用等号给出，最后一个参数可以是列表变量.\n"
                   "例如: '${arg1} | ${arg2}=default value | @{rest}'.\n"
                   "提示。您可以在此字段中使用变量快捷"),
     'ABOUT_RFCODE_DES':
        '''
RFCode的设计思想源于RIDE，
感谢RIDE团队和RobotFramework社区提供的开源软件和代码，
未来RFCode也会开源
RFcode的主要开发者：
 王建雄、关翔宇、叶火能、顾攀、郭国辉、张振扬、
 姜雪琦、 黄宗全、刘绪光、陈志祥、朱小杰、李培强
        ''',
     'NOT_SAVE_TIP': '用例执行时不允许保存，用例执行完毕可以保存!',
     'DELETE_TIPS': "确认要删除？",
     'ADD_SOURCE_LABEL':
     '''
请成对配置库名和库路径，注意必须成对，如：用户关键字: D:\\script_v3\\5GNR\\test\\userkeywords
    ''',

     # tabEdit
     'EDIT': '编辑',
     'TEXT_EDIT': '文件编辑',
     'RUN': '执行',
     'BASH': '命令窗口',
     "RF_ASSISTANT": 'RF助手',
     'BASH_ERROR_INFO': '打开git-bash错误，请确保您安装git并成功配置git-bash环境变量',
     'FIRST_STEP': '''第一步:
    在path环境变量中添加git-bash路径,比如：'C:\Program Files (x86)\Git'
    ''',
     'SECOND_STEP': '''第二步:
    在cmd命令行中执行git-bash命令
    ''',
     'THIRD_STEP': '''第三步:
    如果能够成功启动git-bash窗口表示配置成功.
    ''',

     # edit页面
     'SETTINGS': '显示/隐藏设置',

     # run页面
     'START': '开始',
     'STOP': '停止',
     'PAUSE': '暂停',
     'CONTINUE': '继续',
     'LOG': '日志',
     'DETAILS': '勾选的用例',
     "CONFIG_LIB_PATH": '配置',
     'DEATILS_TIP': '将要执行的用例：',
     'NO_PROJECT': '工程不存在！',
     'SYSTEM_LIB_TIP': 'rfcode中没有配置系统库！',
     'EMPTY_NAME': '名字是空的',
     'ILLEGAL_NAME': '名字是违法的，名字中不能有.\ /:*?"<>|，不能是下划线或数字和双下划线开头',
     'SEARCH_RESULT': '搜索结果为空！',
     'INVALID_PROJECT': '路径无效!',
     'FILE_EXISTED': '文件夹已存在！',
     'RUNNING_UN_CHANGE_FORMAT': "当用例运行时，不允许修改格式。",
     'TRACE_LOG': '详细log打印等级设置:',
     'FILE_CHANGED': '''
这个文件已经在文件系统中发生改变了，你想重新加载这个文件吗，点击否将会覆盖磁盘文件。
''',
     'CHANGED_FILE': '修改的文件：',
     'FILE_MONITOR': '文件监测',
     'OK': '确定',
     'CANCEL': '取消',
     'REAL_TIME_LOG_SWITCH': '打开实时日志开关',
     'TRACE_LOG_SWITCH': '关闭详细log打印开关',
     'LOG_FILE_SHOW_TIPS': '日志文件生成失败，请重跑用例',
     'PAUSE_ON_FAILURE' : '失败暂停',
     'EXPAND_LATEST_PROJECT': '展开最近打开的项目'
     }
