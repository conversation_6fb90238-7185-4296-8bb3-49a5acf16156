*** Settings ***
Suite Setup	加载配置	gNBRequest	${dataset}
Suite Teardown	删除配置
Variables	request.py
Library	Collections
Resource	../variable/resource.tsv
Resource	../../../../../../../userkeywords/basic/resource.tsv
Resource	../../../../../../../userkeywords/basic/ume/sta/sta.tsv
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv
Resource	../../../../../../../userkeywords/basic_multi/radio/enodeb_config.tsv
Resource	../../../../../../../userkeywords/basic_multi/instrument/Artifactory/artifactory.tsv
Resource	../common/es_userkeywords.tsv
Library	testlib5g.app_service.basic_multi.im.sta.MtsService
Library	../../../../../../../../testlib5g/app_service/basic_multi/aianalysis/MecSignalDeal.py
Library	testlib5g.app_service.basic_multi.support.TxRxGroupService
Resource	../../../template.tsv

*** Variables ***
${StopOnError}	${True}	# 遇到测试失败继续测试，请设置为${False}；遇到测试失败中断测试，请设置为${True}

*** Test Cases ***
RAN-3490808 [TDL]多prru场景：增强符号关断-节能前后ping时延对比__RAN-3490805
	[Teardown]
	${tdlCell5}	获取TDD LTE小区	tddCell5
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell5}	150
	#UE做业务
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell5}
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	10	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3502617 [LV共框]多prru LV混模场景NR性能验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，定点空口性能和prru功耗测试__RAN-3502606
	[Teardown]	恢复环境sleep
	${nrCell260}	获取NR小区	5101-260
	配置FDL小区1射频合并
	sleep	480
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	150
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	${M1}	${eth1PowerM1}	${eth2PowerM1}	近点PRB10%NR流量测试	${CPE260}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	近点PRB30%NR流量测试	${CPE260}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	近点PRB50%NR流量测试	${CPE260}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	近点PRB10%NR流量测试	${CPE260}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	近点PRB30%NR流量测试	${CPE260}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	近点PRB50%NR流量测试	${CPE260}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.3
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.3
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.3
	run keyword and continue on failure	should be true	${eth1PowerM1}>${dtxEth1PowerM1}
	run keyword and continue on failure	should be true	${eth1PowerM2}>${dtxEth1PowerM2}
	run keyword and continue on failure	should be true	${eth1PowerM3}>${dtxEth1PowerM3}
	run keyword and continue on failure	should be true	${eth2PowerM1}>${dtxEth2PowerM1}
	run keyword and continue on failure	should be true	${eth2PowerM2}>${dtxEth2PowerM2}
	run keyword and continue on failure	should be true	${eth2PowerM3}>${dtxEth2PowerM3}
	# 中点
	设置UE信号衰减	${CPE6}	25
	${M1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	中点PRB10%NR流量测试	${CPE260}
	${M2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	中点PRB30%NR流量测试	${CPE260}
	${M3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	中点PRB50%NR流量测试	${CPE260}
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5
	${dtxM1}	${eth1PowerM1}	${eth2PowerM1}	中点PRB10%NR流量测试	${CPE260}
	${dtxM2}	${eth1PowerM2}	${eth2PowerM2}	中点PRB30%NR流量测试	${CPE260}
	${dtxM3}	${eth1PowerM3}	${eth2PowerM3}	中点PRB50%NR流量测试	${CPE260}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.3
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.3
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.3
	run keyword and continue on failure	should be true	${eth1PowerM1}>${dtxEth1PowerM1}
	run keyword and continue on failure	should be true	${eth1PowerM2}>${dtxEth1PowerM2}
	run keyword and continue on failure	should be true	${eth1PowerM3}>${dtxEth1PowerM3}
	run keyword and continue on failure	should be true	${eth2PowerM1}>${dtxEth2PowerM1}
	run keyword and continue on failure	should be true	${eth2PowerM2}>${dtxEth2PowerM2}
	run keyword and continue on failure	should be true	${eth2PowerM3}>${dtxEth2PowerM3}

多prru场景：增强符号关断（时隙关断）-定点空口性能和prru功耗测试（SA）__RAN-2057172
	[Documentation]	RAN-3502617已实现
	log	RAN-3502606已实现

RAN-3502614 [LV共框]多prru LV混模场景TDL性能验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，定点空口性能和AAU/RRU功耗测试__RAN-3502609
	[Teardown]	恢复环境sleep
	${tdlCell5}	获取TDD LTE小区	tddCell5
	配置FDL小区1射频合并
	sleep	480
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell5}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell5}
	#UE做业务
	${M1}	${eth1PowerM1}	${eth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	近点PRB10%TDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	近点PRB10%TDL流量测试	${CPE6}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	近点PRB10%TDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2
	# 中点
	设置UE信号衰减	${CPE6}	25
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	近点PRB10%TDL流量测试	${CPE6}
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5
	${M1}	${eth1PowerM1}	${eth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	近点PRB10%TDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	近点PRB10%TDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2

RAN-3490894 [FDL]多prru场景：增强符号关断-定点空口性能和AAU/RRU功耗测试__RAN-3490890
	[Teardown]	恢复环境sleep
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区2射频合并
	sleep	480
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	#UE做业务
	${M1}	${eth1PowerM1}	${eth2PowerM1}	小区近点PRB10%FDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	小区近点PRB30%FDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	小区近点PRB50%FDL流量测试	${CPE6}
	NR小区符号关断节能	2	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	4	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	小区近点PRB10%FDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	小区近点PRB30%FDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	小区近点PRB50%FDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2
	# 中点
	设置UE信号衰减	${CPE6}	25
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	小区中点PRB10%FDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	小区中点PRB30%FDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	小区中点PRB50%FDL流量测试	${CPE6}
	NR小区符号关断节能	2	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	4	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	2
	${M1}	${eth1PowerM1}	${eth2PowerM1}	小区中点PRB10%FDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	小区中点PRB30%FDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	小区中点PRB50%FDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2

RAN-3490893 [FDL]多prru场景：增强符号关断-节能前后ping时延对比__RAN-3490889
	[Teardown]	恢复环境sleep
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区2射频合并
	sleep	480
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	#UE做业务
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3507373 [ITRAN-FDL]超级小区场景：增强符号关断-节能前后ping时延对比__RAN-3507373
	${fdlCell2}	获取FDD LTE小区	5101-2
	${subCellLocalIdList}	create list	1
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	2	${subCellLocalIdList}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	#UE做业务
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3521062 [ITRAN-TDL]超级小区场景：增强符号关断-节能前后ping时延对比__RAN-3521062
	[Teardown]
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell5}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell5}
	#UE做业务
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	10	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3502612 [LV共框]多prru LV混模场景FDL性能验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，定点空口性能和AAU/RRU功耗测试__RAN-3502608
	[Teardown]	恢复环境sleep
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区2射频合并
	sleep	480
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	#UE做业务
	${M1}	${eth1PowerM1}	${eth2PowerM1}	小区近点PRB10%FDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	小区近点PRB30%FDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	小区近点PRB50%FDL流量测试	${CPE6}
	NR小区符号关断节能	2	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	4	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	小区近点PRB10%FDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	小区近点PRB30%FDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	小区近点PRB50%FDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2
	# 中点
	设置UE信号衰减	${CPE6}	25
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	小区中点PRB10%FDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	小区中点PRB30%FDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	小区中点PRB50%FDL流量测试	${CPE6}
	NR小区符号关断节能	2	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	4	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	2
	${M1}	${eth1PowerM1}	${eth2PowerM1}	小区中点PRB10%FDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	小区中点PRB30%FDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	小区中点PRB50%FDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2

RAN-3502613 [LV共框]多prru LV混模场景TDL时延验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，节能前后ping时延对比__RAN-3502610
	${tdlCell5}	获取TDD LTE小区	tddCell5
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell5}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell5}
	#UE做业务
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	10	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	10	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3507374 [ITRAN-TDL]超级小区场景：增强符号关断-定点空口性能和AAU/RRU功耗测试__RAN-3507374
	[Teardown]	恢复环境
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell5}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell5}
	#UE做业务
	${M1}	${eth1PowerM1}	${eth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	近点PRB30%TDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	近点PRB50%TDL流量测试	${CPE6}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	10	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	近点PRB30%TDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	近点PRB50%TDL流量测试	${CPE6}
	Comment	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2
	# 中点
	设置UE信号衰减	${CPE6}	25
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	中点PRB10%TDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	中点PRB30%TDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	中点PRB50%TDL流量测试	${CPE6}
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	10	1
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	10	5
	${M1}	${eth1PowerM1}	${eth2PowerM1}	中点PRB10%TDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	中点PRB30%TDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	中点PRB50%TDL流量测试	${CPE6}
	Comment	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2

RAN-3521063 [ITRAN-FDL]超级小区场景：增强符号关断-定点空口性能和AAU/RRU功耗测试__RAN-3521063
	[Teardown]	恢复环境
	${fdlCell2}	获取FDD LTE小区	5101-2
	${subCellLocalIdList}	create list	1
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	2	${subCellLocalIdList}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	#UE做业务
	#UE做业务
	${M1}	${eth1PowerM1}	${eth2PowerM1}	小区近点PRB10%FDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	小区近点PRB30%FDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	小区近点PRB50%FDL流量测试	${CPE6}
	NR小区符号关断节能	2	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	4	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	小区近点PRB10%FDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	小区近点PRB30%FDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	小区近点PRB50%FDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2
	# 中点
	设置UE信号衰减	${CPE6}	25
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	小区中点PRB10%FDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	小区中点PRB30%FDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	小区中点PRB50%FDL流量测试	${CPE6}
	NR小区符号关断节能	2	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	4	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	2
	${M1}	${eth1PowerM1}	${eth2PowerM1}	小区中点PRB10%FDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	小区中点PRB30%FDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	小区中点PRB50%FDL流量测试	${CPE6}
	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2

RAN-3502615 [LV共框]多prru LV混模场景NR时延验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，节能前后ping时延对比__RAN-3502605
	[Teardown]	恢复环境sleep
	${nrCell260}	获取NR小区	5101-260
	配置FDL小区1射频合并
	sleep	480
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	150
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	#UE做业务
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE260}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE260}
	...	${PDN}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE260}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE260}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3502616 [LV共框]多prru LV混模场景FDL时延验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，节能前后ping时延对比__RAN-3502607
	[Teardown]	恢复环境sleep
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区2射频合并
	sleep	480
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	#UE做业务
	${avgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${avgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	NR小区符号关断节能	2	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	2	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	4	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	${dtxavgDelayTime1}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE6}
	...	${PDN}
	${dtxavgDelayTime2}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE6}
	...	${PDN}
	run keyword and continue on failure	should be true	abs((${${avgDelayTime1}}-${${dtxavgDelayTime1}})/${${avgDelayTime1}})<0.2
	run keyword and continue on failure	should be true	abs((${${avgDelayTime2}}-${${dtxavgDelayTime2}})/${${avgDelayTime2}})<0.2

RAN-3490809 [TDL]多prru场景：增强符号关断-定点空口性能和AAU/RRU功耗测试__RAN-3490804
	[Teardown]	恢复环境
	${tdlCell5}	获取TDD LTE小区	tddCell5
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell5}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell5}
	#UE做业务
	${M1}	${eth1PowerM1}	${eth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	近点PRB30%TDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	近点PRB50%TDL流量测试	${CPE6}
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	10	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	近点PRB30%TDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	近点PRB50%TDL流量测试	${CPE6}
	Comment	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2
	# 中点
	设置UE信号衰减	${CPE6}	25
	${dtxM1}	${dtxEth1PowerM1}	${dtxEth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${dtxM2}	${dtxEth1PowerM2}	${dtxEth2PowerM2}	近点PRB30%TDL流量测试	${CPE6}
	${dtxM3}	${dtxEth1PowerM3}	${dtxEth2PowerM3}	近点PRB50%TDL流量测试	${CPE6}
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	10	5
	${M1}	${eth1PowerM1}	${eth2PowerM1}	近点PRB10%TDL流量测试	${CPE6}
	${M2}	${eth1PowerM2}	${eth2PowerM2}	近点PRB30%TDL流量测试	${CPE6}
	${M3}	${eth1PowerM3}	${eth2PowerM3}	近点PRB50%TDL流量测试	${CPE6}
	Comment	run keyword and continue on failure	should be true	abs((${${M1}}-${${dtxM1}})/${${dtxM1}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M2}}-${${dtxM2}})/${${dtxM2}})<0.2
	Comment	run keyword and continue on failure	should be true	abs((${${M3}}-${${dtxM3}})/${${dtxM3}})<0.2

V9200-GULNV-QCell-ES--0020--0010-0033 打开符号关断，打开QCI5判决开关，满足符号关断条件并且无QCI5用户则进入符号关断__8798320
	FDD LTE小区符号关断节能	1
	sleep	60
	确认LTE sonm节能上报成功	${ENODEB}	DTX ES	ES Start	10	1

V9200-GULNV-QCell-ES--0020--0010-0031 打开符号关断增强型RRU自检，当业务RB利用率小于符号关断门限，进入符号关断__8798318
	FDD LTE小区符号关断节能	1
	sleep	60
	确认LTE sonm节能上报成功	${ENODEB}	DTX ES	ES Start	10	1

RAN-2128481 [LV共框]多prru LV混模场景NR时延验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，节能前后ping时延对比__RAN-2128480
	[Documentation]	pass
	${nrCell260}	获取NR小区	5101-260
	配置FDL小区1射频合并
	sleep	480
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	150
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	${delayTimeFp32}	${name}	${value}	ping首包时延	UL	32	${CPE260}
	...	${PDN}
	${avgDelayTime32}	${name}	${value}	执行ping包操作并查询信息	UL	32	${CPE260}
	...	${PDN}
	${delayTimeFp2000}	${name}	${value}	ping首包时延	UL	2000	${CPE260}
	...	${PDN}
	${avgDelayTime2000}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE260}
	...	${PDN}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${dtxDelayTimeFp32}	${name}	${value}	ping首包时延	UL	32	${CPE260}
	...	${PDN}
	${dtxAvgDelayTime32}	${name}	${value}	执行ping包操作并查询信息	UL	1500	${CPE260}
	...	${PDN}
	${dtxDelayTimeFp2000}	${name}	${value}	ping首包时延	UL	2000	${CPE260}
	...	${PDN}
	${dtxAvgDelayTime2000}	${name}	${value}	执行ping包操作并查询信息	UL	2000	${CPE260}
	...	${PDN}
	run keyword and continue on failure	should be true	(${${avgDelayTime32}}-${dtxAvgDelayTime32})/${avgDelayTime32}<0.2
	run keyword and continue on failure	should be true	(${${avgDelayTime2000}}-${dtxAvgDelayTime2000})/${avgDelayTime2000}<0.2

RAN-2128482 [LV共框]多prru LV混模场景NR性能验证：各通道各制式都配置小区，开启所有制式增强型符号关断开关，定点空口性能和prru功耗测试__RAN-2128479
	log	RAN-3502606已实现

V9200-GULNV-QCell-ES--0020--0010-0032 打开符号关断增强型RRU自检，当业务RB利用率大于符号关断门限，打开符号__8798319
	${fdlCell2}	获取FDD LTE小区	5101-2
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${fdlCell2}	150
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	2
	sleep	65
	判断可替换单元节能模式	513	rfSymbolShutdown
	${PB1125F_1}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_2
	${eth1State}	PB供电状态检测_多模	${PB1125F_1}	5
	${eth1Power1}	evaluate	${eth1State}[-1]
	${number}	FDDLTE小区符号关断子帧个数	${fdlCell2}
	should be true	${number}!=0
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${fdlCell2}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE6}	DL	400m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	${eth1State}	PB供电状态检测_多模	${PB1125F_1}	5
	${eth1Power2}	evaluate	${eth1State}[-1]
	${number}	FDDLTE小区符号关断子帧个数	${fdlCell2}
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL
	should be true	${number}==0
	should be true	${eth1Power1} < ${eth1Power2}

V9200-GULNV-QCell-ES--0070--0020-0002 共框场景，打开Prru不支持的节能功能无害测试__8664391
	${nrCell260}	获取NR小区	5101-260
	${almStart}	查询基站当前告警_多模	${GNODEB}
	${filterDict}	create dictionary	mocName=NRCarrierObj	nrCarrierId=1
	${attributeNameList}	create list	moId
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}
	${moId}	Get From Dictionary	${result[0]}	moId
	${filterDict}	create dictionary	mocName=ChannelESPolicy	moId=1
	${attrDict}	create dictionary	chEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	${keyMoPathDict}	create dictionary	ESPolicy=${moId}
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	30
	${result}	run keyword and ignore error	确认sonm节能监控上报正确	${GNODEB}	Channel Shutdown ES	1	Close Downlink
	...	Start ES	10
	should be true	'${result[0]}'=='FAIL'
	Wait Until Keyword Succeeds	3min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}
	设置UE信号衰减	${CPE6}	10
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	150
	NR业务性能验证	${CPE260}	${nrCell260}

V9200-GULNV-QCell-ES--0020--0020-0001 LTE支持符号关断计数器，C373475046小区符号关断子帧个数__8094017
	[Teardown]	挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}
	${timeStrap}	get time	epoch
	${gnbId}	获取环境变量	${GNODEB}	meId
	${countIds}	create list	C373475046
	${TaskPara}	Create Dictionary	taskName=${gnbId}_${timeStrap}	moType=CUEUtranCellFDDLTE	counters=${countIds}	granularity=10
	${taskId}	创建实时KPI监控任务	${GNODEB}	${TaskPara}
	${taskIdList}	Create List
	Append To List	${taskIdList}	${taskId}
	sleep	60
	${realKpiData}	查询实时KPI监控数据	${GNODEB}	${taskId}
	${isRealKpiReport}	校验实时KPI监控数据上报	${realKpiData}
	Should Be True	${isRealKpiReport}	实时KPI数据上报异常

RAN-3505817 [LV共框]多频段prru：各通道各制式配置NR和LTE载波，打开所有频段增强型符号关断__RAN-3505817
	[Teardown]	恢复环境
	${nrCell260}	获取NR小区	5101-260
	${tdlCell1}	获取TDD LTE小区	tddCell5
	${fdlCell1}	获取FDD LTE小区	5101-1
	${taskId}	创建测量任务_多模	${GNODEB}	CellFDD	${None}	900	37347
	${taskId}	创建测量任务_多模	${GNODEB}	CellTDD	${None}	900	37347
	${taskId}	创建测量任务_多模	${GNODEB}	RruEnergy	${None}	300	37058
	${power}	PRRU511 PB端口功率
	NR小区符号关断节能	1	1
	NR小区符号关断节能	3	1
	sleep	5
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	判断可替换单元节能模式	511	rfSymbolShutdown
	${esPower}	PRRU511 PB端口功率
	should be true	${esPower} < ${power}
	验证节能后SON计数器
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	120
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	NR小区符号关断节能	1	0
	NR小区符号关断节能	3	0
	sleep	5
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	TDD LTE小区符号关断节能	0
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5
	sleep	70
	判断可替换单元节能模式	511	${Empty}
	验证节能停止SON计数器
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}

RAN-3505816 [LV共框]多频段prru：各通道各制式配置NR和LTE载波，打开FDL增强型符号关断, 对其他频段无影响__RAN-3505816
	${nrCell260}	获取NR小区	5101-260
	${tdlCell1}	获取TDD LTE小区	tddCell5
	${fdlCell1}	获取FDD LTE小区	5101-1
	${power}	PRRU511 PB端口功率
	FDD LTE小区符号关断节能	1
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	${esPower}	PRRU511 PB端口功率
	should be true	${esPower} < ${power}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell1}	120
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell1}
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	120
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	FDD LTE小区符号关断节能	0
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	sleep	70
	判断可替换单元节能模式	511	${Empty}
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell1}
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}

RAN-3505814 [LV共框]多频段prru：各通道各制式配置NR和LTE载波，打开NR增强型符号关断，对其他频段无影响__RAN-3505814
	${nrCell260}	获取NR小区	5101-260
	${tdlCell1}	获取TDD LTE小区	tddCell5
	${fdlCell1}	获取FDD LTE小区	5101-1
	${power}	PRRU511 PB端口功率
	NR小区符号关断节能	1	1
	NR小区符号关断节能	3	1
	sleep	5
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	${esPower}	PRRU511 PB端口功率
	should be true	${esPower} < ${power}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell1}	120
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell1}
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	120
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	NR小区符号关断节能	1	0
	NR小区符号关断节能	3	0
	sleep	5
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	sleep	70
	判断可替换单元节能模式	511	${Empty}
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell1}
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}

RAN-3505815 [LV共框]多频段prru：各通道各制式配置NR和LTE载波，打开TDL增强型符号关断,对其他频段无影响__RAN-3505815
	${nrCell260}	获取NR小区	5101-260
	${tdlCell1}	获取TDD LTE小区	tddCell5
	${fdlCell1}	获取FDD LTE小区	5101-1
	${power}	PRRU511 PB端口功率
	TDD LTE小区符号关断节能	1
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	${esPower}	PRRU511 PB端口功率
	should be true	${esPower} < ${power}
	设置UE模式_多模	${CPE6}	LTE
	设置UE锁NR小区频点和pci_多模	${CPE6}	${tdlCell1}	120
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell1}
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	120
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	TDD LTE小区符号关断节能	0
	sleep	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5
	sleep	70
	判断可替换单元节能模式	511	${Empty}
	UE去附着	${CPE6}
	UE同步并接入NR小区成功_多模	${CPE6}	${tdlCell1}
	UE去附着	${CPE260}
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}

RAN-3521085 [ITRAN-FDL]超级小区场景：辅cp异常时增强型符号关断启动和停止测试__RAN-3521085
	[Teardown]
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区1射频合并
	配置FDL小区2射频合并
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	修改FDL超级小区1 辅CP异常
	sleep	300
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	sleep	120
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	513	${EMPTY}
	判断可替换单元节能模式	514	${EMPTY}
	sleep	180
	#关闭节能开关
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	1
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	513	${EMPTY}
	判断可替换单元节能模式	514	${EMPTY}

RAN-3521076 [ITRAN-FDL]超级小区场景：增强型符号关断生效后闭塞主辅CP__RAN-3521076
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区1射频合并
	配置FDL小区2射频合并
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	300
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru513}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+513
	${prru514}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+514
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	修改FDL CP状态	1-0	1
	sleep	40
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	30	1
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	修改FDL CP状态	1-0	0
	sleep	180
	FOR	${i}	IN RANGE	6
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	10
	run keyword if	'${i}'=='5'	fail
	判断可替换单元节能模式	512	rfSymbolShutdown
	修改FDL CP状态	2-1	1
	sleep	40
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop	30	1
	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start	30	1
	sleep	70
	判断可替换单元节能模式	513	${EMPTY}
	判断可替换单元节能模式	514	${EMPTY}
	修改FDL CP状态	2-1	0
	sleep	180
	FOR	${i}	IN RANGE	6
	${result}	run keyword and ignore error	判断可替换单元节能模式	513	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	10
	run keyword if	'${i}'=='5'	fail
	判断可替换单元节能模式	514	rfSymbolShutdown

RAN-3521072 [ITRAN-FDL]超级小区场景：增强型符号关断生效后复位主辅CP部分prru__RAN-3521072
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区1射频合并
	配置FDL小区2射频合并
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	300
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru513}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+513
	${prru514}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+514
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	复位PRRU_多模	${prru511}
	复位PRRU_多模	${prru513}
	sleep	90
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	513	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	sleep	1000
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	513	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail

RAN-3521074 [ITRAN-FDL]超级小区场景：增强型符号关断生效后复位主辅CP全部prru__RAN-3521074
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区1射频合并
	配置FDL小区2射频合并
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	300
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru513}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+513
	${prru514}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+514
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	复位PRRU_多模	${prru511}
	复位PRRU_多模	${prru512}
	复位PRRU_多模	${prru513}
	复位PRRU_多模	${prru514}
	sleep	90
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	512	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	513	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	514	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	sleep	1000
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	512	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	513	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	514	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail

RAN-3521083 [ITRAN-FDL]超级小区场景：主cp异常时增强型符号关断启动和停止测试__RAN-3521083
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区1射频合并
	配置FDL小区2射频合并
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	修改FDL超级小区1 主CP异常
	sleep	300
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru513}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+513
	${prru514}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+514
	sleep	120
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	#关闭节能开关
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	1
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	513	${EMPTY}
	判断可替换单元节能模式	514	${EMPTY}

RAN-3521071 [ITRAN-FDL]非节能态时拆组超级小区，超级小区增强型符号关断启动和停止测试__RAN-3521071
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	配置FDL小区1射频合并
	配置FDL小区2射频合并
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	300
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru513}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+513
	${prru514}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182135+514
	sleep	120
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	1
	拆分LTE超级小区_多模	${ENODEB}	1
	sleep	80
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	2
	sleep	60
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	513	rfSymbolShutdown
	判断可替换单元节能模式	514	rfSymbolShutdown
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	2
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	513	${EMPTY}
	判断可替换单元节能模式	514	${EMPTY}

RAN-3521084 [ITRAN-TDL]超级小区场景：辅cp异常时增强型符号关断启动和停止测试__RAN-3521084
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${tdlCell6}	获取TDD LTE小区	tddCell6
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${result}	__修改节点属性	tddCell5	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${result}	__修改节点属性	tddCell6	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	修改TDL超级小区5 辅CP异常
	#组合TDDLTE超级小区
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}	tdd
	#打开节能开关
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	150
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	${EMPTY}
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru520}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8139 M182326+520
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	5
	sleep	90
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	520	${EMPTY}

RAN-3521075 [ITRAN-TDL]超级小区场景：增强型符号关断生效后闭塞主辅CP__RAN-3521075
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${tdlCell6}	获取TDD LTE小区	tddCell6
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${result}	__修改节点属性	${tdlCell5}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${result}	__修改节点属性	${tdlCell6}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}	tdd
	#打开节能开关
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	60	5
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	TDL闭塞解闭塞主CP符号关断测试
	TDL闭塞解闭塞辅CP符号关断测试

RAN-3507371 [ITRAN-TDL]超级小区场景：增强型符号关断生效后复位主辅CP部分prru__RAN-3507371
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${tdlCell6}	获取TDD LTE小区	tddCell6
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${result}	__修改节点属性	tddCell5	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${result}	__修改节点属性	tddCell6	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	#组合TDDLTE超级小区
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}	tdd
	#打开节能开关
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru520}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8139 M182326+520
	复位PRRU_多模	${prru511}
	复位PRRU_多模	${prru520}
	sleep	90
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	520	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	sleep	1000
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	520	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail

RAN-3521073 [ITRAN-TDL]超级小区场景：增强型符号关断生效后复位主辅CP全部prru__RAN-3521073
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${tdlCell6}	获取TDD LTE小区	tddCell6
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${result}	__修改节点属性	tddCell5	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${result}	__修改节点属性	tddCell6	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	#组合TDDLTE超级小区
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}	tdd
	#打开节能开关
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru520}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8139 M182326+520
	复位PRRU_多模	${prru511}
	复位PRRU_多模	${prru512}
	复位PRRU_多模	${prru520}
	sleep	90
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	512	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	FOR	${i}	IN RANGE	5
	${result}	run keyword and ignore error	判断可替换单元节能模式	520	${EMPTY}
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='4'	fail
	sleep	1000
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	511	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	512	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail
	FOR	${i}	IN RANGE	10
	${result}	run keyword and ignore error	判断可替换单元节能模式	520	rfSymbolShutdown
	exit for loop if	'${result[0]}'=='PASS'
	sleep	60
	run keyword if	'${i}'=='9'	fail

RAN-3521082 [ITRAN-TDL]超级小区场景：主cp异常时增强型符号关断启动和停止测试__RAN-3521082
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${tdlCell6}	获取TDD LTE小区	tddCell6
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${result}	__修改节点属性	tddCell5	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${result}	__修改节点属性	tddCell6	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	修改TDL超级小区5 主CP异常
	#组合TDDLTE超级小区
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}	tdd
	#打开节能开关
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	520	rfSymbolShutdown
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru520}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8139 M182326+520
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	520	${EMPTY}

RAN-3521070 [ITRAN-TDL]非节能态时拆组超级小区，超级小区增强型符号关断启动和停止测试__RAN-3521070
	${tdlCell5}	获取TDD LTE小区	tddCell5
	${tdlCell6}	获取TDD LTE小区	tddCell6
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${result}	__修改节点属性	tddCell5	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${result}	__修改节点属性	tddCell6	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	#组合TDDLTE超级小区
	${subCellLocalIdList}	create list	6
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	5	${subCellLocalIdList}	tdd
	#打开节能开关
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	${prru511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prru512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	${prru520}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8139 M182326+520
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	5
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	520	${EMPTY}
	拆分LTE超级小区_多模	${ENODEB}	5	tdd
	sleep	80
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	6
	sleep	60
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	6
	sleep	60
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	判断可替换单元节能模式	520	${EMPTY}

RAN-3521080 [ITRAN-FDL]超级小区场景：增强型符号关断生效后修改cellid，增强型符号关断功能正常__RAN-3521080
	#修改小区2的射频
	${attrDict}	create dictionary	moId=19	refReplaceableUnit=512	antGroupNo=6	usedRxChannel=7-8	usedTxChannel=7-8
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruTxRxGroup	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${attrDict}	create dictionary	moId=SF_FL15	refPrruTxRxGroup=SupportFunction=1,PrruTxRxGroup=19	freqBand=3	availableSectorPower=0.5	configuredOutputPower=0.5
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	SectorFunction	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncFDDLTE	moId=2-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_FL15
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}
	检查激活配置	${GNODEB}
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	600
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	sleep	120
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${attrDict}	Create Dictionary	cellLocalId=11
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	cellLocalId=11
	${filterDict}	Create Dictionary	mocName=DUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	11
	sleep	120
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown

RAN-3521081 [ITRAN-FDL]超级小区场景：DTX策略变更，超级小区按照修改后的策略生效__RAN-3521081
	#修改小区2的射频
	${attrDict}	create dictionary	moId=19	refReplaceableUnit=512	antGroupNo=6	usedRxChannel=7-8	usedTxChannel=7-8
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruTxRxGroup	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${attrDict}	create dictionary	moId=SF_FL15	refPrruTxRxGroup=SupportFunction=1,PrruTxRxGroup=19	freqBand=3	availableSectorPower=0.5	configuredOutputPower=0.5
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	SectorFunction	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncFDDLTE	moId=2-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_FL15
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}
	检查激活配置	${GNODEB}
	${fdlCell1}	获取FDD LTE小区	5101-1
	${fdlCell2}	获取FDD LTE小区	5101-2
	#打开LTE节能小区
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${result}	__修改节点属性	${fdlCell1}	${filterDict}	${attrDict}	planarea	${None}
	${attrDict}	Create Dictionary	energySavControl=1
	${filterDict}	Create Dictionary	mocName=CUEUtranCellFDDLTE	moId=2
	${result}	__修改节点属性	${fdlCell2}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	240
	#组合FDDLTE超级小区
	${subCellLocalIdList}	create list	2
	run keyword and ignore error	组合LTE超级小区_多模	${ENODEB}	1	${subCellLocalIdList}
	#打开节能开关
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	sleep	120
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	sleep	60
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	1
	sleep	120
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown

RAN-4043302 [TNR]TNR劈裂场景：符号关断进入和退出测试，观察NR之间影响__RAN-4043302
	${filterDict}	create dictionary	mocName=PrruTxRxGroup	refReplaceableUnit=Equipment=1,ReplaceableUnit=511	usedRxChannel=3-6
	${attributeNameList}	create list	moId
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}
	${moId}	Get From Dictionary	${result[0]}	moId
	${filterDict}	create dictionary	mocName=PrruTxRxGroup	moId=${moId}
	${attrDict}	create dictionary	usedRxChannel=3-4	usedTxChannel=3-4
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=PrruTxRxGroup	refReplaceableUnit=Equipment=1,ReplaceableUnit=512	usedRxChannel=3-6
	${attributeNameList}	create list	moId
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}
	${moId}	Get From Dictionary	${result[0]}	moId
	${filterDict}	create dictionary	mocName=PrruTxRxGroup	moId=${moId}
	${attrDict}	create dictionary	usedRxChannel=3-4	usedTxChannel=3-4
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=SectorFunction	moId=SF_NR11
	${attrDict}	create dictionary	availableSectorPower=0.8	configuredOutputPower=0.8
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=SectorFunction	moId=SF_NR11
	${attrDict}	create dictionary	availableSectorPower=0.8	configuredOutputPower=0.8
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=NRSectorCarrier	moId=1
	${attrDict}	create dictionary	configuredMaxTxPower=260
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=NRSectorCarrier	moId=3
	${attrDict}	create dictionary	configuredMaxTxPower=260
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=CPList	moId=1
	${attrDict}	create dictionary	powerPerRERef=-92
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=CPList	moId=1
	${attrDict}	create dictionary	powerPerRERef=-92
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=3
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}
	${attrDict}	create dictionary	moId=19	refReplaceableUnit=511	antGroupNo=5	usedRxChannel=5-6	usedTxChannel=5-6
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruTxRxGroup	${keyMoPathDict}
	${attrDict}	create dictionary	moId=20	refReplaceableUnit=512	antGroupNo=5	usedRxChannel=5-6	usedTxChannel=5-6
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruTxRxGroup	${keyMoPathDict}
	${attrDict}	create dictionary	moId=SF_NR20	refPrruTxRxGroup=SupportFunction=1,PrruTxRxGroup=19;SupportFunction=1,PrruTxRxGroup=20	freqBand=41	availableSectorPower=0.8	configuredOutputPower=0.8
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	SectorFunction	${keyMoPathDict}
	检查激活配置	${GNODEB}
	#修改小区9，10扇区
	${filterDict}	create dictionary	mocName=NRSectorCarrier	moId=9
	${attrDict}	create dictionary	configuredMaxTxPower=260	refSectorFunction=SupportFunction=1,SectorFunction=SF_NR20
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=NRSectorCarrier	moId=10
	${attrDict}	create dictionary	configuredMaxTxPower=260	refSectorFunction=SupportFunction=1,SectorFunction=SF_NR20
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea
	${filterDict}	create dictionary	mocName=CPList	moId=1
	${attrDict}	create dictionary	powerPerRERef=-92
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=9
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=CPList	moId=1
	${attrDict}	create dictionary	powerPerRERef=-92
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=10
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}
	sleep	300
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	NR小区符号关断节能	9	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	9	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	10	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	10	Enhanced Symbol Shutdown	Start ES
	sleep	70
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	NR小区符号关断节能	9	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	9	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	10	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	10	Enhanced Symbol Shutdown	Stop ES
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}

test
	Comment	${taskId}	创建测量任务_多模	${GNODEB}	CellFDD	${None}	900	37347
	Comment	${taskId}	创建测量任务_多模	${GNODEB}	CellTDD	${None}	900	37347
	Comment	${taskId}	创建测量任务_多模	${GNODEB}	RruEnergy	${None}	300	37058
	Comment	sleep	60
	${queryModleDict}	create dictionary	5101_ESDTX=me
	Comment	${time}	查询基站时间_多模	${GNODEB}
	Comment	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	15
	${startTime}	set variable	2024-08-24T12:45:00.000
	${endTime}	set variable	2024-08-24T13:00:00.000
	Comment	sleep	2400
	${filePathList}	create list
	FOR	${queryModle}	IN	@{queryModleDict}
	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}
	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}	${endTime}
	...	me	900
	append to list	${filePathList}	${filePath}
	保存NR小区信息_多模

[LV共框]多prru场景：DTX增强型符号关断节能期间，复位部分PRRU__RAN-3460239
	[Teardown]	恢复环境
	${nrCell260}	获取NR小区	5101-260
	配置FDL小区1射频合并
	sleep	480
	设置UE模式_多模	${CPE260}	SA
	设置UE锁NR小区频点和pci_多模	${CPE260}	${nrCell260}	120
	UE同步并接入NR小区成功_多模	${CPE260}	${nrCell260}
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${R511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	复位PRRU_多模	${R511}
	sleep	150
	判断可替换单元节能模式	511	${Empty}
	sleep	480
	判断可替换单元节能模式	511	rfSymbolShutdown

符号关断与工具：所有频段NR LTE进入符号关断和退出符号关断后，操作NI扫描__RAN-6159424
	${nrCell260}	获取NR小区	5101-260
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	频谱扫描	${nrCell260}
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5
	频谱扫描	${nrCell260}

符号关断与性能测量：所有频段NR LTE进入符号关断和退出符号关断后，验证性能测量数据正常上报__RAN-6159426
	#参考RAN-4065249用例
	run keyword and ignore error	删除基站所有测量任务_多模	${ENODEB}
	创建测量任务_多模	${ENODEB}	AauChannel	${None}	300	37060
	创建测量任务_多模	${ENODEB}	AauCarrier	${None}	300	37059
	创建测量任务_多模	${ENODEB}	PrruPwr	${None}	300	37016
	创建测量任务_多模	${ENODEB}	RruPwr	${None}	300	37019
	sleep	720
	节能前射频通道测量	511
	节能前通道级载波测量	511
	节能前电源功率测量	511
	节能前RRU功率测量	511
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	sleep	720
	节能后射频通道测量	511
	节能后通道级载波测量	511
	节能后电源功率测量	511
	节能后RRU功率测量	511

符号关断与诊断：所有频段NR LTE进入符号关断和退出符号关断后，小区功率查询、查询prru实际发射功率__RAN-6159425
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${PR511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prruPower1}	查询PRRU发射功率_多模	${PR511}	1
	${prruPower2}	查询PRRU发射功率_多模	${PR511}	2
	${prruPower3}	查询PRRU发射功率_多模	${PR511}	3
	${prruPower4}	查询PRRU发射功率_多模	${PR511}	4
	${prruPower5}	查询PRRU发射功率_多模	${PR511}	5
	${prruPower6}	查询PRRU发射功率_多模	${PR511}	6
	${prruPower7}	查询PRRU发射功率_多模	${PR511}	7
	${prruPower8}	查询PRRU发射功率_多模	${PR511}	8
	should be true	15 < ${prruPower1} < 17
	should be true	15 < ${prruPower2} < 17
	should be true	17 < ${prruPower3} < 19
	should be true	17 < ${prruPower4} < 19
	should be true	17 < ${prruPower5} < 19
	should be true	17 < ${prruPower6} < 19
	should be true	15 < ${prruPower7} < 17
	should be true	15 < ${prruPower8} < 17
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5
	sleep	65
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	${PR511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${prruPower1}	查询PRRU发射功率_多模	${PR511}	1
	${prruPower2}	查询PRRU发射功率_多模	${PR511}	2
	${prruPower3}	查询PRRU发射功率_多模	${PR511}	3
	${prruPower4}	查询PRRU发射功率_多模	${PR511}	4
	${prruPower5}	查询PRRU发射功率_多模	${PR511}	5
	${prruPower6}	查询PRRU发射功率_多模	${PR511}	6
	${prruPower7}	查询PRRU发射功率_多模	${PR511}	7
	${prruPower8}	查询PRRU发射功率_多模	${PR511}	8
	should be true	15 < ${prruPower1} < 17
	should be true	15 < ${prruPower2} < 17
	should be true	17 < ${prruPower3} < 19
	should be true	17 < ${prruPower4} < 19
	should be true	17 < ${prruPower5} < 19
	should be true	17 < ${prruPower6} < 19
	should be true	15 < ${prruPower7} < 18
	should be true	15 < ${prruPower8} < 18

多频段prru：各通道各制式配置NR和LTE载波，打开FDL符号关断, 对其他频段无影响（其中2.6G LV配置）__RAN-6159245
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	sleep	65
	FDL小区节能状态校验	1	1;0;0;0;0;0
	TDL小区节能状态校验	5	0;0;0;0;0;0
	NR小区节能状态校验	260	0
	NR小区节能状态校验	261	0
	判断可替换单元节能模式	511	rfSymbolShutdown

多频段prru：各通道各制式配置NR和LTE载波，打开TDL符号关断,对其他频段无影响（其中2.6G LV配置）__RAN-6159178
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	FDL小区节能状态校验	1	0;0;0;0;0;0
	TDL小区节能状态校验	5	1;0;0;0;0;0
	NR小区节能状态校验	260	0
	NR小区节能状态校验	261	0
	判断可替换单元节能模式	511	rfSymbolShutdown

多频段prru：各通道各制式配置NR和LTE载波，打开TNR符号关断，对其他频段无影响（其中2.6G LV配置）__RAN-6159206
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	sleep	65
	FDL小区节能状态校验	1	0;0;0;0;0;0
	TDL小区节能状态校验	5	0;0;0;0;0;0
	NR小区节能状态校验	260	3
	NR小区节能状态校验	261	3
	判断可替换单元节能模式	511	rfSymbolShutdown

多频段prru：部分频段未配置载波时，打开其他频段符号关断__RAN-6159430
	保存NR小区信息_多模	${GNODEB}
	删除指定NR小区_多模	${GNODEB}	260
	删除指定NR小区_多模	${GNODEB}	261
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	导入基站数据_多模	${GNODEB}	${XML_PATH}
	保存LTE-FDD小区信息_多模	${GNODEB}
	删除指定LTE-FDD小区_多模	${GNODEB}	1
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	导入基站数据_多模	${GNODEB}	${XML_PATH}
	保存LTE-TDD小区信息_多模	${GNODEB}
	删除指定LTE-TDD小区_多模	${GNODEB}	5
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown

所有频段NR LTE进入符号关断后，复位所有prru，载波节能先退再进__RAN-6159221
	配置FDL小区1射频合并
	sleep	480
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${R511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	${R512}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+512
	复位PRRU_多模	${R511}
	复位PRRU_多模	${R512}
	sleep	150
	FDL小区节能状态校验	1	0;0;0;0;0;0
	TDL小区节能状态校验	5	0;0;0;0;0;0
	NR小区节能状态校验	260	0
	NR小区节能状态校验	261	0
	sleep	480
	FDL小区节能状态校验	1	1;0;0;0;0;0
	TDL小区节能状态校验	5	1;0;0;0;0;0
	NR小区节能状态校验	260	3
	NR小区节能状态校验	261	3

所有频段NR LTE进入符号关断后，复位部分prru（每个小区保留1个prru不复位），TDL FDL TNR小区不退节能，prru运行正常后进节能__RAN-6159205
	配置FDL小区1射频合并
	sleep	480
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	${R511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	复位PRRU_多模	${R511}
	sleep	150
	判断可替换单元节能模式	511	${EMPTY}
	FDL小区节能状态校验	1	1;0;0;0;0;0
	TDL小区节能状态校验	5	1;0;0;0;0;0
	NR小区节能状态校验	260	3
	NR小区节能状态校验	261	3
	sleep	480
	判断可替换单元节能模式	511	rfSymbolShutdown

部分prru虚配时，打开和关闭所有频段NR LTE符号关断，载波节能可正常进退__RAN-6159198
	[Teardown]	run keyword and ignore error	上电PRRU_多模	${R511}
	配置FDL小区1射频合并
	${R511}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	R8149 M182326+511
	下电PRRU_多模	${R511}
	sleep	480
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	判断可替换单元节能模式	512	rfSymbolShutdown
	NR小区符号关断节能	1	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Stop ES
	NR小区符号关断节能	3	0
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	FDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	1
	TDD LTE小区符号关断节能	0
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	5

增删改小区：双载波配置，各通道只有1个载波进入符号关断状态时，删除节能载波__RAN-6159289
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-FDD
	__删除节点	${GNODEB}	${filterDict}
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD
	__删除节点	${GNODEB}	${filterDict}
	检查激活配置	${GNODEB}
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=6
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	保存NR小区信息_多模	${GNODEB}
	删除指定NR小区_多模	${GNODEB}	261
	保存LTE-TDD小区信息_多模	${ENODEB}
	删除指定LTE-TDD小区_多模	${ENODEB}	6
	TDL小区节能状态校验	5	0;0;0;0;0;0
	NR小区节能状态校验	260	0

增删改小区：双载波配置，各通道只有1个载波进入符号关断状态时，删除非节能载波__RAN-6159325
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-FDD
	__删除节点	${GNODEB}	${filterDict}
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD
	__删除节点	${GNODEB}	${filterDict}
	检查激活配置	${GNODEB}
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=6
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	保存NR小区信息_多模	${GNODEB}
	删除指定NR小区_多模	${GNODEB}	260
	保存LTE-TDD小区信息_多模	${ENODEB}
	删除指定LTE-TDD小区_多模	${ENODEB}	5
	TDL小区节能状态校验	6	1;0;0;0;0;0
	NR小区节能状态校验	261	3

增删改小区：双载波配置，都进入符号关断后，删除其中一个载波，不影响未删除载波的节能状态__RAN-6159369
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	保存NR小区信息_多模	${GNODEB}
	删除指定NR小区_多模	${GNODEB}	261
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Stop ES
	保存LTE-TDD小区信息_多模	${ENODEB}
	删除指定LTE-TDD小区_多模	${ENODEB}	6
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	20	6
	FDL小区节能状态校验	1	1;0;0;0;0;0
	TDL小区节能状态校验	5	1;0;0;0;0;0
	NR小区节能状态校验	260	3
	修改TDL小区状态	8	1

增删改小区：所有通道单载波进入符号关断后，新建载波，原载波不退节能，新建载波非节能态，通道非节能状态__RAN-6159263
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	保存NR小区信息_多模	${GNODEB}
	删除指定NR小区_多模	${GNODEB}	260
	保存LTE-TDD小区信息_多模	${ENODEB}
	删除指定LTE-TDD小区_多模	${ENODEB}	5
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-FDD
	__删除节点	${GNODEB}	${filterDict}
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD
	__删除节点	${GNODEB}	${filterDict}
	检查激活配置	${GNODEB}
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=6
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	创建指定NR小区_多模	${GNODEB}	260
	创建指定LTE-TDD小区_多模	${ENODEB}	5
	sleep	240
	FDL小区节能状态校验	1	0;0;0;0;0;0
	TDL小区节能状态校验	5	0;0;0;0;0;0
	TDL小区节能状态校验	6	1;0;0;0;0;0
	NR小区节能状态校验	260	0
	NR小区节能状态校验	261	3

增删改小区：进入符号关断后，修改TNR FDL TDL小区频点及带宽__RAN-6159248
	${nrCell260}	获取NR小区	5101-260
	${nrCell261}	获取NR小区	5101-261
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	FDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	TDD LTE小区符号关断节能	1
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=1
	${attrDict}	create dictionary	bandWidthDl=3	bandWidthUl=3	earfcnUl=1740	earfcnDl=1835	maxUeRbNumDl=50
	...	sampleRateCfg=0	maxUeRbNumUl=23
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=5
	${attrDict}	create dictionary	earfcn=2350	bandWidth=3	maxUeRbNumDl=50	maxUeRbNumUl=23	sampleRateCfg=0
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2365	bandWidth=3	maxUeRbNumDl=50	maxUeRbNumUl=23	sampleRateCfg=0
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	修改TNR小区的带宽	${nrCell260}	80	2568	217
	修改TNR小区的带宽	${nrCell261}	40	2642.01	106
	sleep	240
	FDL小区节能状态校验	1	1;0;0;0;0;0
	TDL小区节能状态校验	5	1;0;0;0;0;0
	TDL小区节能状态校验	6	1;0;0;0;0;0
	NR小区节能状态校验	260	3
	NR小区节能状态校验	261	3

增删改小区：进入符号关断后，删除载波，再新建原载波__RAN-6159288
	${nrCell260}	获取NR小区	5101-260
	${nrCell261}	获取NR小区	5101-261
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-FDD
	__删除节点	${GNODEB}	${filterDict}
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD
	__删除节点	${GNODEB}	${filterDict}
	检查激活配置	${GNODEB}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellFDDLTE=1
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyFDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=5
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=6
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	sleep	65
	判断可替换单元节能模式	511	rfSymbolShutdown
	保存NR小区信息_多模	${GNODEB}
	删除指定NR小区_多模	${GNODEB}	260
	删除指定NR小区_多模	${GNODEB}	261
	保存LTE-TDD小区信息_多模	${ENODEB}
	删除指定LTE-TDD小区_多模	${ENODEB}	5
	删除指定LTE-TDD小区_多模	${ENODEB}	6
	保存LTE-FDD小区信息_多模	${ENODEB}
	删除指定LTE-FDD小区_多模	${ENODEB}	1
	创建指定LTE-FDD小区_多模	${ENODEB}	1
	创建指定LTE-TDD小区_多模	${ENODEB}	5
	创建指定LTE-TDD小区_多模	${ENODEB}	6
	创建指定NR小区_多模	${GNODEB}	260
	创建指定NR小区_多模	${GNODEB}	261
	sleep	240
	FDL小区节能状态校验	1	0;0;0;0;0;0
	TDL小区节能状态校验	5	0;0;0;0;0;0
	TDL小区节能状态校验	6	0;0;0;0;0;0
	NR小区节能状态校验	260	0
	NR小区节能状态校验	261	0

通道上多载波配置，只有部分载波进入符号关断时，PA不关闭。全部载波进入关断时PA才关闭（覆盖2.3 TDL多载波+2.6G/3.5G/4.9G NR双载波配置+2.1G FDL多载波等）__RAN-6159244
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-FDD
	__删除节点	${GNODEB}	${filterDict}
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD
	__删除节点	${GNODEB}	${filterDict}
	检查激活配置	${GNODEB}
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=6
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	sleep	65
	${power1}	PRRU511 PB端口功率
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	${attrDict}	create dictionary	moId=2	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=5
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	sleep	65
	${power2}	PRRU511 PB端口功率
	should be true	${power1} > ${power2}

闭塞小区：TDL多载波配置，进入符号关断后闭塞解闭塞1个小区和全部小区__RAN-6159264
	#“R8149 M182326”的7、8射频端口最多可配置1个FDD载波
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=6
	${attrDict}	create dictionary	earfcn=2360
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${attrDict}	create dictionary	refSectorFunction=SupportFunction=1,SectorFunction=SF_TL11
	${keyMoPathDict}	create dictionary	CPResource=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	240
	NR小区符号关断节能	1	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Enhanced Symbol Shutdown	Start ES
	NR小区符号关断节能	3	1
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	3	Enhanced Symbol Shutdown	Start ES
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-FDD
	__删除节点	${GNODEB}	${filterDict}
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD
	__删除节点	${GNODEB}	${filterDict}
	检查激活配置	${GNODEB}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellFDDLTE=1
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyFDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	1
	${attrDict}	create dictionary	moId=1	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=6
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	6
	${attrDict}	create dictionary	moId=2	sonFuncId=57	refSonPolicyEsLTE=ENBCUCPFunction=460-01_5101,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=5
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}
	检查激活配置	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	20	5
	修改TDL小区状态	5	1
	sleep	65
	TDL小区节能状态校验	5	0;0;0;0;0;0
	${power1}	PRRU511 PB端口功率
	修改TDL小区状态	5	0
	sleep	65
	TDL小区节能状态校验	5	1;0;0;0;0;0
	${power2}	PRRU511 PB端口功率
	修改TDL小区状态	5	1
	修改TDL小区状态	6	1
	sleep	65
	TDL小区节能状态校验	5	0;0;0;0;0;0
	TDL小区节能状态校验	6	0;0;0;0;0;0
	修改TDL小区状态	5	0
	修改TDL小区状态	6	0
	sleep	65
	TDL小区节能状态校验	5	1;0;0;0;0;0
	TDL小区节能状态校验	6	1;0;0;0;0;0
	${power4}	PRRU511 PB端口功率


*** Keywords ***
加载配置
	[Arguments]	${scene}	${dataset}
	${params}	获取资源	${scene}	${dataset}
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}
	${XML_PATH}	导出基站数据_多模	${GNODEB}
	Set Global Variable	${XML_PATH}
	创建UE对象	${CPE260}
	创建UE对象	${CPE6}
	创建PDN	${PDN}
	删除基站所有测量任务_多模	${ENODEB}
	实例化单板_多模	${GNODEB}	${XML_PATH}
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V
	导出基站XML并备份	${GNODEB}	${UME}
	创建VSW_多模	${GNODEB}	${VSW}
	关闭告警防抖_多模	${GNODEB}

删除配置
	设置UE信号衰减	${CPE6}	0
	导入基站数据_多模	${GNODEB}	${XML_PATH}
	UE去附着	${CPE6}
	UE去附着	${CPE260}
	删除UE对象	${CPE6}
	删除UE对象	${CPE260}
	删除PDN	${PDN}
	删除VSW_多模	${VSW}
	释放实例化无线配置_多模	${GNODEB}
	释放实例化无线配置_多模	${ENODEB}
	释放实例化单板_多模	${GNODEB}
	删除基站_多模	${NODEB}

恢复环境
	设置UE信号衰减	${CPE6}	0
	导入基站数据_多模	${GNODEB}	${XML_PATH}

恢复环境sleep
	设置UE信号衰减	${CPE6}	0
	run keyword and ignore error	设置测量任务状态_多模	${ENODEB}	CellFDD
	run keyword and ignore error	删除测量任务_多模	${ENODEB}	CellFDD
	run keyword and ignore error	设置测量任务状态_多模	${ENODEB}	CellTDD
	run keyword and ignore error	删除测量任务_多模	${ENODEB}	CellTDD
	导入基站数据_多模	${GNODEB}	${XML_PATH}
	sleep	600

网管创建网络共享逻辑站
	[Arguments]	${gnbAlias}	${nssai}	${tarGnbID}	${refNRPhysicalCellDUId}=1	${cellLocalId}=1	${refIp}=1	${moIdSctp}=1	${tac}=512000
	[Documentation]	[功能说明]：
	...	\ \网管创建网络共享一个逻辑站
	...	[入参]：
	...	1、gnbAlias：参考基站别名
	...	2、nssai：新增网络共享支持plmn以及切片（参考开站模板cell5g->NSSAI 配置，第一个plmn为主plmn）
	...	例如:460-01:1118481&460-10:1118481
	...	A.表示新建主plmn为460-01，460-01、460-08支持切片1118481；460-00、460-10支持切片1118481、1118482的逻辑小区,需要使用者自己保证 plmn、切片、sd可用性，不能与同载波小区plmn重复
	...	B.如果在原逻辑小区已经存在，则只会在原逻辑小区新增460-08、460-00、460-10三个plmn
	...	
	...	3、tarGnbID：新逻辑站gnbid；支持兼容输入基站别名，通过别名获取config文件中的gnbid
	...	4、refNRPhysicalCellDUId：新逻辑小区索引的物理小区id，默认为1；支持直接输入参考的小区别名，脚本中自动获取其对应物理小区id
	...	5、cellLocalId：新建逻辑小区id，默认为1；支持按照vbpfiber方式从config自动获取cellid
	...	refIp
	...	6、refIp：sctp引用的IP ID
	...	7、moIdSctp：sctp的moId
	...	8、tac：切片tac，默认512000
	[Teardown]	#清除规划区变更数据	${gnbAlias}
	${gnbId}	获取环境变量	${gnbAlias}	gnodebId
	${moId}	获取环境变量	${gnbAlias}	moId
	${curPlmn}	获取环境变量	${gnbAlias}	plmn
	${cellPlmnList}	split string	${nssai}	:
	${cellPlmn}	set variable	${cellPlmnList[0]}
	${moId}	set variable	${cellPlmn}_${gnbId}
	${filterDict}	create dictionary	mocName=ServiceMap5g	moId=1
	${attr}	create list	plmn
	${attrlistTemp}	__查询节点属性信息	${gnbAlias}	${filterDict}	${attr}
	${plmn}	get from dictionary	${attrlistTemp[0]}	plmn
	${plmnList}	split string	${plmn}	;
	${result}	run keyword and ignore error	Should Contain Match	${plmnList}	${cellPlmn}
	${cellPlmnListStr}	set variable if	'${result[0]}'=='PASS'	${plmn}	${plmn};${cellPlmn}
	log	4、配置TransportNetwork
	${tmp}	split string	${cellPlmn}	-
	${localPort}	set variable	${tmp[0]}${tmp[1]}
	创建SCTP_多模	${gnbAlias}	${moIdSctp}	${localPort}	${refIp}	5000	***************
	...	8192
	修改ServiceMap5g PLMN值	${gnbAlias}	${cellPlmnListStr}
	log	1、配置GNBDUFunction
	${attrDict}	create dictionary	moId=${moId}	gNBId=${gnbId}	pLMNId=${cellPlmn}	gNBDUId=${moIdSctp}
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	GNBDUFunction	${keyMoPathDict}
	${attrDict}	create dictionary	moId=${moIdSctp}	pLMNId=${cellPlmn}
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	PlmnIdList	${keyMoPathDict}
	检查激活配置	${gnbAlias}	${True}	plan
	Comment	${attrDict}	create dictionary	moId=${cellLocalId}	cellLocalId=${cellLocalId}	refNRPhysicalCellDU=1	tac=${tac}
	Comment	${attrDict}	create dictionary	moId=${cellLocalId}	cellLocalId=${cellLocalId}	refNRPhysicalCellDU=1	nRTAC=${tac}	refPlmnIdList=GNBDUFunction=${cellPlmn}_5101,PlmnIdList=${moIdSctp}
	${attrDict}	create dictionary	moId=${cellLocalId}	cellLocalId=${cellLocalId}	refNRPhysicalCellDU=1
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	NRCellDU	${keyMoPathDict}	${True}
	${attrDict}	create dictionary	moId=${cellLocalId}	tac=${tac}	refPlmnIdList=GNBDUFunction=${cellPlmn}_5101,PlmnIdList=${moIdSctp}
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	PlmnInfoDU	${keyMoPathDict}	${True}
	${attrDict}	create dictionary	moId=${moIdSctp}	refPlmnIdList=${moIdSctp}
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}	NRCellDU=260
	__创建节点	${gnbAlias}	${attrDict}	planarea	PlmnGroupList	${keyMoPathDict}
	${attrDict}	create dictionary	moId=1	nssiId=1	operationalState=Enabled
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	NetworkSliceSubnet	${keyMoPathDict}
	${attrDict}	create dictionary	moId=1	sliceProfileId=1	coverageAreaTAList=${tac}	pLMNIdList=${cellPlmn}
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	SliceProfile	${keyMoPathDict}
	${attrDict}	create dictionary	moId=1	sst=1	sd=1118481
	${keyMoPathDict}	create dictionary	GNBDUFunction=${moId}	SliceProfile=1
	__创建节点	${gnbAlias}	${attrDict}	planarea	NSSAI	${keyMoPathDict}
	检查激活配置	${gnbAlias}	${True}	plan
	log	2、配置GNBCUCPFunction
	${moId}	set variable	${cellPlmn}_${gnbId}
	${attrDict}	create dictionary	moId=${moId}	gNBId=${gnbId}	pLMNId=${cellPlmn}
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	GNBCUCPFunction	${keyMoPathDict}
	${attrDict}	create dictionary	moId=${moIdSctp}	pLMNId=${cellPlmn}
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	PlmnIdListCU	${keyMoPathDict}
	检查激活配置	${gnbAlias}	${True}	plan
	${attrDict}	create dictionary	moId=${moIdSctp}	sctpInfo=${moIdSctp}
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	NgAp	${keyMoPathDict}
	检查激活配置	${gnbAlias}	${True}	plan
	${attrDict}	create dictionary	moId=${cellLocalId}	cellLocalId=${cellLocalId}	cityLabel=SZ
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	NRCellCU	${keyMoPathDict}	${True}
	检查激活配置	${gnbAlias}	${True}	plan
	log	3、配置GNBCUUPFunction
	${moId}	set variable	${cellPlmn}_${gnbId}
	${attrDict}	create dictionary	moId=${moId}	gNBId=${gnbId}	pLMNId=${cellPlmn}	gNBCUUPID=${gnbId}
	${keyMoPathDict}	create dictionary	GNBCUUPFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	GNBCUUPFunction	${keyMoPathDict}
	${attrDict}	create dictionary	moId=${moIdSctp}	pLMNId=${cellPlmn}
	${keyMoPathDict}	create dictionary	GNBCUUPFunction=${moId}
	__创建节点	${gnbAlias}	${attrDict}	planarea	PlmnIdListUP	${keyMoPathDict}
	检查激活配置	${gnbAlias}	${True}	plan

修改ServiceMap5g PLMN值
	[Arguments]	${gnbAlias}	${cellPlmnListStr}
	${filterDict}	create dictionary	mocName=ServiceMap5g	moId=1
	${attrDict}	create dictionary	plmn=${cellPlmnListStr}
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}	planarea
	检查激活配置	${gnbAlias}	${True}	plan

网管获取mocName某属性列表
	[Arguments]	${gnbAlias}	${mocName}	${attr}=moId	${keyMoDUPathDict}=${None}	${filterDict}=${None}	${templateName}=planarea
	[Documentation]	[功能说明]：
	...	\ \网管获取mocName某属性列表
	...	[入参]：
	...	1、gnbAlias：基站别名
	...	2、mocName：目标属性名
	...	3、attr：需要查的属性，默认为moid
	...	4、keyMoDUPathDict：过滤字典（ldn筛选），默认为${None}
	...	5、templateName：规划区，默认planarea，如果是现网区配置 ${None}
	...	
	...	
	...	
	...	[返回]：
	...	该mocname 满足过滤条件的属性值列表，由小到达顺序排列
	...	[备注]：
	...	已实现
	...	[作者]：
	...	10270755
	[Return]	${attrlist}
	${filterNewDict}	run keyword if	${filterDict}==${None}	create dictionary	mocName	${mocName}	ELSE
	...	copy dictionary	${filterDict}
	run keyword if	${filterDict}!=${None}	set to dictionary	${filterNewDict}	mocName	${mocName}
	log	${filterNewDict}
	${attrlistTemp}	__查询节点属性信息	${gnbAlias}	${filterNewDict}	${attr}	${keyMoDUPathDict}	${templateName}
	${attrlist}	create list
	FOR	${i}	IN	@{attrlistTemp}
	${attrTemp}	get from dictionary	${i}	${attr}
	append to list	${attrlist}	${attrTemp}
	${temp}	run keyword if	${attrlist}!=[]	evaluate	'${attrlist[0]}'.isdigit()	ELSE	set variable
	...	${False}
	${attrlist}	run keyword if	${temp}	evaluate	map(eval, ${attrlist})	ELSE	set variable
	...	${attrlist}
	sort list	${attrlist}

虚配MEC连接
	创建SCTP_多模	${GNODEB}	102	102	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=102	sctpInfo=102
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-12_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	103	103	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=103	sctpInfo=103
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-13_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	104	104	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=104	sctpInfo=104
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-14_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	105	105	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=105	sctpInfo=105
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-15_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	106	106	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=106	sctpInfo=106
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-16_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	107	107	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=107	sctpInfo=107
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-17_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	108	108	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=108	sctpInfo=108
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-18_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	109	109	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=109	sctpInfo=109
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-19_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	120	120	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=120	sctpInfo=120
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-20_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	创建SCTP_多模	${GNODEB}	121	121	1	38458	10.0.0.1
	...	8192
	${attrDict}	create dictionary	moId=121	sctpInfo=121
	${keyMoPathDict}	create dictionary	GNBCUCPFunction=460-21_5101
	__创建节点	${GNODEB}	${attrDict}	planarea	NRMecsAp	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan

闭塞除46001其他小区
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Unlocked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-01_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-11_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-12_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-13_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-14_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-15_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-16_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-17_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-18_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-19_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-20_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-21_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan

闭塞除46011其他小区
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-01_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Unlocked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-11_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-12_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-13_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-14_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-15_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-16_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-17_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-18_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-19_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-20_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan
	${filterDict}	create dictionary	mocName=NRCellDU	moId=260
	${attrDict}	create dictionary	adminState=Locked
	${keyMoPathDict}	create dictionary	GNBDUFunction=460-21_5101
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan

关闭DRX配置开关
	${celllist}	create list	260	261	350	351	356
	...	401	402	403	404	411	412	413
	...	414
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=260
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=261
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=350
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=351
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=356
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=401
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=402
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=403
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=404
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=411
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=412
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=413
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	drxSwitch=Close
	${filterDict}	Create Dictionary	mocName=DRXCfg	moId=1
	${keyMoPathDict}	Create Dictionary	NRCellCU=414
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan

修改小区polltime
	${attrDict}	Create Dictionary	pollTime=8
	${filterDict}	Create Dictionary	mocName=PositionAssistance	moId=1
	${keyMoPathDict}	Create Dictionary	NRPhysicalCellDU=1
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	srsNormalPeriod=10;20;160
	${filterDict}	Create Dictionary	mocName=SRSConfig	moId=1
	${keyMoPathDict}	Create Dictionary	NRPhysicalCellDU=1
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan

计算实时polltime
	[Arguments]	${NRPhysicalCellDU}	${srsAdaptionSwitch}=1
	[Documentation]	[入参]：
	...	
	...	${srsAdaptionSwitch}：SRS算法开关策略，0:虚拟周期固定策略; 1:真实周期固定策略
	...	
	...	[返回]：
	...	无
	...	[备注]：
	...	已实现
	...	无
	...	[作者]：
	[Return]	${polltime}
	${filterDict}	create dictionary	mocName=SRSConfig	moId=1
	${attributeNameList}	run keyword if	${srsAdaptionSwitch}==1	create list	srsPeriod	ELSE	create list
	...	srsNormalPeriod
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=1
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}	${keyMoPathDict}
	${srsPeriod}	Get From Dictionary	${result[0]}	${attributeNameList[0]}
	${srsPeriodList}	split string	${srsPeriod}	;
	${polltime}	evaluate	int(${srsPeriodList[-1]} * 0.05)

配置SRS新框架
	[Arguments]	${cell}
	${cellMoid}	查询NR物理DU小区属性_多模	${cell}	NRPhysicalCellDU	moId
	${filterDict}	create dictionary	mocName=PositionAssistance	moId=1
	${attrDict}	create dictionary	fingerprintPositionEnable=0
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=${cellMoid}
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=SRSConfig	moId=1
	${attrDict}	create dictionary	srsAdaptionSwitch=1
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=${cellMoid}
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${polltime}	计算实时polltime	${cellMoid}
	${filterDict}	create dictionary	mocName=PositionAssistance	moId=1
	${attrDict}	create dictionary	pollTime=${polltime}
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=${cellMoid}
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	Comment	${filterDict}	create dictionary	mocName=SRSConfig	moId=1
	Comment	${attrDict}	create dictionary	srsAdaptionSwitch=1
	Comment	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=${cellMoid}
	Comment	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=PositionAssistance	moId=1
	${attrDict}	create dictionary	fingerprintPositionEnable=1
	${keyMoPathDict}	create dictionary	NRPhysicalCellDU=${cellMoid}
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}

中点PRB10%NR流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	10m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${eth1Power1}	${eth2Power1}	NR小区260PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

中点PRB30%NR流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	Comment	UE去附着	${CPE2}
	Comment	UE同步并接入NR小区成功_多模	${CPE2}	${cell2}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	18m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${eth1Power1}	${eth2Power1}	NR小区260PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

中点PRB50%NR流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	Comment	UE去附着	${CPE2}
	Comment	UE同步并接入NR小区成功_多模	${CPE2}	${cell2}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	21m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${eth1Power1}	${eth2Power1}	NR小区260PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

NR小区260PB端口功率
	[Return]	${eth1Power}	${eth2Power}
	${PB_25_1}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_1
	${PB_25_3}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_3
	${eth1State}	PB供电状态检测_多模	${PB_25_1}	7
	${eth2State}	PB供电状态检测_多模	${PB_25_3}	5
	${eth1Power}	evaluate	${eth1State}[-1]
	${eth2Power}	evaluate	${eth2State}[-1]

近点PRB10%NR流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	35m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${eth1Power1}	${eth2Power1}	NR小区260PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

近点PRB30%NR流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	80m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${eth1Power1}	${eth2Power1}	NR小区260PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

近点PRB50%NR流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	100m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${eth1Power1}	${eth2Power1}	NR小区260PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

NR小区符号关断节能
	[Arguments]	${nrCarrierId}	${dtxEsSwitch}
	${filterDict}	create dictionary	mocName=NRCarrierObj	nrCarrierId=${nrCarrierId}
	${attributeNameList}	create list	moId
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}
	${moId}	Get From Dictionary	${result[0]}	moId
	${filterDict}	create dictionary	mocName=DTXESPolicy	moId=1
	${attrDict}	create dictionary	dtxEsSwitch=${dtxEsSwitch}	dtxFunction=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	${keyMoPathDict}	create dictionary	ESPolicy=${moId}
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}
	sleep	30

FDD LTE小区符号关断节能
	[Arguments]	${esDTXSwitch}
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}
	同步规划区数据_多模	${GNODEB}
	sleep	20
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}

TDD LTE小区符号关断节能
	[Arguments]	${esDTXSwitch}
	${attr}	create dictionary	sonFuncId	57	sonSwitch	0
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	同步规划区数据_多模	${GNODEB}
	sleep	20
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=0	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}
	同步规划区数据_多模	${GNODEB}

近点PRB10%TDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	4m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	TDL PRB资源个数KPI	349162	CUEUtranCellTDDLTE	5
	${eth1Power1}	${eth2Power1}	TDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

近点PRB30%TDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	10m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	${PRBcount}	TDL PRB资源个数KPI	349162	CUEUtranCellTDDLTE	5
	${eth1Power1}	${eth2Power1}	TDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL

近点PRB50%TDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	3
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	${PRBcount}	TDL PRB资源个数KPI	349162	CUEUtranCellTDDLTE	5
	${eth1Power1}	${eth2Power1}	TDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL

中点PRB10%TDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	3m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	${PRBcount}	TDL PRB资源个数KPI	349162	CUEUtranCellTDDLTE	5
	${eth1Power1}	${eth2Power1}	TDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL

中点PRB30%TDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	8m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	${PRBcount}	TDL PRB资源个数KPI	349162	CUEUtranCellTDDLTE	5
	${eth1Power1}	${eth2Power1}	TDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL

中点PRB50%TDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	12m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	${PRBcount}	TDL PRB资源个数KPI	349162	CUEUtranCellTDDLTE	5
	${eth1Power1}	${eth2Power1}	TDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL

TDL PRB资源个数KPI
	[Arguments]	${counters}	${moType}	${monitorObjMoIds}
	[Teardown]	挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}
	[Return]	${value}
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	${meId}	获取网元ID	${GNODEB}
	${paras}	create dictionary	taskName=${meId}_${time}	moType=${moType}	counters=${counters}	monitorObjMoIds=${monitorObjMoIds}
	${taskId}	创建实时KPI监控任务	${GNODEB}	${paras}
	${taskIdList}	create list	${taskId}
	sleep	150
	${filterDict1}	create dictionary	E-UTRAN TDD Cell ID=5
	${filterDictList1}	create list	${filterDict1}
	Comment	${filterDict2}	create dictionary	Cell portion list ID=2
	Comment	${filterDictList2}	create list	${filterDict2}
	${realKpiData1}	查询实时KPI监控数据	${GNODEB}	${taskId}	1	50	${filterDictList1}
	Comment	${realKpiData2}	查询实时KPI监控数据	${GNODEB}	${taskId}	1	50	${filterDictList2}
	${value}	过滤实时KPI指定列数据	${realKpiData1}	10
	${value}	Get Slice From List	${value}	0	-1
	${value}	列表求平均值	${value}
	Comment	${value2}	计算实时KPI指定列平均值	${realKpiData2}	10
	Comment	${value1}	evaluate	${value1}[0]
	Comment	${value2}	evaluate	${value2}[0]

TDL PB端口功率
	[Return]	${eth1Power}	${eth2Power}
	${PB_25_1}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_1
	${PB_25_3}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_3
	${eth1State}	PB供电状态检测_多模	${PB_25_1}	7
	${eth2State}	PB供电状态检测_多模	${PB_25_3}	5
	${eth1Power}	evaluate	${eth1State}[-1]
	${eth2Power}	evaluate	${eth2State}[-1]

列表求平均值
	[Arguments]	${listAlias}
	[Return]	${value}
	${sum}	set variable	0
	FOR	${elem}	IN	@{listAlias}
	${sum}	Evaluate	${sum}+${elem}
	${len}	get length	${listAlias}
	${value}	Evaluate	${sum}/${len}

小区近点PRB10%FDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	4m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	小区FDL PRB资源个数KPI	349162	CUEUtranCellFDDLTE	2
	${eth1Power1}	${eth2Power1}	小区FDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

小区近点PRB30%FDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	10m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	小区FDL PRB资源个数KPI	349162	CUEUtranCellFDDLTE	2
	${eth1Power1}	${eth2Power1}	小区FDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

小区近点PRB50%FDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	18m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	小区FDL PRB资源个数KPI	349162	CUEUtranCellFDDLTE	2
	${eth1Power1}	${eth2Power1}	小区FDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

小区中点PRB10%FDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	3m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	小区FDL PRB资源个数KPI	349162	CUEUtranCellFDDLTE	2
	${eth1Power1}	${eth2Power1}	小区FDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

小区中点PRB30%FDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	8m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	小区FDL PRB资源个数KPI	349162	CUEUtranCellFDDLTE	2
	${eth1Power1}	${eth2Power1}	小区FDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

小区中点PRB50%FDL流量测试
	[Arguments]	${CPE}
	[Return]	${dlValue}	${eth1Power1}	${eth2Power1}
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	12m
	...	9999	1024	4
	sleep	30
	${dlValue}	查询UDP流量	${trafficId}	DL
	${PRBcount}	小区FDL PRB资源个数KPI	349162	CUEUtranCellFDDLTE	2
	${eth1Power1}	${eth2Power1}	小区FDL PB端口功率
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	DL

小区FDL PRB资源个数KPI
	[Arguments]	${counters}	${moType}	${monitorObjMoIds}
	[Teardown]	#挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}
	[Return]	${value}
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	${meId}	获取网元ID	${GNODEB}
	${paras}	create dictionary	taskName=${meId}_${time}	moType=${moType}	counters=${counters}	monitorObjMoIds=${monitorObjMoIds}
	${taskId}	创建实时KPI监控任务	${GNODEB}	${paras}
	${taskIdList}	create list	${taskId}
	sleep	120
	${filterDict1}	create dictionary	E-UTRAN FDD Cell ID=${monitorObjMoIds}
	${filterDictList1}	create list	${filterDict1}
	Comment	${filterDict2}	create dictionary	Cell portion list ID=2
	Comment	${filterDictList2}	create list	${filterDict2}
	${realKpiData1}	查询实时KPI监控数据	${GNODEB}	${taskId}	1	50	${filterDictList1}
	Comment	${realKpiData2}	查询实时KPI监控数据	${GNODEB}	${taskId}	1	50	${filterDictList2}
	${value}	过滤实时KPI指定列数据	${realKpiData1}	10
	Comment	${value}	Get Slice From List	${value}	0	-1
	${value}	列表求平均值	${value}
	Comment	${value2}	计算实时KPI指定列平均值	${realKpiData2}	10
	Comment	${value1}	evaluate	${value1}[0]
	Comment	${value2}	evaluate	${value2}[0]
	挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}

小区FDL PB端口功率
	[Return]	${eth1Power}	${eth2Power}
	${PB_25_2}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_2
	${eth1State}	PB供电状态检测_多模	${PB_25_2}	5
	${eth2State}	PB供电状态检测_多模	${PB_25_2}	7
	${eth1Power}	evaluate	${eth1State}[-1]
	${eth2Power}	evaluate	${eth2State}[-1]

配置FDL小区1射频合并
	${attrDict}	create dictionary	moId=19	refReplaceableUnit=512	antGroupNo=6	usedRxChannel=7-8	usedTxChannel=7-8
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruTxRxGroup	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=SectorFunction	moId=SF_FL11
	${attributeNameList}	create list	refPrruTxRxGroup
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}
	${refPrruTxRxGroup}	Get From Dictionary	${result[0]}	refPrruTxRxGroup
	${filterDict}	create dictionary	mocName=SectorFunction	moId=SF_FL11
	${attrDict}	create dictionary	refPrruTxRxGroup=${refPrruTxRxGroup};SupportFunction=1,PrruTxRxGroup=19
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}
	检查激活配置	${GNODEB}

配置FDL小区2射频合并
	${attrDict}	create dictionary	moId=20	refReplaceableUnit=514	antGroupNo=6	usedRxChannel=3-4	usedTxChannel=3-4
	${keyMoPathDict}	create dictionary	SupportFunction=1
	__创建节点	${GNODEB}	${attrDict}	${None}	PrruTxRxGroup	${keyMoPathDict}
	检查激活配置	${GNODEB}
	${filterDict}	create dictionary	mocName=SectorFunction	moId=SF_FL12
	${attributeNameList}	create list	refPrruTxRxGroup
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}
	${refPrruTxRxGroup}	Get From Dictionary	${result[0]}	refPrruTxRxGroup
	${filterDict}	create dictionary	mocName=SectorFunction	moId=SF_FL12
	${attrDict}	create dictionary	refPrruTxRxGroup=${refPrruTxRxGroup};SupportFunction=1,PrruTxRxGroup=20
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}
	检查激活配置	${GNODEB}

ping首包时延
	[Arguments]	${direction}	${packageSize}	${UE}	${PDN}
	[Return]	${avgDelayTime}	${name}	${value}
	${trafficId}	开始Ping	${direction}	${UE}	${PDN}	${packageSize}	1
	...	1000	${True}
	${pingNum}	Set Variable	110
	sleep	3
	${avgDelayTime}	${minDelayTime}	${maxDelayTime}	查询时延	${trafficId}	${direction}
	结束Ping	${trafficId}	${direction}
	${time}	get time
	${name}	create list	time	avgDelayTime	minDelayTime	maxDelayTime
	${value}	create list	${time}	${avgDelayTime}	${minDelayTime}	${maxDelayTime}
	sleep	10

FDDLTE小区符号关断子帧个数
	[Arguments]	${monitorObjAliases}
	[Return]	${value1}
	${time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	${meId}	获取网元ID	${GNODEB}
	${paras}	create dictionary	taskName=${meId}_${time}	moType=CUEUtranCellFDDLTE	counters=C373475046	monitorObjAliases=${monitorObjAliases}
	${taskId}	创建实时KPI监控任务	${GNODEB}	${paras}
	sleep	100
	${realKpiData1}	查询实时KPI监控数据	${GNODEB}	${taskId}	1	600
	${value1}	计算实时KPI指定列平均值	${realKpiData1}	10
	${value1}	evaluate	${value1}[0]
	${taskIdList}	create list	${taskId}
	挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}

NR业务性能验证
	[Arguments]	${CPE}	${cell}
	UE同步并接入NR小区成功_多模	${CPE}	${cell}
	${trafficId2}	开始UDP收灌包	RANDOM	${PDN}	${CPE}	DL	100m
	...	9999	1024	16
	sleep	30
	${dlValue}	查询UDP流量	${trafficId2}	DL
	run keyword and continue on failure	结束UDP收灌包	${trafficId2}	DL
	should be true	${dlValue} > 200

PRRU R8149 M182326 PA状态确定
	[Arguments]	${prruInfo}	${23state}	${26state}	${18state}
	${prru}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	${prruInfo}
	${result}	PRRU诊断测试_多模	${prru}
	${PA}	set variable	${result['pa']}
	${ant1PaState}	set variable	${PA[0][-1]}
	${ant2PaState}	set variable	${PA[1][-1]}
	${ant3PaState}	set variable	${PA[2][-1]}
	${ant4PaState}	set variable	${PA[3][-1]}
	${ant5PaState}	set variable	${PA[4][-1]}
	${ant6PaState}	set variable	${PA[5][-1]}
	${ant7PaState}	set variable	${PA[6][-1]}
	${ant8PaState}	set variable	${PA[7][-1]}
	should be equal	${ant1PaState}	${23state}
	should be equal	${ant2PaState}	${23state}
	should be equal	${ant3PaState}	${26state}
	should be equal	${ant4PaState}	${26state}
	should be equal	${ant5PaState}	${26state}
	should be equal	${ant6PaState}	${26state}
	should be equal	${ant7PaState}	${18state}
	should be equal	${ant8PaState}	${18state}

PRRU511 PB端口功率
	[Return]	${eth2Power}
	${PB_25_1}	根据名称或moId获取实例化单板别名_多模	${GNODEB}	PB1125F+PB_25_1
	${eth2State}	PB供电状态检测_多模	${PB_25_1}	7
	${eth2Power}	evaluate	${eth2State}[-1]

获取C370330103历史KPI值
	[Arguments]	${Id}
	[Return]	${C370330103}
	${countIdList}	create list	C370330103
	${kpiRlt}	查询测量数据_多模	${GNODEB}	OptPower	${None}	1	900
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][11]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${Id}"	exit for loop
	${C370330103}	evaluate	${data}[14].replace(',','')

验证节能后SON计数器
	Comment	${queryModleDict}	create dictionary	5101_ESDTX=me
	Comment	${time}	查询基站时间_多模	${GNODEB}
	Comment	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	15
	sleep	2100
	Comment	${filePathList}	create list
	Comment	: FOR	${queryModle}	IN	@{queryModleDict}
	Comment	${EMPTY}	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}
	Comment	${EMPTY}	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}	${endTime}	me	900
	Comment	${EMPTY}	append to list	${filePathList}	${filePath}
	${C613680000}	C613680000历史节能KPI查询	1
	${C613680004}	C613680004历史节能KPI查询	1
	${C613680010}	C613680010历史节能KPI查询	1
	${C613680014}	C613680014历史节能KPI查询	1
	${C616710001}	C616710001历史节能KPI查询	1
	${FDLC373475046}	FDLC373475046历史节能KPI查询	1
	${TDLC373475046}	TDLC373475046历史节能KPI查询	5
	Comment	${C613680000}	读取csv指定列的指标值	${filePath}	7
	Comment	${C613680004}	读取csv指定列的指标值	${filePath}	8
	Comment	${C613680010}	读取csv指定列的指标值	${filePath}	9
	Comment	${C613680014}	读取csv指定列的指标值	${filePath}	10
	Comment	${C616710001}	读取csv指定列的指标值	${filePath}	11
	Comment	${FDLC373475046}	读取csv指定列的指标值	${filePath}	12
	Comment	${TDLC373475046}	读取csv指定列的指标值	${filePath}	13
	should be true	${C613680000} > 0
	should be true	${C613680004} > 0
	should be true	${C613680010} > 0
	should be true	${C613680014} > 0
	should be true	${C616710001} > 0
	should be true	${FDLC373475046} > 0
	should be true	${TDLC373475046} > 0
	${countIdList}	create list	C370580002	C370580004
	${kpiRlt}	查询测量数据_多模	${GNODEB}	RruEnergy	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "511"	exit for loop
	${C370580002}	evaluate	${data}[12].replace(',','')
	${C370580004}	evaluate	${data}[12].replace(',','')
	should be true	${C370580002} > 0
	should be true	${C370580004} > 0

按模板任务获取历史性能指标数据
	[Arguments]	${queryModleDict}
	[Return]	${filePathList}
	${time}	查询基站时间_多模	${GNODEB}
	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	${testTime}
	Comment	${sleepTime}	evaluate	${testTime} * 60 + 900
	Comment	sleep	${sleepTime}
	${filePathList}	create list
	FOR	${queryModle}	IN	@{queryModleDict}
	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}
	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}	${endTime}
	...	me	900
	append to list	${filePathList}	${filePath}

按过滤条件读取csv指定列的指标值
	[Arguments]	${filePath}	${returnAttr}
	[Return]	${returnValue}
	${resList}	create list
	${attrList}	读取csv行数据_多模	${filePath}	0
	${returnValueIndex}	evaluate	${attrList}.index('${returnAttr}')
	Comment	${filterValueIndex}	evaluate	${attrList}.index('${filterAttr}')
	${returnValueList}	读取csv列数据_多模	${filePath}	${returnValueIndex}
	${returnValue}	evaluate	float(${returnValueList}[-1].replace(',',''))
	Comment	${returnValueTemp}	evaluate	float(${returnValueList}[1])
	Comment	${result}	evaluate	float(${returnValueList[-1]})
	Comment	${filterValueList}	读取csv列数据_多模	${filePath}	${filterValueIndex}
	Comment	${LENGTH}	evaluate	len(${returnValueList}) - 1
	Comment	: FOR	${i}	IN RANGE	${LENGTH}
	Comment	${EMPTY}	${returnValueTemp}	evaluate	float(${returnValueList}[${i}+1].replace(',',''))
	Comment	${EMPTY}	${filterValueTemp}	evaluate	${filterValueList}[${i}+1]
	Comment	${EMPTY}	run keyword if	'${filterValueTemp}' \ == '${filterValue}'	append to list	${resList}	${returnValueTemp}

读取csv指定列的指标值
	[Arguments]	${filePath}	${index}
	[Return]	${returnValue}
	Comment	${resList}	create list
	Comment	${attrList}	读取csv行数据_多模	${filePath}	0
	Comment	${returnValueIndex}	evaluate	${attrList}.index('${returnAttr}')
	Comment	${filterValueIndex}	evaluate	${attrList}.index('${filterAttr}')
	${returnValueList}	读取csv列数据_多模	${filePath}	${index}
	${returnValue}	evaluate	float(${returnValueList}[-1].replace(',',''))
	Comment	${returnValueTemp}	evaluate	float(${returnValueList}[1])
	Comment	${result}	evaluate	float(${returnValueList[-1]})
	Comment	${filterValueList}	读取csv列数据_多模	${filePath}	${filterValueIndex}
	Comment	${LENGTH}	evaluate	len(${returnValueList}) - 1
	Comment	: FOR	${i}	IN RANGE	${LENGTH}
	Comment	${EMPTY}	${returnValueTemp}	evaluate	float(${returnValueList}[${i}+1].replace(',',''))
	Comment	${EMPTY}	${filterValueTemp}	evaluate	${filterValueList}[${i}+1]
	Comment	${EMPTY}	run keyword if	'${filterValueTemp}' \ == '${filterValue}'	append to list	${resList}	${returnValueTemp}

验证节能停止SON计数器
	Comment	${queryModleDict}	create dictionary	5101_ESDTX=me
	Comment	${time}	查询基站时间_多模	${GNODEB}
	Comment	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	15
	sleep	2100
	Comment	${filePathList}	create list
	Comment	: FOR	${queryModle}	IN	@{queryModleDict}
	Comment	${EMPTY}	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}
	Comment	${EMPTY}	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}	${endTime}	me	900
	Comment	${EMPTY}	append to list	${filePathList}	${filePath}
	${C613680000}	C613680000历史节能KPI查询	1
	${C613680004}	C613680004历史节能KPI查询	1
	${C613680010}	C613680010历史节能KPI查询	1
	${C613680014}	C613680014历史节能KPI查询	1
	${C616710001}	C616710001历史节能KPI查询	1
	${FDLC373475046}	FDLC373475046历史节能KPI查询	1
	${TDLC373475046}	TDLC373475046历史节能KPI查询	5
	Comment	${C613680000}	读取csv指定列的指标值	${filePath}	7
	Comment	${C613680004}	读取csv指定列的指标值	${filePath}	8
	Comment	${C613680010}	读取csv指定列的指标值	${filePath}	9
	Comment	${C613680014}	读取csv指定列的指标值	${filePath}	10
	Comment	${C616710001}	读取csv指定列的指标值	${filePath}	11
	Comment	${FDLC373475046}	读取csv指定列的指标值	${filePath}	12
	Comment	${TDLC373475046}	读取csv指定列的指标值	${filePath}	13
	should be true	${C613680000} == 0
	should be true	${C613680004} == 0
	should be true	${C613680010} == 0
	should be true	${C613680014} == 0
	should be true	${C616710001} == 0
	should be true	${FDLC373475046} == 0
	should be true	${TDLC373475046} == 0
	${countIdList}	create list	C370580002	C370580004
	${kpiRlt}	查询测量数据_多模	${GNODEB}	RruEnergy	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	log	${replaceableUnitId}
	log	${data}
	run keyword if	"${replaceableUnitId}" == "511"	exit for loop
	${C370580002}	evaluate	${data}[12].replace(',','')
	${C370580004}	evaluate	${data}[12].replace(',','')
	should be true	${C370580002} == 0
	should be true	${C370580004} == 0

修改FDL超级小区1 辅CP异常
	[Arguments]	${adminState}=1
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=3
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=513
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=4
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=513
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=3
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=514
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=4
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=514
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}

修改FDL CP状态
	[Arguments]	${moId}	${adminState}=1
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=ECellEquipFuncFDDLTE	moId=${moId}
	${keyMoPathDict}	Create Dictionary	DULTE=1
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}

修改FDL超级小区1 主CP异常
	[Arguments]	${adminState}=1
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=7
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=511
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=8
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=511
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=7
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=512
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=8
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=512
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}

修改TDL超级小区5 主CP异常
	[Arguments]	${adminState}=1
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=1
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=511
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=2
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=511
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=1
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=512
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=2
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=512
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}

修改TDL超级小区5 辅CP异常
	[Arguments]	${adminState}=1
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=1
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=520
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	${attrDict}	Create Dictionary	adminState=${adminState}
	${filterDict}	Create Dictionary	mocName=Channel	moId=2
	${keyMoPathDict}	Create Dictionary	ReplaceableUnit=520
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	...	${keyMoPathDict}
	同步规划区数据_多模	${GNODEB}

TDL闭塞解闭塞主CP符号关断测试
	${attrDict}	Create Dictionary	adminState=1
	${filterDict}	Create Dictionary	mocName=ECellEquipFuncTDDLTE	moId=5-0
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	70
	判断可替换单元节能模式	511	${EMPTY}
	判断可替换单元节能模式	512	${EMPTY}
	${attrDict}	Create Dictionary	adminState=0
	${filterDict}	Create Dictionary	mocName=ECellEquipFuncTDDLTE	moId=5-0
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	180
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown

TDL闭塞解闭塞辅CP符号关断测试
	${attrDict}	Create Dictionary	adminState=1
	${filterDict}	Create Dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	sleep	90
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	${EMPTY}
	${attrDict}	Create Dictionary	adminState=0
	${filterDict}	Create Dictionary	mocName=ECellEquipFuncTDDLTE	moId=6-1
	${result}	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	${None}
	同步规划区数据_多模	${GNODEB}
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop	30	5
	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start	30	5
	sleep	180
	判断可替换单元节能模式	511	rfSymbolShutdown
	判断可替换单元节能模式	512	rfSymbolShutdown
	判断可替换单元节能模式	520	rfSymbolShutdown

节能前射频通道测量
	[Arguments]	${id}
	${countIdList}	create list	C370600000	C370600001	C370600002	C370600003	C370600004
	...	C370600005	C370600009	C370600010	C370600011	C370600015
	${kpiRlt}	查询测量数据_多模	${GNODEB}	AauChannel	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370600000}	evaluate	${data}[21].replace(',','')
	${C370600001}	evaluate	${data}[22].replace(',','')
	${C370600002}	evaluate	${data}[23].replace(',','')
	${C370600003}	evaluate	${data}[24].replace(',','')
	${C370600004}	evaluate	${data}[25].replace(',','')
	${C370600005}	evaluate	${data}[26].replace(',','')
	${C370600009}	evaluate	${data}[27].replace(',','')
	${C370600010}	evaluate	${data}[28].replace(',','')
	${C370600011}	evaluate	${data}[29].replace(',','')
	${C370600015}	evaluate	${data}[30].replace(',','')
	should be true	1 < ${C370600000} < 2
	should be true	50 < ${C370600001} < 70
	should be true	50 < ${C370600002} < 70
	should be true	50 < ${C370600003} < 70
	should be true	${C370600004} == 300
	should be true	${C370600005} == 0

节能前通道级载波测量
	[Arguments]	${id}
	${countIdList}	create list	C370590000	C370590001	C370590002	C370590003	C370590004
	...	C370590005
	${kpiRlt}	查询测量数据_多模	${GNODEB}	AauCarrier	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370590000}	evaluate	${data}[23].replace(',','')
	${C370590001}	evaluate	${data}[24].replace(',','')
	${C370590002}	evaluate	${data}[25].replace(',','')
	${C370590003}	evaluate	${data}[26].replace(',','')
	${C370590004}	evaluate	${data}[27].replace(',','')
	${C370590005}	evaluate	${data}[28].replace(',','')
	should be true	${C370590000} != 0
	should be true	${C370590001} != 0
	should be true	${C370590002} != 0
	should be true	${C370590003} < -80
	should be true	${C370590004} < -80
	should be true	${C370590005} < -80

节能前电源功率测量
	[Arguments]	${id}
	${countIdList}	create list	C370160000	C370160001	C370160002	C370160003	C370160004
	...	C370160005	C370160006	C370160007	C370160008
	${kpiRlt}	查询测量数据_多模	${GNODEB}	PrruPwr	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370160000}	evaluate	${data}[13].replace(',','')
	${C370160001}	evaluate	${data}[14].replace(',','')
	${C370160002}	evaluate	${data}[15].replace(',','')
	${C370160003}	evaluate	${data}[16].replace(',','')
	${C370160004}	evaluate	${data}[17].replace(',','')
	${C370160005}	evaluate	${data}[18].replace(',','')
	${C370160006}	evaluate	${data}[19].replace(',','')
	${C370160007}	evaluate	${data}[20].replace(',','')
	${C370160008}	evaluate	${data}[21].replace(',','')
	should be true	40 < ${C370160000} < 60
	should be true	40 < ${C370160001} < 60
	should be true	40 < ${C370160002} < 60
	should be true	40 < ${C370160003} < 60
	should be true	40 < ${C370160004} < 60
	should be true	40 < ${C370160005} < 60
	should be true	0 < ${C370160006} < 2
	should be true	0 < ${C370160007} < 2
	should be true	0 < ${C370160008} < 2

节能前RRU功率测量
	[Arguments]	${id}
	${countIdList}	create list	C370190000	C370190001
	${kpiRlt}	查询测量数据_多模	${GNODEB}	RruPwr	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370190000}	evaluate	${data}[12].replace(',','')
	${C370190001}	evaluate	${data}[13].replace(',','')
	should be true	${C370190000} < 0.1
	should be true	${C370190001} < 0.1

节能后射频通道测量
	[Arguments]	${id}
	${countIdList}	create list	C370600000	C370600001	C370600002	C370600003	C370600004
	...	C370600005	C370600009	C370600010	C370600011	C370600015
	${kpiRlt}	查询测量数据_多模	${GNODEB}	AauChannel	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370600000}	evaluate	${data}[21].replace(',','')
	${C370600001}	evaluate	${data}[22].replace(',','')
	${C370600002}	evaluate	${data}[23].replace(',','')
	${C370600003}	evaluate	${data}[24].replace(',','')
	${C370600004}	evaluate	${data}[25].replace(',','')
	${C370600005}	evaluate	${data}[26].replace(',','')
	${C370600009}	evaluate	${data}[27].replace(',','')
	${C370600010}	evaluate	${data}[28].replace(',','')
	${C370600011}	evaluate	${data}[29].replace(',','')
	${C370600015}	evaluate	${data}[30].replace(',','')
	should be true	1 < ${C370600000} < 2
	should be true	50 < ${C370600001} < 70
	should be true	50 < ${C370600002} < 70
	should be true	50 < ${C370600003} < 70
	should be true	${C370600004} == 300
	should be true	${C370600005} == 0

节能后通道级载波测量
	[Arguments]	${id}
	${countIdList}	create list	C370590000	C370590001	C370590002	C370590003	C370590004
	...	C370590005
	${kpiRlt}	查询测量数据_多模	${GNODEB}	AauCarrier	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370590000}	evaluate	${data}[23].replace(',','')
	${C370590001}	evaluate	${data}[24].replace(',','')
	${C370590002}	evaluate	${data}[25].replace(',','')
	${C370590003}	evaluate	${data}[26].replace(',','')
	${C370590004}	evaluate	${data}[27].replace(',','')
	${C370590005}	evaluate	${data}[28].replace(',','')
	should be true	${C370590000} != 0
	should be true	${C370590001} != 0
	should be true	${C370590002} != 0
	should be true	${C370590003} < -80
	should be true	${C370590004} < -80
	should be true	${C370590005} < -80

节能后电源功率测量
	[Arguments]	${id}
	${countIdList}	create list	C370160000	C370160001	C370160002	C370160003	C370160004
	...	C370160005	C370160006	C370160007	C370160008
	${kpiRlt}	查询测量数据_多模	${GNODEB}	PrruPwr	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370160000}	evaluate	${data}[13].replace(',','')
	${C370160001}	evaluate	${data}[14].replace(',','')
	${C370160002}	evaluate	${data}[15].replace(',','')
	${C370160003}	evaluate	${data}[16].replace(',','')
	${C370160004}	evaluate	${data}[17].replace(',','')
	${C370160005}	evaluate	${data}[18].replace(',','')
	${C370160006}	evaluate	${data}[19].replace(',','')
	${C370160007}	evaluate	${data}[20].replace(',','')
	${C370160008}	evaluate	${data}[21].replace(',','')
	should be true	30 < ${C370160000} < 60
	should be true	30 < ${C370160001} < 60
	should be true	30 < ${C370160002} < 60
	should be true	40 < ${C370160003} < 60
	should be true	40 < ${C370160004} < 60
	should be true	40 < ${C370160005} < 60
	should be true	0 < ${C370160006} < 2
	should be true	0 < ${C370160007} < 2
	should be true	0 < ${C370160008} < 2

节能后RRU功率测量
	[Arguments]	${id}
	${countIdList}	create list	C370190000	C370190001
	${kpiRlt}	查询测量数据_多模	${GNODEB}	RruPwr	${None}	1	300
	...	${countIdList}
	${totalcount}	evaluate	len(${kpiRlt})
	FOR	${i}	IN RANGE	${totalcount}
	log	${i}
	${replaceableUnitId}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values'][10]
	${data}	evaluate	${kpiRlt}[${totalcount}-${i}-1]['values']
	run keyword if	"${replaceableUnitId}" == "${id}"	exit for loop
	${C370190000}	evaluate	${data}[12].replace(',','')
	${C370190001}	evaluate	${data}[13].replace(',','')
	should be true	${C370190000} < 0.1
	should be true	${C370190001} < 0.1

修改TDL小区状态
	[Arguments]	${moId}	${adminState}
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moId}
	${attrDict}	create dictionary	adminState=${adminState}
	${keyMoPathDict}	create dictionary	CULTE=1
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}


