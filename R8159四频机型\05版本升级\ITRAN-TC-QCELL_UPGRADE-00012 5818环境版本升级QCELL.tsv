*Settings*							
Suite Setup	加载配置	NRRequest	${dataset}				
Suite Teardown	删除配置						
Variables	NRRequest.py						
Resource	omc.tsv						
Resource	D:/script_v2/5GNR/testlib5g/infrastructure/resource/resource.tsv						
Resource	D:/script_v2/5GNR/Test/userkeywords/basic_multi/resource.tsv						
Resource	../../../../userkeywords/basic/utility/excelHandle.tsv						
Resource	../template.tsv						
							
*Test Cases*							
TC-jenkins-0020-00012 5818环境基站版本升级	Comment	${tarName}	获取防护网待升级版本号_多模	oam	OamTest2019	qcell_version.txt	
	${tarVersion}	查询TAR包版本号_多模	${GNODEB}	UNI_V5.75.20.10R12.tar			
	${version}	查询基站运行版本号_多模	${GNODEB}	SOFTWARE			
	Run keyword If	'${version}'=='${tarVersion}'	sleep	450			
	...	ELSE	ITRAN版本升级	${GNODEB}	UNI_V5.75.10.20F15.11010603.tar	${tarVersion}	SOFTWARE
	${version}	查询基站运行版本号_多模	${GNODEB}	COLDPATCH			
	${tarColdName}	获取防护网待升级版本号_多模	oam	OamTest2019	qcell_patch_version.txt		
	保存基站MO_多模	${GNODEB}	PrruSavingArray				
	${tarVersion}	查询TAR包版本号_多模	${GNODEB}	${tarColdName}			
	Run keyword If	(''!='${tarVersion}') & ('${version}'!='${tarVersion}')	ITRAN版本升级	${GNODEB}	${tarColdName}	${tarVersion}	COLDPATCH
	${hotName}	获取防护网待升级版本号_多模	oam	OamTest2019	qcell_hot_patch_version.txt		
	Run keyword If	'${hotName}'!='None'	删除基站所有升级任务_多模	${GNODEB}			
	Run keyword If	'${hotName}'!='None'	升级热补丁版本_多模	${GNODEB}	${hotName}		
	${XML_PATH}	导出基站XML并备份	${GNODEB}	${UME}			
							
*Keywords*							
删除配置	删除基站_多模	${GNODEB}					
							
加载配置	[Arguments]	${scene}	${dataset}				
	获取资源	${scene}	${dataset}				
	创建基站_多模	${GNODEB}	${UME}				
	${version}	查询基站运行版本	${GNODEB}	${UME}			
	${meid_gnb}	查询环境设备属性值	${GNODEB}	meId			
	run keyword and ignore error	导入基站数据_多模	${GNODEB}	D:/0基站配置备份/CfgBackup_${meid_gnb}_${version}.xml			
	sleep	1200					
							
导出基站XML并备份	[Arguments]	${gnbAlias}	${omcAlias}	${isOverWriteFile}=False			
	[Documentation]	导出基站xml，并保存在D:/0基站配置备份 目录下，返回文件目录。如果该版本的xml存在，则不会覆盖。					
	...	该文件用来在测试套结束时恢复环境用。					
	${version}	查询基站运行版本	${gnbAlias}	${omcAlias}			
	${meid}	查询环境设备属性值	${gnbAlias}	meId			
	${xmlPath}	set variable	D:/0基站配置备份/CfgBackup_${meid}_${version}.xml				
	${isFile}	判断文件是否存在	${xmlPath}				
	return from keyword if	('${isOverWriteFile}'=='False') & ('${isFile}' == 'True')	${xmlPath}				
	${xml}	导出基站数据_多模	${gnbAlias}				
	${xmlPath}	复制文件并重命名_多模	${xml}	D:/0基站配置备份	CfgBackup_${meid}_${version}.xml	${isOverWriteFile}	
	[Return]	${xmlPath}					
							
ITRAN版本升级	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE		
	删除基站所有升级任务_多模	${enodebAlias}					
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}	
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	ITRAN基站开站	${enodebAlias}	${tarName}		
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200			
	${pkgVersion}	重复执行_多模	30	查询基站运行版本号_多模	${enodebAlias}	${pkgType}	
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}				
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR	
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR		
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}		
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}			
							
ITRAN基站开站	[Arguments]	${enodebAlias}	${tarName}				
	run keyword and ignore error	拆分NR超级小区	${enodebAlias}				
	保存基站MO_多模	${enodebAlias}	ExternalNrCellLTE				
	保存基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE				
	保存基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE				
	保存基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE				
	保存基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE				
	保存基站MO_多模	${enodebAlias}	GlobalConfigLTE				
	保存基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE				
	保存基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE				
	保存基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE				
	保存基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE				
	保存基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE				
	保存基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE				
	保存基站MO_多模	${enodebAlias}	SsbMeasInfo				
	保存基站MO_多模	${enodebAlias}	NRCellRelation				
	保存基站MO_多模	${enodebAlias}	ExternalNRCellCU				
	保存基站MO_多模	${enodebAlias}	NRFreq				
	保存基站MO_多模	${enodebAlias}	FrequencyBandList				
	保存基站MO_多模	${enodebAlias}	NRFreqRelation				
	保存基站MO_多模	${enodebAlias}	InterFHoA1A2				
	保存基站MO_多模	${enodebAlias}	CoverMobilityCtrl				
	保存基站MO_多模	${enodebAlias}	NRRadioInfrastructure				
	保存基站MO_多模	${enodebAlias}	X2Ap				
	保存基站MO_多模	${enodebAlias}	ENDCX2Ap				
	保存基站MO_多模	${enodebAlias}	Ip				
	保存基站MO_多模	${enodebAlias}	Sctp				
	保存基站MO_多模	${enodebAlias}	FrequencyBandOffset				
	保存基站MO_多模	${enodebAlias}	PrruPowerSupplyConfig				
	保存基站MO_多模	${enodebAlias}	GNBCUUPFunction				
	保存基站MO_多模	${enodebAlias}	GNBCUCPFunction				
	保存基站MO_多模	${enodebAlias}	GNBDUFunction				
	保存基站MO_多模	${enodebAlias}	PlmnIdListUP				
	保存基站MO_多模	${enodebAlias}	PlmnIdListCU				
	保存基站MO_多模	${enodebAlias}	PlmnIdList				
	保存基站MO_多模	${enodebAlias}	NetworkSliceSubnet				
	保存基站MO_多模	${enodebAlias}	SliceProfile				
	保存基站MO_多模	${enodebAlias}	NSSAI				
	保存基站MO_多模	${enodebAlias}	NgAp				
	保存基站MO_多模	${enodebAlias}	Clk				
	保存基站MO_多模	${enodebAlias}	ClockSyncConfig				
	保存基站MO_多模	${enodebAlias}	XnAp				
	保存基站MO_多模	${enodebAlias}	NRFreqObj				
	保存基站MO_多模	${enodebAlias}	ExternalNrCell				
	保存基站MO_多模	${enodebAlias}	EnDCCtrl				
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}			
	修改开站模板文件_多模	${filePath}	${tarName}				
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4	
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}			
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}		
	sleep	1200					
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR	
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR		
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}		
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}			
	创建基站MO_多模	${enodebAlias}	ExternalNrCellLTE				
	创建基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE				
	创建基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE				
	创建基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE				
	创建基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE				
	修改基站MO_多模	${enodebAlias}	GlobalConfigLTE				
	修改基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE				
	修改基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE				
	修改基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE				
	修改基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE				
	创建基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE				
	创建基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE				
	创建基站MO_多模	${enodebAlias}	NRFreq				
	创建基站MO_多模	${enodebAlias}	FrequencyBandList				
	创建基站MO_多模	${enodebAlias}	ExternalNRCellCU				
	创建基站MO_多模	${enodebAlias}	SsbMeasInfo				
	创建基站MO_多模	${enodebAlias}	NRCellRelation				
	创建基站MO_多模	${enodebAlias}	NRFreqRelation				
	修改基站MO_多模	${enodebAlias}	InterFHoA1A2				
	修改基站MO_多模	${enodebAlias}	CoverMobilityCtrl				
	修改基站MO_多模	${enodebAlias}	FrequencyBandOffset				
	恢复基站MO_多模	${enodebAlias}	XnAp				
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NRFreqObj			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	ExternalNrCell			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	EnDCCtrl			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NRRadioInfrastructure			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	X2Ap			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	ENDCX2Ap			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	Ip			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	Sctp			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	PrruPowerSupplyConfig			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBCUUPFunction			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBCUCPFunction			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBDUFunction			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdListUP			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdListCU			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdList			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NetworkSliceSubnet			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	SliceProfile			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NSSAI			
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NgAp			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	Clk			
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	ClockSyncConfig			
							
拆分NR超级小区	[Arguments]	${enodebAlias}					
	@{ids}	查询NR超级小区主小区IDS_多模	${enodebAlias}				
	: FOR	${id}	IN	@{ids}			
	\	拆分NR超级小区_多模	${enodebAlias}	${id}			
