*** Settings ***
Documentation    大文件性能测试 - 包含大量中文内容
Library          SeleniumLibrary
Library          Collections
Library          String
Library          DateTime
Resource         common.robot
Variables        variables.py

Suite Setup      初始化测试环境
Suite Teardown   清理测试环境
Test Setup       准备测试数据
Test Teardown    清理测试数据

*** Variables ***
${BASE_URL}      https://www.example.com
${BROWSER}       chrome
${TIMEOUT}       30s
${USERNAME}      测试用户
${PASSWORD}      测试密码123
@{USERS}         管理员    普通用户    访客用户    测试用户
&{CONFIG}        host=localhost    port=8080    debug=true    timeout=30

*** Test Cases ***
用户登录功能测试
    [Documentation]    测试用户登录功能的完整流程
    [Tags]    登录    smoke    critical    用户管理
    [Setup]    准备登录测试环境
    [Teardown]    清理登录测试数据
    
    打开登录页面    ${BASE_URL}
    输入用户名    ${USERNAME}
    输入密码      ${PASSWORD}
    点击登录按钮
    验证登录成功
    检查用户信息显示    ${USERNAME}

用户注册功能测试
    [Documentation]    测试新用户注册功能
    [Tags]    注册    功能测试    用户管理
    
    打开注册页面
    填写注册信息    新用户名    新密码123    <EMAIL>
    点击注册按钮
    验证注册成功
    验证邮件发送    <EMAIL>

搜索功能测试
    [Documentation]    测试搜索功能的各种场景
    [Tags]    搜索    功能测试
    
    打开搜索页面
    输入搜索关键字    robot framework
    点击搜索按钮
    验证搜索结果包含    robot framework
    检查搜索结果数量    大于    0

商品管理测试
    [Documentation]    测试商品管理功能
    [Tags]    商品    管理    功能测试
    
    登录管理员账户    管理员    admin123
    进入商品管理页面
    添加新商品    测试商品    100.00    这是一个测试商品
    验证商品添加成功    测试商品
    编辑商品信息    测试商品    修改后的商品    150.00
    验证商品修改成功    修改后的商品
    删除商品    修改后的商品
    验证商品删除成功

订单处理测试
    [Documentation]    测试订单处理流程
    [Tags]    订单    处理    业务流程
    
    创建测试订单    ${USERNAME}    测试商品    2
    验证订单创建成功
    处理订单支付    支付宝    100.00
    验证支付成功
    更新订单状态    已发货
    验证订单状态更新    已发货

数据导入导出测试
    [Documentation]    测试数据导入导出功能
    [Tags]    数据    导入导出    功能测试
    
    准备测试数据文件    test_data.csv
    执行数据导入    test_data.csv
    验证数据导入成功    100
    执行数据导出    exported_data.csv
    验证导出文件存在    exported_data.csv
    比较导入导出数据    test_data.csv    exported_data.csv

权限管理测试
    [Documentation]    测试用户权限管理
    [Tags]    权限    管理    安全测试
    
    创建测试角色    测试角色    基本权限
    分配权限给角色    测试角色    查看权限    编辑权限
    创建测试用户    测试用户123    test123
    分配角色给用户    测试用户123    测试角色
    验证用户权限    测试用户123    查看权限    编辑权限

API接口测试
    [Documentation]    测试API接口功能
    [Tags]    API    接口测试    自动化
    
    发送GET请求    /api/users    200
    验证响应数据格式    JSON
    发送POST请求    /api/users    {"name": "新用户", "email": "<EMAIL>"}    201
    验证用户创建成功    新用户
    发送PUT请求    /api/users/1    {"name": "更新用户"}    200
    验证用户信息更新    更新用户
    发送DELETE请求    /api/users/1    204
    验证用户删除成功

性能测试
    [Documentation]    测试系统性能
    [Tags]    性能    压力测试    非功能测试
    
    FOR    ${i}    IN RANGE    1    101
        创建并发用户    用户${i}
        执行登录操作    用户${i}    密码${i}
        Log    用户${i}登录完成
    END
    
    统计响应时间
    验证平均响应时间    小于    2秒
    验证成功率    大于    95%

数据库测试
    [Documentation]    测试数据库操作
    [Tags]    数据库    数据验证
    
    连接测试数据库    localhost    test_db    root    password
    执行SQL查询    SELECT COUNT(*) FROM users
    验证查询结果    大于    0
    插入测试数据    INSERT INTO users (name, email) VALUES ('测试用户', '<EMAIL>')
    验证数据插入成功    测试用户
    更新测试数据    UPDATE users SET email='<EMAIL>' WHERE name='测试用户'
    验证数据更新成功    <EMAIL>
    删除测试数据    DELETE FROM users WHERE name='测试用户'
    验证数据删除成功

*** Keywords ***
初始化测试环境
    [Documentation]    初始化整个测试套件的环境
    Log    开始初始化测试环境
    Set Global Variable    ${TEST_START_TIME}    ${EMPTY}
    ${start_time}=    Get Current Date
    Set Global Variable    ${TEST_START_TIME}    ${start_time}
    Log    测试环境初始化完成: ${start_time}

清理测试环境
    [Documentation]    清理测试环境
    Log    开始清理测试环境
    ${end_time}=    Get Current Date
    ${duration}=    Subtract Date From Date    ${end_time}    ${TEST_START_TIME}
    Log    测试总耗时: ${duration}秒
    Log    测试环境清理完成

准备测试数据
    [Documentation]    为每个测试用例准备数据
    Log    准备测试数据
    Set Test Variable    ${TEST_DATA_READY}    True

清理测试数据
    [Documentation]    清理测试用例产生的数据
    Log    清理测试数据
    Set Test Variable    ${TEST_DATA_READY}    False

打开登录页面
    [Arguments]    ${url}=${BASE_URL}
    [Documentation]    打开登录页面
    Log    打开登录页面: ${url}
    Open Browser    ${url}/login    ${BROWSER}
    Maximize Browser Window
    Wait Until Page Contains    登录    timeout=${TIMEOUT}

输入用户名
    [Arguments]    ${username}
    [Documentation]    输入用户名
    Log    输入用户名: ${username}
    Input Text    id=username    ${username}
    Sleep    0.5s

输入密码
    [Arguments]    ${password}
    [Documentation]    输入密码
    Log    输入密码: ****
    Input Password    id=password    ${password}
    Sleep    0.5s

点击登录按钮
    [Documentation]    点击登录按钮
    Log    点击登录按钮
    Click Button    id=login-btn
    Sleep    2s

验证登录成功
    [Documentation]    验证用户成功登录
    Log    验证登录成功
    Wait Until Page Contains    欢迎    timeout=${TIMEOUT}
    Page Should Contain    用户中心
    ${current_url}=    Get Location
    Should Contain    ${current_url}    dashboard

检查用户信息显示
    [Arguments]    ${expected_username}
    [Documentation]    检查用户信息是否正确显示
    Log    检查用户信息显示: ${expected_username}
    Wait Until Page Contains    ${expected_username}    timeout=${TIMEOUT}
    Element Should Contain    id=user-info    ${expected_username}

# 这是中文注释 - 测试注释高亮
# Comment 这是Comment关键字注释
# 混合中英文注释 Mixed Chinese English Comment
