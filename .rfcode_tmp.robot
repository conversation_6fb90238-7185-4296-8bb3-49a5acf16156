*** Settings ***


*** Test Cases ***
BS8912-TC-SM-010-001 8912单U配置下CS业务呼叫稳定性
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 4	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3}	primaryCpichPower=24
	Comment	===UMTS临区配置==========
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3}	repeat = 1800
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_MCP数据卡更新NV_多模	ueid=193-208	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-224	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=225-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-256	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=257-272	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-288	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	comment	==UE 注册到小区=======	===================
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell3}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-208	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=209-224	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-256	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=257-272	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=273-288	psl = cs12.2k_repeat.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-288
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200	task300=${cell1} + ${cell2}+${cell3}
	comment
	${time1}=	业务_查询当前时间_多模
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	业务_等待时间_多模	timeout = 3600
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 300	${EMPTY}	${EMPTY}	lili pass
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 稳定性
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	${EMPTY}	${EMPTY}	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下HSPA务呼叫稳定性
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3}	primaryCpichPower=24
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	Comment	===UMTS临区配置==========
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3}	repeat = 3600
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_MCP数据卡更新NV_多模	ueid=193-208	freq=10563, rrc=2, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-224	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=225-240	freq=10588, rrc=2, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-256	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=257-272	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-288	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	comment	==UE 注册到小区=======	===================	===================
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell3}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-208	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=209-224	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-256	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=257-272	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=273-288	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-288
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200	task300=${cell1} + ${cell2}+${cell3}
	${time1}=	业务_查询当前时间_多模
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	业务_等待时间_多模	timeout = 3600
	comment
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 300	${EMPTY}	${EMPTY}	lili pass
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 稳定性
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下混合业务呼叫稳定性业务呼叫稳定性
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3}	primaryCpichPower=24
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	Comment	===UMTS临区配置==========
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3}	repeat = 3600
	业务_HOC设置端口衰减值_多模	ort4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-224	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=225-226	freq=10588, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=227-230	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-248	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-250	freq=10613, rrc=2, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=251-253	freq=10613, rrc=1, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=254-258	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=259-264	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=265-270	freq=10563, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=271-272	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=282-288	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	comment	==UE 注册到小区=======	===================	===================
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell3}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-288
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200	task300=${cell1} + ${cell2}+${cell3}
	${time1}=	业务_查询当前时间_多模
	业务_HOC设置端口衰减值_多模	ort4 = 31, port5 = 31 port2 = 0, port3 = 0
	业务_等待时间_多模	timeout = 3600
	comment
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 300	${EMPTY}	${EMPTY}	lili pass
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=192-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 稳定性
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下单基带板板最大支持6小区业务接入测试
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell5}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2122.6 ,rxfreq =1932.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+5km+5m	sectorid=2
	${cell6}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq =2122.6 ,rxfreq =1932.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4} + ${cell5} \ +${cell6}	primaryCpichPower=23
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_配置UMTS小区邻区_多模	host = ${cell5}, neighbor = ${cell6}
	业务_配置UMTS小区邻区_多模	host = ${cell6}, neighbor = ${cell5}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4} + ${cell5} +${cell6}	repeat = 3600
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-216	freq=10588, rrc=1, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=217-220	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=221-224	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=225-228	freq=10613, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=229-230	freq=10613, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10613, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10613, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=241-246	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=247-248	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-252	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=252-256	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=257-262	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=263-264	freq=10588, rrc=1, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=265-268	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=269-272	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10613, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10613, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=283-285	freq=10613, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=286-288	freq=10613, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	comment	==UE 注册到小区=======	===================	===================
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell4}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell5}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell6}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-208	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=209-214	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=209-224	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-228	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=228-240	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-246	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=247-256	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=257-262	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=262-272	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=273-276	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=276-288	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200	task300 =${cell1} + ${cell2}+${cell3} + ${cell4} + ${cell5} +${cell6}
	${time1}=	业务_查询当前时间_多模
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 31
	业务_等待时间_多模	timeout = 3600
	业务_HOC循环切换_多模	path =2 <> 3 ,duration =3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 稳定性
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下并发语音业务的并发成功率
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1+2，sector = 1t2r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 3	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3}	primaryCpichPower=24
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	Comment	===UMTS临区配置==========
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3}	repeat = 3600
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_MCP数据卡更新NV_多模	ueid=193-208	freq=10563, rrc=2, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-224	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=225-240	freq=10588, rrc=2, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-256	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=257-272	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-288	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	comment	==UE 注册到小区=======	===================	===================
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell3}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-208	psl = RNC105_CS+PS.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=209-224	psl = RNC105_CS+PS.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_CS+PS.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-256	psl = RNC105_CS+PS.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=257-272	psl = RNC105_CS+PS.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=273-288	psl = RNC105_CS+PS.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-288
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3}
	${time1}=	业务_查询当前时间_多模
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	业务_等待时间_多模	timeout = 3600
	comment
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 300	lili pass
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 稳定性
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下基带板内更软切换区混合业务切换成功率
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant =2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	primaryCpichPower=24
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	repeat = 3600
	业务_HOC设置端口衰减值_多模	ort4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-224	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=225-226	freq=10588, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=227-230	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-248	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-250	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=251-253	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=254-258	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=259-264	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=265-270	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=271-272	freq=10588, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=282-288	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	comment	==UE 注册到小区=======	===================
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell4}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-240
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPA.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3} + ${cell4}
	${time1}=	业务_查询当前时间_多模
	业务_等待时间_多模	timeout = 60
	comment
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 31
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 切换
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下硬切换区混合业务切换成功率（R6小区之间）
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	primaryCpichPower=20
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	repeat = 3600
	业务_HOC设置端口衰减值_多模	ort4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-224	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=225-226	freq=10588, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=227-230	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-248	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-250	freq=10613, rrc=2, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=251-253	freq=10613, rrc=1, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=254-258	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=259-264	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=265-270	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=271-272	freq=10613, rrc=2, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10613, rrc=1, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=282-288	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	Comment	UE注册小区
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell4}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPA.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3} + ${cell4}
	${time1}=	业务_查询当前时间_多模
	业务_等待时间_多模	timeout = 60
	comment
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 31
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 切换
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下R6小区与R99小区混合业务硬切换成功率
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 5	feature = r99+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	primaryCpichPower=20
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	repeat = 3600
	业务_HOC设置端口衰减值_多模	ort4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=0, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-224	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=225-226	freq=10588, rrc=0, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=227-230	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-248	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-250	freq=10613, rrc=0, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=251-253	freq=10613, rrc=1, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=254-258	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=259-264	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=265-270	freq=10638, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=271-272	freq=10638, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10638, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10638, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=282-288	freq=10638, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	Comment	UE注册小区
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell4}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPA.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3} + ${cell4}
	${time1}=	业务_查询当前时间_多模
	业务_等待时间_多模	timeout = 300
	comment
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 31
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 切换
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模

BS8912-TC-SM-************单U配置下R6小区与R5小区混合业务硬切换成功率
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2122.6,rxfreq = 1932.6,maxDlPwr = 5	feature = r99+r5+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	primaryCpichPower=20
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	repeat = 3600
	业务_HOC设置端口衰减值_多模	ort4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=0, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-224	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=225-226	freq=10588, rrc=0, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=227-230	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-248	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-250	freq=10613, rrc=0, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=251-253	freq=10613, rrc=1, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=254-258	freq=10613, rrc=3, dpa=14, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=259-264	freq=10613, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=265-270	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=271-272	freq=10588, rrc=0, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=282-288	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	Comment	UE注册小区
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell4}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = serHoldCs12.2k.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPA.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3} + ${cell4}
	${time1}=	业务_查询当前时间_多模
	业务_等待时间_多模	timeout = 300
	comment
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 15
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 切换
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模

BS8912-TC-SM-************单U配置下基带板内更软切换区混合业务移动呼通率
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant =2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	primaryCpichPower=24
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	repeat = 3600
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡更新NV_多模	ueid=193-200	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=201-202	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=203-204	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=205-208	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-214	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=215-224	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=225-226	freq=10588, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=227-230	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=231-234	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=235-240	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=241-248	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=249-250	freq=10563, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=251-253	freq=10563, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=254-258	freq=10563, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=259-264	freq=10563, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=265-270	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡更新NV_多模	ueid=271-272	freq=10588, rrc=2, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-276	freq=10588, rrc=1, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=277-282	freq=10588, rrc=3, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=282-288	freq=10588, rrc=4, dpa=24, upa=5, amr=10
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	Comment	UE注册小区
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell4}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = cs12.2k_repeat.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPARepeat.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3} + ${cell4}
	${time1}=	业务_查询当前时间_多模
	业务_等待时间_多模	timeout = 60
	comment
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31, port2 = 0, port3 = 31
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 切换
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模

BS8912-TC-SM-************单U配置下DC HSDPA小区间的同频更软切换
	comment	===================	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)	lili pass
	业务_识别小基站环境_多模	env = 8912 单U
	业务_网管导出数据_多模	${EMPTY}	${EMPTY}	lili pass
	业务_MCP数据卡RF关闭_多模	ueid=193-384
	业务_删除所有小区_多模
	Comment	=====UMTS小区配置=====	BS8902-TC-SM-010-001 单U配置下的CS业务呼叫稳定性(STSR)
	业务_更新NodeBFunction_多模	basebandPoolSwitch = 0, autoAssignBpResSwitch = 1
	${cell1}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=1
	${cell2}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 1-1，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=1
	${cell3}=	业务_创建UMTS小区小基站_多模	band = band1,ant = 2-2，sector = 1t1r,txfreq = 2112.6,rxfreq = 1922.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+2.5km+5m	sectorid=2
	${cell4}=	业务_创建UMTS小区小基站_多模	band = band1,ant =2-2，sector = 1t1r,txfreq =2117.6 ,rxfreq =1927.6,maxDlPwr = 5	feature = r99+r5+r6+2ms+5km+5m	sectorid=2
	业务_更新UMTS小区_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	primaryCpichPower=24
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell1},auxiliary1 = ${cell2},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell2},auxiliary1 = ${cell1},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell3},auxiliary1 = ${cell4},
	业务_创建DC关系_多模	type = multi cell hsdpa	main=${cell4},auxiliary1 = ${cell3},
	Comment	===UMTS临区配置==========
	业务_配置UMTS小区邻区_多模	host = ${cell1}, neighbor = ${cell3}
	业务_配置UMTS小区邻区_多模	host = ${cell3}, neighbor = ${cell1}
	业务_配置UMTS小区邻区_多模	host = ${cell2}, neighbor = ${cell4}
	业务_配置UMTS小区邻区_多模	host = ${cell4}, neighbor = ${cell2}
	业务_同步网管数据_多模
	业务_查询UMTS小区状态_多模	cell =${cell1} + ${cell2}+${cell3} + ${cell4}	repeat = 3600
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31 port2 = 0, port3 = 0
	comment	==UE NV参数修改=======	===================	===================
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡更新NV_多模	ueid=193-208	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=209-224	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=225-240	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=241-256	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡更新NV_多模	ueid=257-272	freq=10563, rrc=4, dpa=24, upa=6, amr=5
	业务_MCP数据卡更新NV_多模	ueid=273-288	freq=10588, rrc=4, dpa=24, upa=5, amr=5
	业务_MCP数据卡重启_多模	ueid=193-288
	业务_MCP数据卡RF关闭_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	业务_MCP数据卡RF开启_多模	ueid=193-288
	Comment	UE注册小区
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=193-208	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=209-224	cell=${cell1}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=225-240	cell=${cell2}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=241-256	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=257-272	cell=${cell3}
	业务_MCP数据卡注册UMTS服务小区_多模	ueid=273-288	cell=${cell4}
	业务_MCP数据卡注销UMTS服务小区_多模	ueid=193-288
	业务_等待时间_多模	timeout = 5
	Comment	==UE 下载脚本=======
	业务_MCP数据卡下载psl脚本_多模	ueid=193-200	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=201-214	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=215-224	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=225-240	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=241-248	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=248-264	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=265-270	psl = RNC105_DPA.psl
	业务_MCP数据卡下载psl脚本_多模	ueid=271-288	psl = RNC105_DPA.psl
	业务_MCP数据卡启动psl脚本_多模	ueid=193-240
	业务_EPMS测量获取本地时间_多模
	业务_EPMS测量删除所有任务_多模
	业务_EPMS测量任务创建启动_多模	task100=100,task200=200,	task300=${cell1} + ${cell2}+${cell3} + ${cell4}
	${time1}=	业务_查询当前时间_多模
	业务_等待时间_多模	timeout = 60
	comment
	业务_HOC设置端口衰减值_多模	port4 = 31, port5 = 31, port2 = 0, port3 = 31
	业务_HOC循环切换_多模	path =2 <> 3 ,duration = 3600
	${time2}=	业务_查询当前时间_多模
	业务_MCP数据卡停止psl脚本_多模	ueid=193-288
	comment	====================	===================	===================	lili pass
	业务_配置通过准则_多模	xlsx = 通过准则_0917.xlsx，sheet = 切换
	业务_执行结果处理_多模	type = kpi + epms + alarm， act = query + check， starttime = ${time1}， endtime = ${time2}， templateId=性能+切换---外场常用KPI查询	lili pass
	业务_删除所有小区_多模
	业务_同步网管数据_多模



