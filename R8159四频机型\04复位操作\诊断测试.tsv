*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-5586568 PB单板资源利用率查询__RAN-5586568	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${res}	查询PB资源使用率_多模	${board}			
	\	确认PB资源使用率正确_多模	${board}	${res}			
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586555 PB单板运行时间查询__RAN-5586555	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${res}	查询PB工作时长_多模	${board}			
	\	确认PB工作时长正确_多模	${board}	${res}			
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586533 PB单板，POE供电口状态诊断__RAN-5586533	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	PB供电状态检测_多模	${board}				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586544 PB单板，SNR诊断__RAN-5586544	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	PB以太网口SNR诊断	${board}				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586543 PB单板，光/电口状态检测(只含光口)__RAN-5586543	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}[1]			
	\	PB光口状态诊断	${board}				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586576 PB单板，光/电口误码率查询(只含光口)__RAN-5586576	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}[1]			
	\	PB光口误码率诊断	${board}				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586584 PB单板，光/电模块诊断查询__RAN-5586584	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}[1]			
	\	PB光/电模块诊断	${board}				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586498 PB单板，诊断测试__RAN-5586498	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${res}	PB诊断测试_多模	${board}			
	\	确认PB诊断测试正确_多模	${board}	${res}			
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586528 PB单板，光纤测距__RAN-5586528	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${result}	PB光纤测距_多模	${board}			
	\	should be true	'${result[2]}'=='Shorter Than 200 Meters'				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-5586570 PB单板诊断测试，结果返回单板ID__RAN-5586570	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${result}	PB诊断查询单板类型FID_多模	${board}			
	\	should be true	'${result}' == '0x15'				
	[Teardown]						
							
RAN-5586572 UME上查询PB1125H的输入电压__RAN-5586572	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${res}	PB诊断电压_多模	${board}			
	\	should be true	${res} < 240				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
RAN-6032760 PB1125H长时间正常运行的内存使用检测__RAN-6032760	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${ENODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${res}	查询PB资源使用率_多模	${board}			
	\	确认PB资源使用率正确_多模	${board}	${res}			
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]						
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	Comment	创建UE对象	${CPE}				
	Comment	创建UE对象	${CPE2}				
	Comment	创建UE对象	${CPE3}				
	Comment	创建UE对象	${CPE4}				
	Comment	创建PDN	${PDN}				
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	Comment	关闭告警防抖_多模	${ENODEB}				
	Comment	获取所有小区别名					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	Comment	导入基站数据_多模	${GNODEB}	${XML_PATH}			
	Comment	删除UE对象	${CPE}				
	Comment	删除UE对象	${CPE2}				
	Comment	删除UE对象	${CPE3}				
	Comment	删除UE对象	${CPE4}				
	Comment	删除PDN	${PDN}				
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
PB以太网口SNR诊断	[Arguments]	${board}					
	: FOR	${i}	IN RANGE	8			
	\	${res}	PB以太网口SNR诊断_多模	${board}	${i}		
	\	确认PB以太网口SNR诊断正确_多模	${board}	${res}			
							
PB光口状态诊断	[Arguments]	${board}					
	: FOR	${i}	IN RANGE	12			
	\	${res}	PB光口状态诊断_多模	${board}	${i}		
	\	确认PB光口状态诊断正确_多模	${board}	${res}			
							
PB光口误码率诊断	[Arguments]	${board}					
	: FOR	${i}	IN RANGE	4			
	\	${res}	PB光口误码率诊断_多模	${board}	${i}		
	\	确认PB光口误码率诊断正确_多模	${board}	${res}			
							
PB光/电模块诊断	[Arguments]	${board}					
	: FOR	${i}	IN RANGE	13			
	\	${res}	PB光电模块诊断_多模	${board}	${i}		
	\	确认PB光电口诊断正确_多模	${board}	${res}			
