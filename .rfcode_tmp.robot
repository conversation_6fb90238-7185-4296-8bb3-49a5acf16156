*** Settings ***
Suite Setup	加载配置	1UE100M5ms	${dataset}
Suite Teardown	释放配置
Default Tags
Resource	../../../../../../userkeywords/resource.tsv
Resource	../../../../../../userkeywords/basic_multi/resource.tsv
Variables	Request1.py
Resource	../variable/resource.tsv
Resource	../../template.tsv
Resource	env.tsv
Resource	../../../../../../userkeywords/domain/upa/MTUE.tsv
Resource	../../../../../../userkeywords/domain/upa/resource.tsv


*** Test Cases ***
QCELL-MTU-100M-1001 ping包时延
	${cpeIp}	获取环境变量	${CPE}	ip
	${service_config_dict}	create dictionary	selservice	1	desipaddr	${cpeIp}	pingcount
	...	9999
	MUE Start Test并执行成功	${CPE}	1	1~20	${service_config_dict}
	sleep	60
	MUE Stop Test并执行成功	${CPE}	1
	sleep	20
	${ping_opt}	set variable	0
	${ping_reportpath_group}	获取mue测试报告	${CPE}	1
	${ping_report}	${ping_delay_avg_time}	${ping_delay_min_time}	${ping_delay_max_time}	获取Mtue ping报告时延内容	${ping_reportpath_group}
	FOR	${i}	IN RANGE	1	21
	${Ue1_pingDelayAvg}	Evaluate	${ping_report[${i}][${ping_delay_avg_time}]}
	${ping_opt}=	Run Keyword If	${Ue1_pingDelayAvg}<=20	Evaluate	${ping_opt}+1	ELSE	Evaluate
	...	${ping_opt}+0
	should be true	${ping_opt}==20
	log	业务正常

QCELL-MTU-100M-1002 上下行同传灌包流量
	${ulIperfTpt}	set variable	10
	${dlIperfTpt}	set variable	60
	${cpeIp}	获取环境变量	${CPE}	ip
	MTUE进行上下行同传灌包	${cpeIp}	${ulIperfTpt}	${dlIperfTpt}	${CPE}	1	1~20
	...	120
	#获取并校验灌包报告
	${iperf_reportpath}	获取mue测试报告	${CPE}	1
	校验MTUE上下行同传灌包业务正常	${iperf_reportpath}	${ulIperfTpt}	${dlIperfTpt}
	log	业务正常

QCELL-MTU-100M-1003 下行灌包流量
	${dlIperfTpt}	set variable	60
	${cpeIp}	获取环境变量	${CPE}	ip
	MTUE进行下灌包测试	${cpeIp}	${dlIperfTpt}	0	0	${CPE}	1
	...	1~20	80	60	1	180
	${iperf_reportpath}	获取mue测试报告	${CPE}	1
	校验MTUE灌包业务正常	${iperf_reportpath}	${dlIperfTpt}
	log	业务正常
	Comment	切换低时延场景	${GNODEB}
	Comment	sleep	360
	Comment	#接入15个Ue并上行ping包
	Comment	MTUE进行ping包	*************	32	1	${UE1}	1	1~15	120
	Comment	sleep	30
	Comment	#获取并校验ping包报告
	Comment	${ping_reportpath}	获取mue测试报告	${UE1}	1
	Comment	${ping_suc_num}	${max_lost}	${avg_lost}	${more_50_num}	获取Mtue ping报告内容	${ping_reportpath}
	Comment	should be true	${avg_lost}<1
	Comment	#上下同传灌包
	Comment	${ulIperfTpt}	set variable	10
	Comment	${dlIperfTpt}	set variable	20
	Comment	MTUE进行上下行同传灌包	*************	${ulIperfTpt}	${dlIperfTpt}	${UE1}	1	1~15	120
	Comment	sleep	30
	Comment	#获取并校验灌包报告
	Comment	${iperf_reportpath}	获取mue测试报告	${UE1}	1
	Comment	校验MTUE上下行同传灌包业务正常	${iperf_reportpath}	${ulIperfTpt}	${dlIperfTpt}
	Comment	log	业务正常
	Comment

QCELL-MTU-100M-1004 上行灌包流量
	${ulIperfTpt}	set variable	10
	${cpeIp}	获取环境变量	${CPE}	ip
	MTUE进行灌包	${cpeIp}	${ulIperfTpt}	1	1	${CPE}	1
	...	1~20	120
	${iperf_reportpath}	获取mue测试报告	${CPE}	1
	校验MTUE灌包业务正常	${iperf_reportpath}	${ulIperfTpt}
	log	业务正常
	log	高caps场景存在异常
	Comment	配置备份	${omc1}	${GNODEB}
	Comment	sleep	10
	Comment	切换高Caps场景	${GNODEB}
	Comment	sleep	360
	Comment	修改普通变量	${GNODEB}	EthernetInterface	appscene	1
	Comment	检查激活配置	${GNODEB}
	Comment	sleep	20
	Comment	#接入15个Ue并上行ping包
	Comment	MTUE进行ping包	*************	32	1	${UE1}	1	1~15	120
	Comment	sleep	30
	Comment	#获取并校验ping包报告
	Comment	${ping_reportpath}	获取mue测试报告	${UE1}	1
	Comment	${ping_suc_num}	${max_lost}	${avg_lost}	${more_50_num}	获取Mtue ping报告内容	${ping_reportpath}
	Comment	should be true	${avg_lost}<1
	Comment	#上下同传灌包
	Comment	${ulIperfTpt}	set variable	10
	Comment	${dlIperfTpt}	set variable	20
	Comment	MTUE进行上下行同传灌包	*************	${ulIperfTpt}	${dlIperfTpt}	${UE1}	1	1~15	120
	Comment	sleep	30
	Comment	#获取并校验灌包报告
	Comment	${iperf_reportpath}	获取mue测试报告	${UE1}	1
	Comment	校验MTUE上下行同传灌包业务正常	${iperf_reportpath}	${ulIperfTpt}	${dlIperfTpt}
	Comment	log	业务正常
	Comment	修改普通变量	${GNODEB}	EthernetInterface	appscene	0
	Comment	检查激活配置	${GNODEB}
	Comment	sleep	20

KPI测试
	[Teardown]	挂起并删除实时KPI监控任务	${GNODEB}	${taskIdList}
	${gnbId}	获取环境变量	${GNODEB}	meId
	${taskIdList}	CREATE LIST
	${Time}	Evaluate	datetime.datetime.now().strftime('%Y%m%d%H%M%S')	datetime
	${TaskPara}	Create Dictionary	taskName=${gnbId}_${Time}	moType=${motype4NRCellPhyDU}	counters=${countIds4PRB}	subType=gNBDU	granularity=${gr10}
	${taskId}	创建实时KPI监控任务	${GNODEB}	${TaskPara}
	Append To List	${taskIdList}	${taskId}
	${dlIperfTpt}	set variable	60
	${cpeIp}	获取环境变量	${CPE}	ip
	MTUE进行下灌包测试	${cpeIp}	${dlIperfTpt}	0	0	${CPE}	1
	...	1~20	80	60	1	180
	${percentList}	create list
	${realKpiData}	查询实时KPI监控数据	${GNODEB}	${taskId}
	${valueList}	create list
	${data}	evaluate	eval('${realKpiData}')["rows"]
	FOR	${item}	IN	@{data}
	${valuePer}	set variable	${item['values'][-1]}
	${VALUE616290059}	set variable	${item['values'][-2]}
	${VALUE616290060}	set variable	${item['values'][-3]}
	${VALUE616290059}	evaluate	"${VALUE616290059}".replace(",","")
	${VALUE616290060}	evaluate	"${VALUE616290060}".replace(",","")
	${value}	evaluate	'${valuePer}'[:-1]
	${value}	evaluate	round(${value},1)
	append to list	${valueList}	${value}
	log	${valueList}

删建小区
	删除并释放无线资源_多模	${GNODEB}
	${XML_PATH}	导出基站数据_多模	${GNODEB}
	实例化单板_多模	${GNODEB}	${XML_PATH}
	动态创建基带功能_多模	${VBP1}
	解析单板级联信息_多模	${XML_PATH}	${VBP1}
	${pb1}	根据级联数获取实例化单板别名_多模	${GNODEB}	1
	${PRRUS1}	根据pb单板别名获取其上的prrus_多模	${pb1[0]}
	set suite variable	${PRRUS1}
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP1}	${PRRUS1}	100
	...	100
	sleep	300
	run keyword and continue on failure	确认NR小区状态正常_多模	${cell1}
	修改MTUE频点和带宽	0	TDD	3450000	3450000	78	100
	修改MTUE频点和带宽	1	TDD	3450000	3450000	78	100
	MUE Reset并执行成功	${CPE}
	${dlIperfTpt}	set variable	60
	${cpeIp}	获取环境变量	${CPE}	ip
	MTUE进行下灌包测试	${cpeIp}	${dlIperfTpt}	0	0	${CPE}	1
	...	1~20	80	60	1	180
	${iperf_reportpath}	获取mue测试报告	${CPE}	1
	校验MTUE灌包业务正常	${iperf_reportpath}	${dlIperfTpt}

历史KPI__6400255
	初始化kpi测试数据	${GNODEB}	${CONTROLPC}	${VSW}	${VBP1}	${PRRU}
	${duration}	set variable	3600
	${granularityNum}	Evaluate	int(${duration})/900
	${beginTime}	按粒度计算时间	${granularity15}	${cmpTimeFormat}	0
	${endTime}	按粒度计算时间	${granularity15}	${cmpTimeFormat}	${granularityNum}
	${beginTime}	set variable	2021-03-25 10:00:00
	${endTime}	set variable	2021-03-25 12:00:00
	KPI数据入库零点系统	${GNODEB}	${beginTime}	${endTime}

QCELL-MTUE-100M-2P5ms-0001 单小区20用户上下满流量灌包KPI测试__6400255
	初始化kpi测试数据	${GNODEB}	${CONTROLPC}	${VSW}	${VBP1}	${PRRU}
	删除并释放无线资源_多模	${GNODEB}
	${XML_PATH}	导出基站数据_多模	${GNODEB}
	实例化单板_多模	${GNODEB}	${XML_PATH}
	动态创建基带功能_多模	${VBP1}
	解析单板级联信息_多模	${XML_PATH}	${VBP1}
	${pb1}	根据级联数获取实例化单板别名_多模	${GNODEB}	1
	${PRRUS1}	根据pb单板别名获取其上的prrus_多模	${pb1[0]}
	set suite variable	${PRRUS1}
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP1}	${PRRUS1}	100
	...	100
	sleep	300
	run keyword and continue on failure	确认NR小区状态正常_多模	${cell1}
	修改MTUE频点和带宽	0	TDD	3450000	3450000	78	100
	修改MTUE频点和带宽	1	TDD	3450000	3450000	78	100
	MUE Reset并执行成功	${CPE}
	${dlIperfTpt}	set variable	60
	${cpeIp}	获取环境变量	${CPE}	ip
	MTUE进行下灌包测试	${cpeIp}	${dlIperfTpt}	0	0	${CPE}	1
	...	1~20	300	300	1	300
	${iperf_reportpath}	获取mue测试报告	${CPE}	1
	${duration}	set variable	300
	${granularityNum}	Evaluate	int(${duration})/900
	${beginTime}	按粒度计算时间	${granularity15}	${cmpTimeFormat}	0
	${endTime}	按粒度计算时间	${granularity15}	${cmpTimeFormat}	${granularityNum}
	${beginTime}	set variable	2021-03-25 11:00:00
	${endTime}	set variable	2021-03-25 16:00:00
	KPI数据入库零点系统	${GNODEB}	${beginTime}	${endTime}


*** Keywords ***
加载配置
	[Arguments]	${scene}	${dataset}
	获取资源	${scene}	${dataset}
	创建基站_多模	${GNODEB}	${UME}
	创建UE对象	${CPE}
	创建VSW_多模	${GNODEB}	${VSW}
	创建VBP_多模	${GNODEB}	${VBP1}
	创建PDN	${PDN}
	${XML_PATH}	导出基站XML并备份	${GNODEB}	${UME}
	set global variable	${XML_PATH}
	LMT停止运行	${CPE}
	sleep	3
	LMT启动运行	${CPE}
	SLEEP	20
	${result}	Run Keyword And Return Status	LMT删除CPE实例	${CPE}
	${result}	Run Keyword And Return Status	LMT创建CPE实例	${CPE}
	sleep	3
	LMT创建group实例	${CPE}	BasicGroup	0~0
	LMT创建group实例	${CPE}	Group1	1~20
	LMT下发CPE配置	${CPE}
	sleep	5
	LMT下发CPE配置	${CPE}	NR	3	0
	sleep	3
	LMT下发CPE配置	${CPE}	NR	3	1
	sleep	3
	${variables}	create dictionary	serverroute1	***********
	修改UE配置	${CPE}	NR	${variables}	0
	sleep	3
	修改UE配置	${CPE}	NR	${variables}	1
	sleep	3

释放配置
	导入基站数据_多模	${GNODEB}	${XML_PATH}
	删除UE对象	${CPE}
	删除PDN	${PDN}
	释放实例化单板_多模	${GNODEB}
	释放实例化无线配置_多模	${GNODEB}
	删除基站_多模	${GNODEB}

恢复环境
	设置UE信号衰减	${CPE}	0

MTUE进行下灌包测试
	[Arguments]	${desipaddr}	${bandwidth}	${isuliperf}	${isudp}	${ueAlias}	${ueGroup}	${ue_id}	${iperfTime}	${time}=9999	${repeat}=0	${duration}=6000
	[Documentation]	[功能说明]
	...	MTUE进行UDP灌包
	...	
	...	[输入]：
	...	${desipaddr}：pdn地址
	...	${bandwidth}：灌包流量大小
	...	${isuliperf}：灌包方向，0：下行；1：上行
	...	${isudp}：是否udp灌包，0：TCP，1：udp；
	...	${ueAlias}：UE别名,allue
	...	${ueGroup}：UE groupid
	...	${ue_id}：${ue_id}：需要才做UE Group下面的ue实例，allue代表所有ue实例，要操作某几个需要写表达式,如 1~2，4~5,代表操作ueid为12345的UE实例
	...	${iperfTime}：iperf业务时间
	...	
	...	[返回]：
	...	无
	...	
	...	10288354
	[Teardown]
	${service_config_dict}	create dictionary	selservice	2	desipaddr	${desipaddr}	desport
	...	9956	time	${time}	bandwidth	${bandwidth}	repinterval	1
	...	isuliperf	${isuliperf}	isudp	${isudp}	iperfserport	5001	duration
	...	${duration}	repeat	${repeat}
	MUE Start Test并执行成功	${ueAlias}	${ueGroup}	${ue_id}	${service_config_dict}
	sleep	${iperfTime}
	MUE Stop Test并执行成功	${ueAlias}	${ueGroup}

MTUE进行上下行同传灌包测试
	[Arguments]	${desipaddr}	${ulbandwidth}	${dlbandwidth}	${ueAlias}	${ueGroup}	${ue_id}	${iperfTime}
	[Documentation]	[功能说明]
	...	MTUE进行UDP上下同传灌包
	...	
	...	[输入]：
	...	${desipaddr}：pdn地址
	...	${ulbandwidth}：上行灌包流量大小
	...	${dlbandwidth}：下行灌包流量大小
	...	${ueAlias}：UE别名,allue
	...	${ueGroup}：UE groupid
	...	${ue_id}：${ue_id}：需要才做UE Group下面的ue实例，allue代表所有ue实例，要操作某几个需要写表达式,如 1~2，4~5,代表操作ueid为12345的UE实例
	...	${iperfTime}：iperf业务时间
	...	
	...	[返回]：
	...	无
	...	
	...	10288354
	[Teardown]
	${service_config_dict}	create dictionary	selservice	2	desipaddr	${desipaddr}	desport
	...	9956	time	99999	ulbandwidth	${ulbandwidth}	dlbandwidth	${dlbandwidth}
	...	repinterval	1	isuliperf	2	isudp	1	iperfserport
	...	5001
	MUE Start Test并执行成功	${ueAlias}	${ueGroup}	${ue_id}	${service_config_dict}
	sleep	${iperfTime}
	MUE Stop Test并执行成功	${ueAlias}	${ueGroup}
	sleep	50

挂起并删除实时KPI监控任务
	[Arguments]	${gnb}	${taskIdList}
	挂起实时KPI监控任务	${gnb}	${taskIdList}
	${copyTaskIdList}	Evaluate	copy.deepcopy(${taskIdList})	copy
	FOR	${taskId}	IN	@{copyTaskIdList}
	${delTaskIdList}	Create List	${taskId}
	删除实时KPI监控任务	${gnb}	${delTaskIdList}
	Remove Values From List	${taskIdList}	${taskId}

修改MTUE频点和带宽
	[Arguments]	${groupID}	${radioType}	${freDL}	${freqUL}	${band}	${bandWidth}
	${variables}	create dictionary	radiotype	${radioType}	ulfreq	${freqUL}	dlfreq
	...	${freDL}	band	${band}	bandwidth	${bandWidth}
	修改UE配置	${CPE}	NR	${variables}	${groupID}

初始化kpi测试数据
	[Arguments]	${gnbAlias}	${controlpc}	${vswAlias}	${vbpAlias}	${aauAlias}
	[Documentation]	[功能说明]
	...	初始化上传KPI数据至零点系统时需要的一些测试信息，
	...	参数说明：
	...	gnbAlias：基站别名
	...	controlpc：控制业务的PC，用户可以定义区域、测试团队和测试人员等信息，如groupArea、equipmentId、userId等。分别对应区域团队、测试团队、测试人员id
	...	vswAlias：主控板别名，用于获取一些主控板的类型
	...	vbpAlias：基带板别名，用于获取一些基带板的类型
	...	aauAlias：AAU别名，用于获取一些AAU的型号
	...	[作者]
	...	10269639
	${baseInfo}	Create Dictionary	groupArea=None	usecaseId=None	equipmentId=None	userId=None	versionId=None
	...	startTime=None	endTime=None	autoFlg=None	eccostTime=None	doTimes=None	doResult=None	userdefined=None
	${kpiTestInfo}	获取环境变量	${controlpc}	kpiTestinfo
	${groupArea}	evaluate	${kpiTestInfo}.get("groupArea")
	${usecaseId}	evaluate	"${TEST NAME}".split("__")[1]
	${equipmentId}	evaluate	${kpiTestInfo}.get("equipmentId")
	${userId}	evaluate	${kpiTestInfo}.get("userId")
	${versionId}	查询基站运行版本号_多模	${gnbAlias}
	set to dictionary	${baseInfo}	groupArea=${groupArea}	usecaseId=${usecaseId}	equipmentId=${equipmentId}	userId=${userId}	versionId=${versionId}
	...	autoFlg=1	doTimes=1	userdefined=长保kpi测试
	set test variable	${baseInfo}	${baseInfo}
	${nrCc}	获取主控板类型	${gnbAlias}	${vswAlias}
	${nrBb}	获取基带板类型	${gnbAlias}	${vbpAlias}
	${nrBbNum}	获取基带板数量	${gnbAlias}
	${nrAau}	获取指定aau型号	${gnbAlias}	${aauAlias}
	${nrCellNum}	获取小区个数	${gnbAlias}
	${nrBandWidth}	evaluate	${kpiTestInfo}.get("nrBandWidth")
	${nrSubframe}	evaluate	${kpiTestInfo}.get("nrSubframe")
	${nrFrequency}	evaluate	${kpiTestInfo}.get("nrFrequency")
	${nrCuDuFlg}	evaluate	${kpiTestInfo}.get("nrCuDuFlg")
	${nrInfo}	Create Dictionary	nrCase=9200	nrCc=${nrCc}	nrBb=${nrBb}	nrBbNum=${nrBbNum}	nrAau=${nrAau}
	...	nrCellNum=${nrCellNum}	nrBandWidth=${nrBandWidth}	nrSubframe=${nrSubframe}	nrFrequency=${nrFrequency}	nrCuDuFlg=${nrCuDuFlg}
	set test variable	${nrInfo}	${nrInfo}


