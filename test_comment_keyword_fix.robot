*** Settings ***
Documentation    测试Comment关键字开头的注释行修复
Library          SeleniumLibrary

# 这是标准的#号注释，应该显示为浅灰色
Comment    这是Comment关键字注释，应该显示为浅灰色
    Comment    缩进的Comment注释，应该显示为浅灰色
        Comment    更深缩进的Comment注释，应该显示为浅灰色

*** Variables ***
${URL}           https://www.example.com
# 变量部分的#号注释
Comment    变量部分的Comment注释
    Comment    缩进的变量部分Comment注释

*** Test Cases ***
测试Comment注释修复
    [Documentation]    测试Comment关键字开头的注释行是否正确显示为浅灰色
    [Tags]    注释    Comment    修复
    
    # 标准#号注释
    Log    正常的Log关键字
    Comment    这是Comment注释，整行应该是浅灰色
    Set Variable    test_value
    Comment    另一个Comment注释
    
    # 缩进的Comment注释
        Comment    缩进的Comment注释
            Comment    更深缩进的Comment注释
    
    Should Be Equal    value1    value1

Comment注释的各种形式
    [Documentation]    测试Comment注释的各种形式
    [Tags]    Comment    形式
    
    # 1. 行首Comment（无缩进）
Comment    行首Comment注释
    
    # 2. 缩进Comment
    Comment    缩进Comment注释
        Comment    更深缩进Comment注释
    
    # 3. Comment后跟制表符
Comment	制表符分割的Comment注释
    Comment	缩进+制表符的Comment注释
    
    # 4. Comment后跟空格
Comment 空格分割的Comment注释
    Comment 缩进+空格的Comment注释
    
    # 5. 单独的Comment（无参数）
Comment
    Comment
        Comment

中文Comment注释测试
    [Documentation]    测试中文Comment注释
    [Tags]    中文    Comment
    
    Log    正常关键字
    Comment    中文Comment注释：这是中文内容
    Set Variable    中文变量值
    Comment    混合Comment注释：This is mixed content
    Should Be True    True
    Comment    包含特殊字符的Comment：！@#￥%……&*（）

混合注释类型测试
    [Documentation]    测试#号注释和Comment注释的混合使用
    [Tags]    混合注释
    
    # 这是#号注释
    Log    正常关键字1
    Comment    这是Comment注释
    # 另一个#号注释
    Set Variable    test_value
    Comment    另一个Comment注释
    # 最后一个#号注释
    Should Contain    test    test

Comment与关键字的区分
    [Documentation]    确保Comment注释不会被误识别为关键字
    [Tags]    Comment    关键字区分
    
    # 正常的关键字调用（不是注释）
    Log    这不是注释，是正常的Log关键字
    Set Variable    这不是注释，是正常的Set Variable关键字
    
    # Comment注释（应该整行显示为浅灰色）
    Comment    这是Comment注释，不是关键字调用
    Comment    包含关键字名称的注释：Log, Set Variable, Should Be Equal
    Comment    包含变量语法的注释：${variable}, @{list}, &{dict}

复杂Comment场景
    [Documentation]    测试复杂的Comment注释场景
    [Tags]    复杂Comment
    
    # 在循环中的Comment注释
    FOR    ${i}    IN RANGE    1    4
        Log    循环次数: ${i}
        Comment    循环中的Comment注释: ${i}
        # 循环中的#号注释
    END
    
    # 在条件中的Comment注释
    IF    True
        Log    条件为真
        Comment    条件为真时的Comment注释
    ELSE
        Log    条件为假
        Comment    条件为假时的Comment注释
    END

*** Keywords ***
带Comment注释的用户关键字
    [Documentation]    包含Comment注释的用户关键字
    [Arguments]    ${param}
    
    # 用户关键字中的#号注释
    Log    参数值: ${param}
    Comment    用户关键字中的Comment注释
    ${result}=    Set Variable    processed_${param}
    Comment    处理结果的Comment注释: ${result}
    
    RETURN    ${result}

另一个带Comment的关键字
    [Documentation]    另一个包含Comment注释的关键字
    
    Comment    关键字开始的Comment注释
    Log    开始执行用户关键字
    
    # 混合注释类型
    Comment    这是Comment注释
    # 这是#号注释
    Comment    又一个Comment注释
    
    Log    关键字执行完成
    Comment    关键字结束的Comment注释

# 文件末尾的各种注释
Comment    文件末尾的Comment注释
# 文件末尾的#号注释
Comment    最后一个Comment注释
