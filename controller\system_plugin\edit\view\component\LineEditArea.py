# coding=utf-8
'''
Created on 2019年11月4日

@author: 10247557
'''
import logging
import traceback

from PyQt5.Qt import Qt, QApplication, QCursor
from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON><PERSON>ayout, QLabel, QPushButton, QTextEdit

from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from controller.system_plugin.edit.view.component.Formator import parse_value
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer, \
    ColorizationSettings
from model.CurrentItem import CurrentItem
from view.common.dialog.SettingsDialog import SettingsDialog
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper


class LineEditArea(object):

    def __init__(self, parent, lable_name):
        self._parent = parent
        self._lable_name = lable_name
        self._text = None

    def get_layout(self):
        return self._layout

    def load(self):
        self._label = QLabel(self._lable_name)
        self._label.setFixedWidth(170)
        self._line = LineEdit()
        self._line.setFixedHeight(25)
        self._line.setReadOnly(True)
        self._set_color()
        self._line.clicked.connect(self._show_dialog)
        self._line.jump.connect(self._jump)
        self._line.textChanged.connect(self._modify_data)
        self._clear_btn = QPushButton('Clear', self._parent)
        self._clear_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._clear_btn.setFixedSize(120, 22)
        self._clear_btn.clicked.connect(self._clear_edit_area)
        self._set_layout()

    def _jump(self):
        self._specified_keyword_jumper = SpecifiedKeywordJumper()
        line = self._line.toPlainText()
        keyword_name = line.split('|')[0].strip()
        keyword_path = self._specified_keyword_jumper.get_keyword_path_from_local_repository(keyword_name)
        self._specified_keyword_jumper.get_keyword_item(keyword_path, keyword_name)

    def _set_color(self):
        if self._line.toPlainText():
            self._line.setStyleSheet("background-color: #ffffff;")
        else:
            self._line.setStyleSheet("background-color: #c0c0c0;")

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._label, 1)
        self._layout.addWidget(self._line, 6)
        self._layout.addWidget(self._clear_btn, 1)

    def set_visible(self, bool_value):
        self._label.setVisible(bool_value)
        self._line.setVisible(bool_value)
        self._clear_btn.setVisible(bool_value)

    def _clear_edit_area(self):
        self._line.clear()
        self._set_color()

    def fill_data(self, text):
        try:
            self._text = text
            if text is None:
                text = ''
            if isinstance(text, list):
                keyword = text[0].split(' | ')[0]
                if text[0] and text[1]:
                    text = ' | ' .join(text)
                else:
                    text = '' .join(text)
            else:
                keyword = parse_value(text)[0].split(' | ')[0]
            if Colorizer()._is_keyword(keyword):
                color = ColorizationSettings().get_keyword_color()
                color = (int(color[0]), int(color[1]), int(color[2]))
                self._line.setText('<a href = %s"><font style="color:rgb%s;">%s</font></a>%s' % (keyword, color, keyword, text.replace(keyword, '')))
            else:
                self._line.setFontUnderline(False)
                self._line.setTextColor(Qt.black)
                self._line.setPlainText(text)
            self._set_color()
        except Exception:
            logging.error('============teardown or timeout or arguments or return value except=========')
            traceback.print_exc()

    def get_data(self):
        text = self._line.toPlainText().rstrip('\n')
        return parse_value(text) if text else None

    def _modify_data(self):
        result = self.get_data()
        if self._text != result:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify(self._lable_name.replace(' ', '_').lower(), result)

    def _show_dialog(self):
        self.dialog = SettingsDialog(self, self._lable_name)
        self.dialog.show()
        self.dialog.set_text(parse_value(self._line.toPlainText()))


class LineEdit(QTextEdit):
    clicked = pyqtSignal()
    jump = pyqtSignal()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if QApplication.keyboardModifiers() == Qt.ControlModifier:
                self.jump.emit()
            else:
                self.clicked.emit()


if __name__ == "__main__":
    str = 'sdfa| fgsg '
    result = str.rsplit('| #', 1)
    print(result)
    print(result[1].replace('\\|', '|'))
