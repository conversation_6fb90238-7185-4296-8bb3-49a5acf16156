# encoding=utf-8
'''
Created on 2020年1月6日

@author: 10247557/10240349
'''
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QTreeWidget, \
    QTreeWidgetItem, QStackedWidget, QStackedLayout

from controller.system_plugin.tools.item.Preferences.Drawer import Drawer
from settings.SystemSettings import SystemSettings


class Preferences(QWidget):

    def __init__(self, title):
        super().__init__()
        self.setWindowTitle(title)
        self._init_ui()

    def _init_ui(self):
        self.resize(SystemSettings().get_value('PREFERENCES_DIALOG_WIDTH'),
                    SystemSettings().get_value('PREFERENCES_DIALOG_HEIGHT'))
        self._set_layout()

    def _load(self):
        pass

    def _set_layout(self):
        vbox = QHBoxLayout()
        tree_layout = self._get_tree_layout()
        widget = self._generate_widget()
        vbox.addLayout(tree_layout, 1)
        vbox.addWidget(widget, 2)
        self.setLayout(vbox)

    def _get_tree_layout(self):
        hbox = QHBoxLayout()
        self.tree = QTreeWidget()
        self._set_style()
        for item in (self._add_language_item(), self._add_trace_log_item()):
            self.tree.addTopLevelItem(item)
        hbox.addWidget(self.tree)
        return hbox

    def _set_style(self):
        self.tree.header().hide()
        self.tree.clicked.connect(self._show_page)

    def _add_language_item(self):
        item = QTreeWidgetItem(self.tree)
        item.setText(0, 'Language')
        return item

    def _generate_language_widget(self):
        widget = QWidget()
        layout = Drawer().get_layout('language')
        widget.setLayout(layout)
        return widget

    def _add_trace_log_item(self):
        item = QTreeWidgetItem(self.tree)
        item.setText(0, 'Trace_Log')
        return item

    def _generate_trace_log_widget(self):
        widget = QWidget()
        layout = Drawer().get_layout('Trace_Log')
        widget.setLayout(layout)
        return widget

    def _show_page(self, index):
        item = self.tree.currentItem()
        if item.text(0) == 'Language':
            self._stacked_widget.setCurrentIndex(0)
        elif item.text(0) == 'Trace_Log':
            self._stacked_widget.setCurrentIndex(1)

    def _generate_widget(self):
        widget = QWidget()
        self._stacked_widget = QStackedWidget(widget)
        self._stacked_widget.addWidget(self._generate_language_widget())
        self._stacked_widget.addWidget(self._generate_trace_log_widget())
        return widget
