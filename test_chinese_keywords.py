# -*- coding: utf-8 -*-
"""
测试中文关键字下划线功能的示例文件

使用方法：
1. 在TextEditor中打开此文件
2. 按住Ctrl键，鼠标悬停在中文函数名上应该显示：
   - 蓝色字体颜色
   - 蓝色下划线
   - 手型鼠标指针
3. 按住Ctrl键并点击中文函数名，会跳转到函数定义
4. 验证下划线是否完全覆盖中文字符
5. 验证字体颜色是否变为蓝色

测试场景：
- 中文函数名
- 中英文混合函数名
- 包含下划线的中文函数名
"""

def 测试函数():
    """纯中文函数名"""
    print("这是一个中文函数")
    中英文混合函数()
    return "中文结果"

def 中英文混合函数():
    """中英文混合函数名"""
    print("中英文混合函数")
    带下划线的中文函数_test()
    return "混合结果"

def 带下划线的中文函数_test():
    """带下划线的中文函数名"""
    print("带下划线的中文函数")
    return "下划线结果"

def test_english_function():
    """英文函数名（对比测试）"""
    print("English function")
    测试函数()  # 调用中文函数
    return "English result"

class 测试类:
    """中文类名"""

    def __init__(self):
        """构造函数"""
        self.值 = 0

    def 中文方法(self):
        """中文方法名"""
        self.另一个中文方法()
        return self.值

    def 另一个中文方法(self):
        """另一个中文方法"""
        self.值 = 42
        测试函数()  # 调用全局中文函数
        return self.值

def 主函数():
    """中文主函数"""
    print("开始测试中文关键字")

    # 调用各种中文函数来测试跳转功能
    结果1 = 测试函数()
    print(f"结果1: {结果1}")

    测试对象 = 测试类()
    结果2 = 测试对象.中文方法()
    print(f"结果2: {结果2}")

    结果3 = test_english_function()
    print(f"结果3: {结果3}")

    print("测试完成")

if __name__ == "__main__":
    主函数()
