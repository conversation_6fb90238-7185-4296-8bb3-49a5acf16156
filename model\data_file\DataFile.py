# encoding=utf-8
'''
Create on  2019年10月14日

@author: 10244931, 10243352, 10154402
'''
from _io import StringIO
import copy
from functools import wraps
import logging
import os
import re
import shutil
import threading
import time
import traceback
from robot.libraries import STDLIBS
from settings.SystemSettings import SystemSettings
ROBOT_VERSION = SystemSettings().read('ROBOT_VERSION')
import importlib
robot_module = importlib.import_module(f'{ROBOT_VERSION}.parsing.model')
RawResourceFile = getattr(robot_module, 'ResourceFile')
RawTestCaseFile = getattr(robot_module, 'TestCaseFile')
RawTestDataDirectory = getattr(robot_module, 'TestDataDirectory')
robot_module = importlib.import_module(f'{ROBOT_VERSION}.parsing.populators')
FromFilePopulator = getattr(robot_module, 'FromFilePopulator')
robot_module = importlib.import_module(f'{ROBOT_VERSION}.parsing.robotreader')
RobotReader = getattr(robot_module, 'RobotReader')

from controller.parser.reader.ReaderFactory import ReaderFactory, \
    SUFFIX_READER_MAP
from model.data_file.KeyWords import KeyWordRepository, KeyWords, KeyWord
from model.data_file.PyLib import PyLib
from model.data_file.Repository import DataFileRepository, \
    LocalKeyWordRepository, LocalVariableRepository, ProjectTreeRepository, \
    BuildInKeyWordRepository
from model.data_file.RfStdLibs import RfStdLibs
from model.data_file.Settings import Settings
from model.data_file.TestCases import TestCases, TestCase
from model.data_file.TextBlock import Resource, Library
from model.data_file.Variables import VariableRepository, Dict, Scalar, List, Variables
from utility.ObjectRepository import ObjectRepository
from utility.PluginRepository import PluginRepository
from utility.Singleton import Singleton
from utility.FileHandler import FileHandler
from settings.SystemSettings import SystemSettings


def set_lastest_modify_time(func):
    @wraps(func)
    def decorated(*args, **kwargs):
        data_file = args[0]
        try:
            return func(*args, **kwargs)
        except Exception as _:
            pass
        finally:
            data_file._latstest_modify_time = time.mktime(time.localtime(os.stat(data_file.path).st_mtime))
    return decorated


def _report_invalid_syntax(cls, message, level='ERROR'):
    initfile = getattr(cls, 'initfile', None)
    path = os.path.join(cls.source, initfile) if initfile else cls.source
    #logging.error("%s: Error in file '%s': %s" % (level, path, message))


class FromStringIOPopulator(FromFilePopulator):

    def populate(self, content):
        RobotReader().read(content, self)


class TestDataDirectory(RawTestDataDirectory):

    def report_invalid_syntax(self, message, level='ERROR'):
        return _report_invalid_syntax(self, message, level)


class TestCaseFile(RawTestCaseFile):

    def report_invalid_syntax(self, message, level='ERROR'):
        return _report_invalid_syntax(self, message, level)


class ResourceFile(RawResourceFile):

    def report_invalid_syntax(self, message, level='ERROR'):
        return _report_invalid_syntax(self, message, level)


class SaveSequence(object):
    CACHE = []

    @staticmethod
    def add(data_file):
        SaveSequence.CACHE.append(data_file)

    @staticmethod
    def save_all():
        for data_file in SaveSequence.CACHE:
            data_file.flush()
        SaveSequence.CACHE.clear()

    @staticmethod
    def is_add(data_file):
        return data_file in SaveSequence.CACHE


class DataFile(object):

    TMP_REPO_PARSE_FILES = []
    TABLES = ["settings", "variables", "testcases", "keywords"]
    TABLE_2_TAG = {
        "settings": "*** Settings ***",
        "variables": "*** Variables ***",
        "testcases": "*** Test Cases ***",
        "keywords": "*** Keywords ***"
    }

    __slots__ = [
        "_path",
        '_lock',
        "_tmp_path",
        "_suffix",
        "_reformat_flag",
        "_rename_flag",
        "_reformat_suffix",
        "_cache",
        "settings",
        "keywords",
        "variables",
        "testcases",
        "is_dirty",
        "_latstest_modify_time",
    ]

    def __init__(self, path):
        self._path = os.path.abspath(path)
        self._suffix = os.path.splitext(self._path)[-1][1:]
        self._tmp_path = os.path.split(self._path)[0]
        self._reformat_flag = False
        self._rename_flag = False
        self._reformat_suffix = self._suffix
        self._cache = ""
        self._lock = threading.Lock()
        self.is_dirty = False
        self._latstest_modify_time = 0
        python_ver = SystemSettings().read('PYTHON_VER')
        if python_ver == '3':
            self.file_type = '.robot'
        else:
            self.file_type = '.tsv'

    @property
    def path(self):
        return self._path

    def is_delete(self):
        return not os.path.exists(self.path)

    def is_modified(self):
        return time.mktime(time.localtime(os.stat(self.path).st_mtime)) > self._latstest_modify_time

    def set_dirty(self):
        self.is_dirty = True

    @set_lastest_modify_time
    def rename(self, name):
        path = os.path.join(os.path.dirname(self.path), "{name}.{suffix}".format(name=name, suffix=self._suffix))
        shutil.move(self._path, path)
        self.change_path(path)

    def change_path(self, path):
        DataFileRepository().delete(self.path)
        self._path = os.path.abspath(path)
        DataFileRepository().add(self._path, self)
        if hasattr(self, "keywords"):
            for keyword in self.keywords.content:
                keyword.path = self.path
        if hasattr(self, "variables"):
            for variable in self.variables.content:
                variable.path = self.path

    @set_lastest_modify_time
    def parse(self, path=None, force_parse=True):
        path = self._path if not path else path
        data_file = DataFileRepository().find(path)
        if not data_file or force_parse:
            reader = ReaderFactory().get_instance(path)
            self._clear_attr()
            with self._lock:
                tables = reader.parse()
                self._inject(tables)
                if self._has_settings():
                    self._parse_resources()
                if not path:
                    DataFileRepository().add(self._path, self)
                self.store_keywords()
                self.store_variables()
        else:
            self._copy_attrs(data_file)

    def open(self, is_clear_local_repo=True):
        if is_clear_local_repo:
            self._clear_local_repo()
        self._load_init_file()
        self.update_local_repository()
        self.store_resource()
        self._parse_libraries()

    def store_keywords(self):
        if hasattr(self, "keywords"):
            for keyword in self.keywords.content:
                keyword.path = self._path
                KeyWordRepository().add(keyword.name, keyword)

    def store_variables(self):
        if hasattr(self, "variables"):
            for variable in self.variables.content:
                variable.path = self._path
                VariableRepository().add(variable.name, variable)

    def get_content(self):
        content = ""
        for table in DataFile.TABLES:
            if hasattr(self, table):
                content += DataFile.TABLE_2_TAG.get(table) + "\n"
                table_obj = getattr(self, table)
                for element in table_obj.content:
                    content += element.anti_populate(table_obj.__class__.__name__)
            content += "\n"
        return content

    @set_lastest_modify_time
    def save_content(self, content):
        tmp_path = f".rfcode_tmp{self.file_type}"
        with open(tmp_path, "w", encoding='utf-8') as f:
            f.write(content)
            f.flush()
        self._format_for_head(tmp_path)
        self._suffix = os.path.splitext(self._path)[-1][1:]
        if not self._is_running_testcase():
            self.check_file(tmp_path)
            self.parse(force_parse=True)
        else:
            format_content = self._get_format_content(self._get_target(tmp_path), tmp_path, True)
            with open(tmp_path, "w", encoding='utf-8') as f:
                f.write(format_content)
                f.flush()
            self.parse(path=tmp_path)
            if not SaveSequence.is_add(self):
                SaveSequence.add(self)
            self._cache = format_content
            os.remove(tmp_path)
        self.is_dirty = False

    def update(self, content):
        tmp_path = os.path.join(self._tmp_path, f".rfcode_tmp{self.file_type}")
        with open(tmp_path, "w", encoding='utf-8') as f:
            f.write(content)
            f.flush()
        self._format_for_head(tmp_path)
        self._suffix = "tsv" if self.file_type == '.tsv' else "robot"
        try:
            format_content = self._get_format_content(self._get_target(tmp_path), tmp_path, True)
            with open(tmp_path, "w", encoding='utf-8') as f:
                f.write(format_content)
                f.flush()
        except Exception as e:
            traceback.print_exc()
        self._clear_cur_keywords()
        self._clear_cur_variables()
        self.parse(os.path.abspath(tmp_path))
        self.is_dirty = True
        os.remove(tmp_path)

    def modify_settings(self, key, content):
        if not hasattr(self, "settings"):
            self.settings = Settings()
        if self.settings.modify(key, content):
            index, table = int(content.get("index")), content.get("table")
            setting_name, relative_path = table[index][0], table[index][1]
            abs_path = os.path.abspath(os.path.join(self.path, relative_path))
            if not DataFileRepository().find(abs_path) and setting_name.lower() == "resource" and os.path.isfile(abs_path):
                DataFileRepository().add(abs_path, ResourceDataFile(abs_path))
                DataFileRepository().find(abs_path).parse()
        self.settings.query()
        self.open()

    @set_lastest_modify_time
    def save(self):
        tmp_path = f".rfcode_tmp{self.file_type}"
        with open(tmp_path, "w", encoding='utf-8') as f:
            content = self.get_content()
            f.write(content)
            f.flush()
        self._suffix = os.path.splitext(self._path)[-1][1:]
        # if not self._is_running_testcase():
        self.check_file(tmp_path)
        try:
            self._refresh_sub_items()
        except Exception as _:
            traceback.print_exc()
        # else:
        #     format_content = self._get_format_content(self._get_target(tmp_path), tmp_path, False)
        #     self._cache = format_content
        #     if not SaveSequence.is_add(self):
        #         SaveSequence.add(self)
        self.is_dirty = False

    @set_lastest_modify_time
    def flush(self):
        with open(self._path, "w", encoding="UTF-8") as f:
            f.write(self._cache)
            f.flush()
        self._clear_cache()
        if self._reformat_flag:
            self.reformat(self._reformat_suffix)

    def modify_variable(self, content, index):
        name = content[0].strip()[2:-1]
        op_variable = self.variables.content[index]
        for variable in self.variables.content:
            if variable == op_variable:
                continue
            if name == variable.name:
                return "repeat"
        op_variable.modify("content", content)

    def delete(self):
        if os.path.exists(self._path):
            os.remove(self._path)
        if hasattr(self, "keywords"):
            for keyword in self.keywords.content:
                LocalKeyWordRepository().delete(keyword)
                KeyWordRepository().delete(keyword)
        if hasattr(self, "variables"):
            for variable in self.variables.content:
                LocalVariableRepository().delete(variable)
                VariableRepository().delete(variable)
        DataFileRepository().delete(self._path)

    def move_up(self, content):
        if isinstance(content, KeyWord):
            table = getattr(self, "keywords")
        if isinstance(content, TestCase):
            table = getattr(self, "testcases")
        index = table.content.index(content)
        if index == 0:
            return
        item = table.content[index]
        table.content.remove(item)
        table.content.insert(index - 1, item)

    def move_down(self, content):
        if isinstance(content, KeyWord):
            table = getattr(self, "keywords")
        if isinstance(content, TestCase):
            table = getattr(self, "testcases")
        index = table.content.index(content)
        if index == len(table.content):
            return
        item = table.content[index]
        table.content.remove(item)
        table.content.insert(index + 1, item)

    def add_testcase(self, name):
        options = {
            "table": "TestCases",
            "name": name,
        }
        return self._add_item(options)

    def delete_testcase(self, index):
        options = {
            "table": "TestCases",
            "index": index,
        }
        self._delete_item(options)

    def add_keyword(self, name, arguments):
        options = {
            "table": "KeyWords",
            "name": name,
            "arguments": arguments,
            "path": self._path
        }
        return self._add_item(options)

    def delete_keyword(self, index):
        options = {
            "table": "KeyWords",
            "index": index,
        }
        self._delete_item(options)

    def delete_variable(self, index):
        options = {
            "table": "Variables",
            "index": index,
        }
        self._delete_item(options)

    def add_scalar(self, name, value):
        options = {
            "table": "Variables",
            "name": name,
            "type": Scalar(name).__class__.__name__,
            "value": value
        }
        return self._add_item(options)

    def add_list(self, name, value):
        options = {
            "table": "Variables",
            "name": name,
            "type": List(name).__class__.__name__,
            "value": value,
            "path": self._path
        }
        return self._add_item(options)

    def add_dict(self, name, value):
        options = {
            "table": "Variables",
            "name": name,
            "type": Dict(name).__class__.__name__,
            "value": value,
            "path": self._path
        }
        return self._add_item(options)

    def copy_testcase(self, testcase):
        if not hasattr(self, "testcases"):
            self.testcases = TestCases()
        self.testcases.content.append(copy.deepcopy(testcase))

    def copy_keyword(self, keyword):
        if not hasattr(self, "keywords"):
            self.keywords = KeyWords()
        keyword = copy.deepcopy(keyword)
        keyword.path = self._path
        self.keywords.content.append(keyword)

    @set_lastest_modify_time
    def reformat(self, suffix):
        format_content = self._get_format_content(self._get_target(self._path), self._path, suffix=suffix)
        os.remove(self._path)
        self._path = os.path.splitext(self._path)[0] + ".{0}".format(suffix)
        with open(self._path, "w", encoding="UTF-8") as f:
            f.write(format_content)
            f.flush()
        self._suffix = suffix

    def _delete_item(self, options):
        table = getattr(self, options.get("table").lower())
        options.update({"path": self._path})
        table.del_child(options)

    def update_local_repository(self):
        DataFile.TMP_REPO_PARSE_FILES.append(self._path)
        if hasattr(self, "keywords"):
            for keyword in self.keywords.content:
                keyword.path = self._path
                LocalKeyWordRepository().add(keyword.name, keyword)
                LocalKeyWordRepository().add(".".join([os.path.splitext(os.path.split(keyword.path)[-1])[0], keyword.name]), keyword)
        if hasattr(self, "variables"):
            for variable in self.variables.content:
                variable.path = self._path
                LocalVariableRepository().add(variable.name, variable)

    def store_resource(self):
        if not hasattr(self, "settings"):
            return
        for text_block in self.settings.content:
            if isinstance(text_block, Resource):
                self._update_local_repository(text_block)
            elif isinstance(text_block, Library):
                self._update_ref_buildin_keywords(text_block)

    def _update_ref_buildin_keywords(self, text_block):
        buildin_lib_name = text_block.get_relative_path()
        if buildin_lib_name not in STDLIBS or self._is_stored_in_local_repo(buildin_lib_name):
            return
        DataFile.TMP_REPO_PARSE_FILES.append(buildin_lib_name)
        RfStdLibs(BuildInKeyWordRepository(), True).parse(buildin_lib_name)

    def _update_local_repository(self, text_block):
        abs_path = self._get_abs_path(text_block)
        if not abs_path:
            return
        if self._is_stored_in_local_repo(abs_path):
            return
        resource = DataFileRepository().find(abs_path)
        if not resource:
            return
        resource.update_local_repository()
        resource.store_resource()

    def _parse_libraries(self):
        BuildInKeyWordRepository().clear()
        RfStdLibs(BuildInKeyWordRepository(), True).parse("BuiltIn")
        if not hasattr(self, "settings"):
            return
        for text_block in self.settings.content:
            if isinstance(text_block, Library):
                relative_path = text_block.get_relative_path()
                if relative_path in STDLIBS:
                    RfStdLibs().parse(relative_path)
                else:
                    if os.path.isfile(os.path.join(os.path.dirname(self.path), relative_path)):
                        if os.path.splitext(relative_path)[-1] == ".py":
                            PyLib(relative_path, source_path=self.path).parse()
                    else:
                        PyLib(relative_path, is_import_path=True).parse()

    def _clear_attr(self):
        if hasattr(self, "settings"):
            del self.settings
        if hasattr(self, "keywords"):
            del self.keywords
        if hasattr(self, "variables"):
            del self.variables
        if hasattr(self, "testcases"):
            del self.testcases

    def _copy_attrs(self, data_file):
        cur_attrs = []
        for attr in ["settings", "keywords", "variables", "testcases", "is_dirty"]:
            if hasattr(data_file, attr):
                cur_attrs.append(attr)
        for attr in cur_attrs:
            setattr(self, attr, getattr(data_file, attr))

    def _clear_local_repo(self):
        LocalKeyWordRepository().clear()
#         LocalVariableRepository().clear()
        DataFile.TMP_REPO_PARSE_FILES = []

    def _is_stored_in_local_repo(self, abs_path):
        return abs_path in DataFile.TMP_REPO_PARSE_FILES

    def _add_item(self, options):
        table_name = options.get("table")
        if hasattr(self, table_name.lower()):
            table = getattr(self, table_name.lower())
        else:
            table = eval(table_name)()
            setattr(self, table_name.lower(), table)
        if str(table.add_child(options)) == "repeat":
            return "repeat"
        self.set_dirty()
        return table

    def _check_file(self, target, path, is_clear_cache=False):
        try:
            file_content = self._get_format_content(target, path, is_clear_cache=is_clear_cache)
            print('liuweibo-------_check_file-')
            print(self._path)
            print(file_content)
            with open(self._path, "w", encoding='utf-8') as f:
                f.write(file_content)
                f.flush()
            PluginRepository().add('FILE_WRITE_CHECK', '')
        except Exception as e:
            traceback.print_exc()
            PluginRepository().add('FILE_WRITE_CHECK', [str(e), self._path])
        finally:
            if is_clear_cache:
                os.remove(path)

    def _get_format_content(self, target, path, suffix=None, is_clear_cache=True):
        suffix = self._suffix if not suffix else suffix
        FromStringIOPopulator(target).populate(path)
        output = StringIO()
        target.save(output=output, format=suffix, txt_separating_spaces=4)
        return output.getvalue()

    def _inject(self, tables):
        for table in tables:
            setattr(self, table.__class__.__name__.lower(), table)

    def _parse_resources(self):
        for text_block in self.settings.content:
            if isinstance(text_block, Resource):
                abs_path = self._get_abs_path(text_block)
                if self._is_need_parse(abs_path):
                    data_file = ResourceDataFile(abs_path)
                    DataFileRepository().add(abs_path, data_file)
                    data_file.parse()

    def _get_abs_path(self, text_block):
        relative_path = text_block.get_relative_path()
        if not relative_path:
            return None
        res = os.path.abspath(os.path.join(os.path.dirname(self._path), relative_path))
        return res

    def _is_need_parse(self, abs_path):
        if not abs_path:
            return False
        suffix = os.path.splitext(abs_path)[-1].lstrip(".")
        if os.path.exists(abs_path) and abs_path not in DataFileRepository().keys() and suffix in SUFFIX_READER_MAP.keys():
            return True

    def _has_settings(self):
        return hasattr(self, "settings")

    def get_path(self):
        return self._path

    def _load_init_file(self):
        tree = ProjectTreeRepository().find(os.path.split(self._path)[0])
        if not tree:
            return
        if not isinstance(self, DictionaryDataFile) and tree.init_file:
            tree.init_file.open(False)

    def _clear_cur_keywords(self):
        if not hasattr(self, "keywords"):
            return
        for keyword in self.keywords.content:
            LocalKeyWordRepository().delete(keyword)
            KeyWordRepository().delete(keyword)

    def _clear_cur_variables(self):
        if not hasattr(self, "variables"):
            return
        for variable in self.variables.content:
            LocalVariableRepository().delete(variable)
            VariableRepository().delete(variable)

    def _format_for_head(self, path):
        content = ""
        if ROBOT_VERSION == 'robot2':
            with open(path, "r", encoding='utf-8') as f:
                for line in f.readlines():
                    if line.strip().lstrip(":").strip().lower().startswith("for\t"):
                        cells = line.split("\t")
                        if len(cells) > 1:
                            cells[1] = ": FOR"
                            for index in range(1, len(cells)):
                                value = cells[index]
                                tmp_variables = re.findall("{(.*)}", value)
                                if len(tmp_variables) == 0:
                                    format_str = " ".join(re.findall("\w+", cells[index].upper()))
                                    if format_str in ["IN", "IN RANGE"]:
                                        cells[index] = format_str
                        line = "\t".join(cells)
                    content += line
        else:
            with open(path, "r", encoding='utf-8') as f:
                for line in f.readlines():
                    if line.strip().lstrip(":").strip().lower().startswith("for\t"):
                        cells = line.split("\t")
                        if len(cells) > 1:
                            cells[1] = "FOR"
                            for index in range(1, len(cells)):
                                value = cells[index]
                                tmp_variables = re.findall("{(.*)}", value)
                                if len(tmp_variables) == 0:
                                    format_str = " ".join(re.findall("\w+", cells[index].upper()))
                                    if format_str in ["IN", "IN RANGE"]:
                                        cells[index] = format_str
                        line = "\t".join(cells)
                    content += line
        with open(path, "w", encoding='utf-8') as f:
            f.write(content)
            f.flush()

    # Keep the old method for backward compatibility
    def _format_for_head_(self, path):
        self._format_for_head(path)

    def _refresh_sub_items(self):
        suffix = os.path.splitext(self.path)[-1]
        tmp_path = os.path.join(self._tmp_path, '.rfcode_tmp{0}'.format(suffix))
        if not os.path.exists(tmp_path):
            return
        shutil.copyfile(self.path, tmp_path)
        data_file = DataFile(tmp_path)
        data_file.parse(tmp_path)
        for attr in ["testcases", "keywords"]:
            if hasattr(self, attr):
                if len(getattr(self, attr).content) != len(getattr(data_file, attr).content):
                    os.remove(tmp_path)
                    return
        for attr in ["testcases", "keywords"]:
            if hasattr(self, attr):
                for index in range(0, len(getattr(self, attr).content)):
                    getattr(self, attr).content[index].content = getattr(data_file, attr).content[index].content
        os.remove(tmp_path)

    def _is_running_testcase(self):
        return PluginRepository().find('TESTCASE_RUNNING')

    def _clear_cache(self):
        self._cache = ""


class SuiteDataFile(DataFile):

    def check_file(self, path):
        return self._check_file(self._get_target(path), path)

    def _get_target(self, path):
        return TestCaseFile(source=path)

    def query(self):
        """查询测试套件文件的基本信息"""
        return {
            "path": self._path,
            "keywords": [kw.name for kw in self.keywords.content] if hasattr(self, "keywords") else [],
            "variables": [var.name for var in self.variables.content] if hasattr(self, "variables") else [],
            "testcases": [tc.name for tc in self.testcases.content] if hasattr(self, "testcases") else []
        }


class ResourceDataFile(DataFile):

    def check_file(self, path):
        return self._check_file(self._get_target(path), path)

    def _get_target(self, path):
        return ResourceFile(source=path)

    def query(self):
        """查询资源文件的基本信息"""
        return {
            "path": self._path,
            "keywords": [kw.name for kw in self.keywords.content] if hasattr(self, "keywords") else [],
            "variables": [var.name for var in self.variables.content] if hasattr(self, "variables") else []
        }


class DictionaryDataFile(DataFile):

    def query(self):
        """查询字典文件的基本信息"""
        return {
            "path": self._path,
            "keywords": [kw.name for kw in self.keywords.content] if hasattr(self, "keywords") else [],
            "variables": [var.name for var in self.variables.content] if hasattr(self, "variables") else []
        }

    def add(self, name, suffix="tsv"):
        path = os.path.abspath(os.path.join(name + "." + suffix))
        file = DictionaryDataFile(path)
        if self._is_child(file):
            return False
        file.set_dirty()
        self._files.append(file)
        DataFileRepository().add(path, file)
        return file

    def check_file(self, path):
        return self._check_file(self._get_target(path), path)

    def _get_target(self, path):
        return TestCaseFile(source=path)


class PyDataFile(DataFile):

    def __init__(self, path):
        super().__init__(path)
        self._cache = ""
        if os.path.exists(path):
            self._latstest_modify_time = time.mktime(time.localtime(os.stat(path).st_mtime))

    @set_lastest_modify_time
    def parse(self, path=None, force_parse=True):
        self._clear_cache()
        return self._path

    def delete(self):
        os.remove(self._path)

    @set_lastest_modify_time
    def get_content(self):
        if self._cache != "":
            return self._cache
        with open(self._path, encoding=FileHandler.get_file_encoding(self._path)) as f:
            content = f.read()
        return content

    @set_lastest_modify_time
    def save_content(self, content):
        content = content.replace("\r\n", "\n")
        if not self._is_running_testcase():
            with open(self._path, "w", encoding='UTF-8') as f:
                f.write(content)
            self._clear_cache()
        else:
            SaveSequence.add(self)
        self.is_dirty = False

    @set_lastest_modify_time
    def save(self):
        if not self._cache:
            return
        if not self._is_running_testcase():
            with open(self._path, "w", encoding='UTF-8') as f:
                f.write(self._cache)
            self._clear_cache()
        else:
            SaveSequence.add(self)
        self.is_dirty = False

    def update(self, content):
        content = content.replace("\r\n", "\n")
        self._cache = content

if __name__ == '__main__':
    DataFile("")
    data_file = SuiteDataFile(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\01_MTS\V3Epic_Story_3017047_【Tool】支持Ue级PPS流量统计在MTS展示\NR2V92001GNB1VBP.tsv")
    data_file.reformat("txt")
    data_file = DictionaryDataFile(r"C:\Users\<USER>\Desktop\Testw\__init__.tsv")
    data_file.parse()
    data_file.save()
    RfStdLibs().parse()
    t1 = time.time()
    data_file = SuiteDataFile(r"D:\Demo\5g_nr_v2\1009\script_v2\5GNR\test\testcases\208\NR2_02_SPA\2T_1UE1VBP_6AAU\1UE1VBP_6AAU.tsv")
    data_file.parse()
    data_file.open()
    print(len(LocalKeyWordRepository().keys()), LocalKeyWordRepository().keys())
    print(len(LocalVariableRepository().keys()), LocalVariableRepository().keys())
    testcase = SuiteDataFile(r"D:\Demo\5g_nr_v2\1009\script_v2\5GNR\test\testcases\208\NR2_02_SPA\2T_1UE1VBP_6AAU\1UE1VBP_6AAU.tsv").parse()
    print(len(KeyWordRepository().keys()), KeyWordRepository().keys())
    print(len(VariableRepository().keys()), VariableRepository().keys())
    datafile = SuiteDataFile(r"D:\Demo\rf-ide\rf-ide\testcases\ut\test_file\t1.tsv")
    datafile.parse()
    datafile.open()
    datafile.save()
    print(datafile.get_content())
    datafile.delete_variable(0)
    datafile.delete_keyword(0)
    datafile.delete_testcase(0)
    datafile.add_keyword("测试添加关键字", "${gupan}|${test}")
    datafile.add_testcase("测试添加用例")
    datafile.add_list("测试添加列表", ["@{test_modify_variable_name}", "test_modify_variable", "# gupan | # gggggg"])
    datafile.add_scalar("测试添加变量", ["${test_modify_variable_name}", "test_modify_variable", "# gupan | # gggggg"])
    print(datafile.get_content())
    print("测试添加关键字" in KeyWordRepository().keys())
    print("测试添加变量" in VariableRepository().keys())
    t2 = time.time()
    datafile.delete_keyword(-1)
    datafile.delete_variable(-1)
    print("测试添加关键字" in KeyWordRepository().keys())
    print("测试添加变量" in VariableRepository().keys())
    print(KeyWordRepository().query("计算"))
    data_file = SuiteDataFile(r"D:\Demo\5g_nr_v2\1009\script_v2\5GNR\test\testcases\208\NR2_02_SPA\2T_1UE1VBP_6AAU\1UE1VBP_6AAU.tsv")
    data_file.parse()
    data_file.open()
    print(LocalKeyWordRepository().query("Run Keyword If"))
    print(t2 - t1)
