# coding=utf-8
'''
Created on 2019年10月31日

@author: 10240349
'''
import copy
import os
import re
import traceback

from PyQt5.Qt import QTreeWidgetItem
from PyQt5.QtWidgets import QLineEdit

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from model.CurrentItem import CurrentItem
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from view.common.FileNameValidator import FileNameValida<PERSON>
from view.common.MessageBox import MessageBox
from view.common.dialog.FormatDialog import FormatDialog
from utility.UIRepository import UIRepository


class TreeItem(QTreeWidgetItem):

    def __init__(self, parent, data_file=None, path=None):
        super(TreeItem, self).__init__(parent)
        self._parent = parent
        self._data_file = data_file
        self._is_clicked = False
        self._path = path
        if not self._data_file:
            self._data_file = self.generate_data_file()
        self.set_ui()

    def refresh_children(self, force_parse=True):
        checked_states = self._get_checked_states()
        self.remove_children()
        self._data_file.parse(force_parse=True)
        self.reload_children()
        SignalDistributor().show(self)
        self._remove_exective_testcases()
        self.set_name_without_star()
        self._restore_checked_states(checked_states)

    def _remove_exective_testcases(self):
        refreshed_item_name = self.get_name()
        _list = ExecutiveTestCaseRepository().find('EXECUTIVE_TESTCASES')
        if _list:
            exective_testcases = [item for item in _list]
            exective_list = copy.deepcopy(exective_testcases)
            for testcase in exective_testcases:
                path = testcase[0]
                if refreshed_item_name in path:
                    exective_list.remove(testcase)
            exective_testcases = exective_list
            ExecutiveTestCaseRepository().update('EXECUTIVE_TESTCASES', exective_testcases)

    def refresh_children_without_parse(self):
        checked_states = self._get_checked_states()
        self.remove_children()
        self.reload_children()
        self._restore_checked_states(checked_states)

    def reload_children_once(self):
        if not self._is_clicked:
            self._is_clicked = True
            self.remove_children()
            self.reload_children()
        if not self.childCount():
            self.set_name_with_empty()

    def update_repository(self):
        self.update_local_repository()

    def reload_children(self):
        raise NotImplementedError()

    def remove_children(self):
        for child in self.children:
            self.removeChild(child)

    def expand(self):
        raise NotImplementedError()

    def set_ui(self):
        raise NotImplementedError()

    def set_name_with_empty(self):
        self.setText(0, self._name + "(空)")

    def set_name_with_star(self):
        self.setText(0, '*' + self._name)

    def set_name_without_star(self):
        self.setText(0, self._name)

    def generate_data_file(self):
        raise NotImplementedError()

    def update_text_edit(self, path):
        try:
            PluginRepository().find('TEXT_EDIT').load_file(path)
        except Exception:
            traceback.print_exc()

    @property
    def children(self):
        cs = []
        for i in range(self.childCount()):
            cs.append(self.child(i))
        return cs

    def _get_focus_child(self, item):
        index = self.parent().indexOfChild(item)
        if index == self.parent().childCount() - 1:
            return self.parent().child(index - 1)
        else:
            return self.parent().child(index + 1)

    def get_name(self):
        return self._name

    def is_unique(self, name, item_type):
        name_without_char = re.sub('\&|\$|@|{|}', '', name)
        _list = self._get_child_name_list(item_type)
        if name_without_char in _list:
            return False
        else:
            return True

    def _get_child_name_list(self, item_type):
        _list = []
        for i in range(self.childCount()):
            if isinstance(self.child(i), item_type):
                name_without_suffix = os.path.splitext(self.child(i).get_name())[0]
                _list.append(re.sub('\&|\$|@|{|}', '', name_without_suffix))
        return _list

    def _set_edit_status(self, text):
        self._tree_item = ProjectTreeRepository().find("PROJECT_TREE")
        self._renamed_item = self._tree_item.currentItem()
        SignalDistributor().show(self._renamed_item)
        self._set_item_widget(text)

    def _set_item_widget(self, text):
        self._name_line = QLineEdit()
        self._tree_item.setItemWidget(self._renamed_item, 0, self._name_line)
        self._name_line.setText(text)
        self._name_line.setFocus()
        self._name_line.setCursorPosition(0)
        self._name_line.returnPressed.connect(self._rename)
        self._tree_item.clicked.connect(self._rename_by_click)

    def get_child_list(self, item_type):
        _list = []
        for i in range(self.childCount()):
            if isinstance(self.child(i), item_type):
                _list.append(self.child(i))
        return _list

    def _rename(self):
        item = self._data_file.query()
        new_name = self._name_line.text()
        if new_name != item.name:
            if self._is_name_legal(new_name):
                self._renamed_item.setText(0, new_name)
                self._save_data(item)
                self._set_unchecked()
                self._name = new_name
            else:
                self._renamed_item.setText(0, item.name)
            SignalDistributor().refresh_text_edit_content(item)
            SignalDistributor().show(self._tree_item.currentItem())
        self._close_editor()

    def _close_editor(self):
        self._tree_item.closePersistentEditor(self._renamed_item, 0)
        self._tree_item.removeItemWidget(self._renamed_item, 0)
        self._tree_item.clicked.disconnect(self._rename_by_click)

    def _rename_by_click(self):
        if self._tree_item.currentItem() != self._renamed_item:
            self._rename()

    def _is_name_legal(self, name):
        if not name.strip():
            MessageBox().show_critical('Name cannot be empty.')
            return False
        if not self.parent().is_unique(name, type(self)):
            MessageBox().show_critical('Name already exists')
            return False
        return True

    def _is_file_name_legal(self, name):
        if FileNameValidator._is_define_illegal_char(name) or FileNameValidator._is_sys_illegal_char(name):
            MessageBox().show_critical(LanguageLoader().get('ILLEGAL_NAME'))
            return False
        return self._is_name_legal(name)

    def _save_data(self, item):
        item.modify('name', self._renamed_item.text(0))
        CurrentItem().set(self._renamed_item)
        data_file = ItemParserFacory().create(CurrentItem().get()['type'] +
                                              'Parser').get_cur_data_file(self._renamed_item)
        if data_file:
            data_file.save()

    def _set_unchecked(self):
        if self.checkState(0):
            self.set_unchecked(self._renamed_item)

    def _refresh_content(self, item):
        SignalDistributor().refresh_text_edit_content(item)
        ProjectTreeRepository().find("PROJECT_TREE").setCurrentItem(item)
        SignalDistributor().show(item)

    def _change_item_format(self):
        self._format_dialog = FormatDialog(self)
        self._format_dialog.show()
        self._format_dialog.ok_pressed.connect(self._set_item_format)

    def _set_item_format(self, _format):
        self._data_file.reformat(_format)
        name = os.path.split(self._data_file.get_path())[-1]
        self._name = name
        self.setText(0, name)
#         SignalDistributor().show(self)

    def get_child_by_name(self, name):
        """根据名称获取子项目对象

        Args:
            name (str): 要查找的名称

        Returns:
            ProjectTreeItem: 匹配的子项目对象，未找到返回None
        """
        for i in range(self.childCount()):
            child = self.child(i)
            if hasattr(child, 'text') and child.text(0) == name:
                return child
        return None

    def _get_checked_states(self):
        """获取当前所有子项的勾选状态"""
        from view.explorer.tree_item.CheckBoxApi import CheckBoxApi
        states = {}
        for i in range(self.childCount()):
            child = self.child(i)
            # 只保存支持勾选功能的子项的状态
            if isinstance(child, CheckBoxApi):
                try:
                    states[child.get_name()] = child.checkState(0)
                except Exception as e:
                    print(f"获取子项 {child.get_name()} 的勾选状态时出错: {e}")
        return states

    def _restore_checked_states(self, states):
        """恢复子项的勾选状态"""
        from view.explorer.tree_item.CheckBoxApi import CheckBoxApi
        for i in range(self.childCount()):
            child = self.child(i)
            name = child.get_name()
            # 检查子项是否支持勾选功能（继承了CheckBoxApi）并且在保存的状态中
            if name in states and isinstance(child, CheckBoxApi):
                try:
                    child.setCheckState(0, states[name])
                except Exception as e:
                    print(f"恢复子项 {name} 的勾选状态时出错: {e}")

    def _set_home_page_handler(self):
        UIRepository().update('user_home_item', self)
        UIRepository().find('MainFrame').simulate_home_button_click()
        # 获取文件路径并发送到RF助手
        try:
            # Get file path based on data file type
            file_path = None
            if hasattr(self._data_file, '_path'):
                file_path = self._data_file._path
            elif hasattr(self._data_file, 'path'):
                file_path = self._data_file.path
            elif hasattr(self._data_file, 'get_path'):
                file_path = self._data_file.get_path()
            # If we still don't have a path, try to get it from the parent
            if not file_path and hasattr(self, 'parent') and hasattr(self.parent(), '_data_file'):
                parent_data_file = self.parent()._data_file
                if hasattr(parent_data_file, 'get_path'):
                    file_path = parent_data_file.get_path()
            if not file_path:
                print("Error: Could not determine file path from data file")
                return
            rf_assistant_plugin = PluginRepository().find('RFAssistantPlugin')
            if rf_assistant_plugin and hasattr(rf_assistant_plugin, 'main_window'):
                rf_helper = rf_assistant_plugin.main_window
                if hasattr(rf_helper, 'set_main_workspace_path'):
                    rf_helper.set_main_workspace_path(file_path)
                    print(f"Successfully set main workspace path: {file_path}")
                else:
                    print("Error: rf_helper does not have set_main_workspace_path method")
            else:
                print("Error: rf_assistant_plugin not found or does not have main_window attribute")
        except Exception as e:
            print(f"Error setting main workspace path: {str(e)}")
            print("Detailed exception information:")
            traceback.print_exc()
