*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-5586600 修改PB单板功能模式__RAN-5586600	${almStart}	查询基站当前告警_多模	${GNODEB}				
	修改级联PB功能模式_多模	${GNODEB}	PB_10-instance	13	0		
	修改级联PB功能模式_多模	${GNODEB}	PB_30-instance	13	0		
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5623420 双上联场景下，反复增删5G小区__RAN-5623420	${almStart}	查询基站当前告警_多模	${GNODEB}				
	删除基站NR制式MO_多模	${GNODEB}					
	sleep	60					
	创建基站NR制式MO_多模	${GNODEB}					
	sleep	600					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5623419 双上联场景下，反复增删4G小区__RAN-5623419	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除基站FDD制式MO_多模	${ENODEB}					
	删除基站TDD制式MO_多模	${ENODEB}					
	sleep	60					
	创建基站FDD制式MO_多模	${ENODEB}					
	创建基站TDD制式MO_多模	${ENODEB}					
	sleep	600					
	Wait Until Keyword Succeeds	6min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5623425 双上联场景下，小区带宽改配__RAN-5623425	#修改NR小区带宽						
	模板修改NR小区带宽_多模	${GNODEB}	${cell1}	80	217		
	#修改LTE小区带宽						
	修改LTE采样速率模式配置_多模	${fddCell1}	0				
	修改LTE小区带宽_多模	${fddCell1}	10				
	检查激活配置	${GNODEB}	${True}	both			
	sleep	300					
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	sleep	90					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5623426 双上联场景下，小区改配__RAN-5623426	保存NR小区信息_多模	${GNODEB}					
	删除指定NR小区_多模	${GNODEB}	6				
	sleep	60					
	创建指定NR小区_多模	${GNODEB}	6				
	sleep	120					
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5586588 PB非共享与共享切换__RAN-5586588	${almStart}	查询基站当前告警_多模	${GNODEB}				
	批量修改设备共享开关	PB1125H	1				
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	批量修改设备共享开关	PB1125H	0				
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	批量修改设备共享标识	PB1125H	1234				
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	批量修改设备共享标识	PB1125H	${None}				
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建UE对象	${CPE4}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除UE对象	${CPE4}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
恢复环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	120					
							
批量修改设备共享开关	[Arguments]	${boardName}	${sharedState}				
	@{boards}	获取实例化单板别名_多模	${GNODEB}	${boardName}			
	: FOR	${board}	IN	@{boards}			
	\	${moid}	evaluate	'${board}'.split('-')[0]			
	\	${filterDict}	create dictionary	mocName=ReplaceableUnit	moId=${moid}		
	\	${attrDict}	create dictionary	sharedSwitch=${sharedState}			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	同步规划区数据_多模	${GNODEB}					
							
批量修改设备共享标识	[Arguments]	${boardName}	${sharedUniqueId}				
	@{boards}	获取实例化单板别名_多模	${GNODEB}	${boardName}			
	: FOR	${board}	IN	@{boards}			
	\	${moid}	evaluate	'${board}'.split('-')[0]			
	\	${filterDict}	create dictionary	mocName=ReplaceableUnit	moId=${moid}		
	\	${attrDict}	create dictionary	sharedUniqueId=${sharedUniqueId}			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	同步规划区数据_多模	${GNODEB}					
