import re

from PyQt5.Qt import Qt, QFont
from PyQt5.QtGui import QBrush, QColor
from PyQt5.QtWidgets import QTableWidgetItem

from model.data_file.Repository import LocalKeyWordRepository, LocalVariableRepository
from model.data_file.Variables import Variable
from settings.UserSettings import UserSettings
from utility.Singleton import Singleton


@Singleton
class Colorizer(object):

    def init_settings(self):
        self._backgroundColor = ColorizationSettings().get_background_color()
        self._argsColor = ColorizationSettings().get_variable_color()
        self._errColor = ColorizationSettings().get_error_color()
        self._font = ColorizationSettings().get_font()
        self._size = ColorizationSettings().get_font_size()
        self._textColor = ColorizationSettings().get_text_color()
        self._highlightColor = ColorizationSettings().get_keyword_color()

    def colorize(self, table, row, line):
        column = -1
        self._find_keyword(line)
        comment = False
        wei_comment = False
        line = self._format_line_content(line)
        for index in range(0, len(line)):
            column = column + 1
            item = QTableWidgetItem(line[index])
            if comment:
                item.setForeground(self._get_color("comment"))
            elif wei_comment:
                item.setForeground(self._get_color("wei_comment"))
            else:
                result = self._colorize(item, index)
                if result == "comment":
                    comment = True
                elif result == "wei_comment":
                    wei_comment = True
            item.setFont(QFont(self._font, int(self._size), QFont.Black))
            all_row = table._table.rowCount()
            all_column = table._table.columnCount()
            if row + 1 == all_row:
                table._table.insertRow(all_row)
            if column + 1 >= all_column:
                table._table.insertColumn(all_column-1)
            table._table.setItem(int(row), int(column), item)

    def _format_line_content(self, line):
        for index in self._keyword_args_indexs:
            if index > len(line) - 1:
                line.insert(index, '')
        return line

    def _check_unfilled_cell(self, table, row):
        for i in self._keyword_args_indexs:
            if table.item(row, i) and table.item(row, i).text():
                continue
            item = QTableWidgetItem("")
            item.setBackground(self._get_color("attention"))
            item.setFont(QFont(self._font, int(self._size), QFont.Black))
            all_row = table.rowCount()
            if i + 1 >= all_row:
                table.insertRow(all_row)
            table.setItem(int(row), int(i), item)

    def _colorize(self, item, index):
        if item.text() == "" and index in self._keyword_args_indexs:
            item.setBackground(self._get_color("attention"))
            item.setForeground(self._get_color("text"))
        elif item.text().startswith('#'):
            item.setForeground(self._get_color("wei_comment"))
            return 'wei_comment'
        elif item.text() == "\\":
            item.setBackground(self._get_color("comment"))
            item.setForeground(self._get_color("comment"))
        elif self._is_variable(item.text()):
            if index < self._last_keyword_index:
                if not self._is_local_variable(item.text()):
                    vaiable_name = re.match("^\$\{(.*)\}=?$", item.text()).group(1)
                    LocalVariableRepository().add(vaiable_name, Variable(vaiable_name))
                item.setForeground(self._get_color("variable"))
                item.setBackground(self._get_color("background"))
            elif index > self._last_keyword_index and self._is_local_variable(item.text()):
                item.setForeground(self._get_color("variable"))
                item.setBackground(self._get_color("background"))
                if index in self._keyword_args_indexs and item.text():
                    self._keyword_args_indexs.remove(index)
            else:
                item.setForeground(self._get_color("error"))
                item.setBackground(self._get_color("background"))
        elif self._is_keyword(item.text()) or item.text().upper() == "END":
            # 将END关键字与其他关键字显示为相同的蓝色
            item.setForeground(self._get_color("keyword"))
            item.setBackground(self._get_color("background"))
            if item.text().upper() == "COMMENT":
                return "comment"
        else:
            item.setForeground(self._get_color("text"))
            item.setBackground(self._get_color("background"))
        return True

    def _get_color(self, type):
        if type == "variable":
            colorTuple = ColorizationSettings().get_variable_color()
        elif type == "keyword":
            colorTuple = ColorizationSettings().get_keyword_color()
        elif type == "error":
            colorTuple = ColorizationSettings().get_error_color()
        elif type == "warn":
            colorTuple = ColorizationSettings().get_warn_color()
        elif type == "background":
            colorTuple = ColorizationSettings().get_background_color()
        elif type == "comment":
            colorTuple = ColorizationSettings().get_comment_color()
        elif type == "wei_comment":
            colorTuple = ColorizationSettings().get_wei_comment_color()
        elif type == "attention":
            colorTuple = ColorizationSettings().get_attention_color()
        else:
            colorTuple = ColorizationSettings().get_text_color()
        return QColor(int(colorTuple[0]), int(colorTuple[1]), int(colorTuple[2]))

    def _find_keyword(self, line):
        self._last_keyword_index = -1
        self._keyword_args_indexs = []
        for i in range(len(line) - 1, -1, -1):
            if not line[i]:
                continue
            keyword_dicts = LocalKeyWordRepository().query(line[i])
            if keyword_dicts and keyword_dicts.get(line[i]) and self._last_keyword_index < 0:
                self._last_keyword_index = i
                self._apend_keyword_args_indexs(i, keyword_dicts.get(line[i])[-1].get("arguments"))
            elif keyword_dicts and keyword_dicts.get(line[i]):
                self._apend_keyword_args_indexs(i, keyword_dicts.get(line[i])[-1].get("arguments"))

    def _apend_keyword_args_indexs(self, index, args):
        args_count = 0
        for arg in args:
            if "=" in arg:
                break
            args_count += 1
        for j in range(args_count):
            self._keyword_args_indexs.append(index + j + 1)

    def _is_variable(self, cell):
        match = re.match("^\$\{(.*)\}=?$", cell)
        return True if match and not match.group(1).isdigit() and "${"  not in match.group(1) else False

    def _is_local_variable(self, cell):
        variable = re.match("^\$\{(.*)\}=?$", cell).group(1)
        local_variables = LocalVariableRepository().query(variable).get(variable)
        return True if local_variables else False

    def _is_keyword(self, cell):
        # 检查是否是本地关键字
        if LocalKeyWordRepository().query(cell).get(cell):
            return True
        # 检查是否是保留关键字（如FOR、END等）
        reserved_keywords = ['FOR', ':FOR', ': FOR','WHILE', 'BREAK', 'CONTINUE', 'END',
                            'IF', 'ELSE', 'ELIF', 'ELSE IF', 'RETURN', 'IN', 'IN RANGE']
        return cell.upper() in reserved_keywords

    def _coloring_task(self, selection_content, row=0, col=0):
        if self._grid:  # For example move from RIDE Log tab to Grid
            if row >= self._grid.NumberRows:
                self._grid.ForceRefresh()
            elif col < self._grid.NumberCols:
                self._colorize_cell(row, col, selection_content)
                self._coloring_task(selection_content, row, col + 1)
            else:
                self._coloring_task(selection_content, row + 1, 0)

    def _colorize_cell(self, row, col, selection_content):
        cell_info = self._controller.get_cell_info(row, col)
        if cell_info is None:
            self._set_default_colors(row, col)
            return
        self._grid.SetCellTextColour(row, col, self._get_text_color(cell_info))
        self._grid.SetCellBackgroundColour(row, col, self._get_background_color(cell_info, selection_content))
        self._grid.SetCellFont(row, col, self._get_cell_font(row, col, cell_info))

    def _set_default_colors(self, row, col):
        self._grid.SetCellTextColour(row, col, self._colors.get_text_color)
        self._grid.SetCellBackgroundColour(row, col, self._colors.get_background_color)

    def _get_text_color(self, cell_info):
        return self._colors.get_text_color(cell_info.content_type)

    def _get_background_color(self, cell_info, selection_content):
        if cell_info.matches(selection_content):
            return self._colors.get_highlight_color()
        if cell_info.has_error():
            return self._colors.get_error_color()
        return self._colors.get_background_color(cell_info.cell_type)

    def _get_cell_font(self, row, col, cell_info):
        font = self._grid.GetCellFont(row, col)
        font.SetWeight(self._get_weight(cell_info))
        return font


@Singleton
class ColorizationSettings(object):

    def get_background_color(self):
        return UserSettings().get_value("BACKGROUND")

    def get_attention_color(self):
        return UserSettings().get_value("ATTENTION")

    def get_unavailible_color(self):
        return UserSettings().get_value("UNAVILIBLE")

    def get_text_color(self):
        return UserSettings().get_value("TEXT")

    def get_keyword_color(self):
        return UserSettings().get_value("KEYWORD")

    def get_warn_color(self):
        return UserSettings().get_value("WARN")

    def get_error_color(self):
        return UserSettings().get_value("ERROR")

    def get_variable_color(self):
        return UserSettings().get_value("VARIABLE")

    def get_comment_color(self):
        return UserSettings().get_value("COMMENT")

    def get_wei_comment_color(self):
        return UserSettings().get_value("WEI_COMMENT")

    def get_font(self):
        return UserSettings().get_value("FONT")

    def get_font_size(self):
        return UserSettings().get_value("TABLE_FONT_SIZE")


if __name__ == "__main__":
    print(ColorizationSettings().get_warn_color())
