*** Settings ***
Documentation    测试制表符分割的内置关键字高亮
Library          SeleniumLibrary

*** Variables ***
${URL}           https://www.example.com
${USERNAME}      testuser
${PASSWORD}      testpass

*** Test Cases ***
测试制表符分割的内置关键字
    [Documentation]    测试使用制表符分割时内置关键字的正确高亮
    [Tags]    内置关键字    制表符
    
    # 使用制表符分割的内置关键字（应该显示为中蓝色）
    Log	这是Log关键字	INFO
    Sleep	1s
    Set Variable	test_value
    Should Be Equal	value1	value1
    Should Contain	Hello World	World
    Should Be True	True
    Should Be False	False
    Run Keyword	Log	执行关键字
    Set Test Variable	${TEST_VAR}	test_value
    Set Suite Variable	${SUITE_VAR}	suite_value
    Set Global Variable	${GLOBAL_VAR}	global_value
    
    # 混合使用制表符和空格
    Log    使用空格的Log关键字
    Set Variable	使用制表符的Set Variable	mixed_value
    Should Be Equal    ${TEST_VAR}	test_value
    
    # 复杂的参数组合
    Run Keyword If	True	Log	条件执行成功
    Run Keyword And Return Status	Should Be Equal	1	1
    Run Keyword And Continue On Failure	Should Be Equal	wrong	right

测试变量和关键字混合
    [Documentation]    测试变量和关键字在制表符分割中的处理
    [Tags]    变量    关键字    混合
    
    # 变量应该保持橙色，关键字应该是中蓝色
    ${result}=	Set Variable	test_result
    Log	变量值: ${result}
    Should Be Equal	${result}	test_result
    
    # 列表和字典操作
    ${list}=	Create List	item1	item2	item3
    ${dict}=	Create Dictionary	key1=value1	key2=value2
    Log	列表: ${list}
    Log	字典: ${dict}

测试字符串和关键字
    [Documentation]    测试字符串和关键字的区分
    [Tags]    字符串    关键字
    
    # 字符串应该保持红色，关键字应该是中蓝色
    Log	"这是字符串"	INFO
    Should Contain	"Hello World"	"World"
    Set Variable	"string_value"
    
    # 单引号字符串
    Log	'这是单引号字符串'
    Should Be Equal	'test'	'test'

测试注释和关键字
    [Documentation]    测试注释行中的关键字处理
    [Tags]    注释    关键字
    
    Log	正常的Log关键字
    # Log	这是注释中的Log，应该显示为绿色
    # Should Be Equal	注释中的关键字	应该是绿色
    Comment	这是Comment关键字	应该显示为绿色

测试库关键字和内置关键字
    [Documentation]    测试库关键字和内置关键字的区分
    [Tags]    库关键字    内置关键字
    
    # 内置关键字（中蓝色）
    Log	内置关键字测试
    Set Variable	builtin_value
    Should Be Equal	test	test
    
    # 库关键字（淡蓝色）
    Open Browser	about:blank	chrome
    Get Title
    Close Browser

测试用户关键字
    [Documentation]    测试用户关键字的识别
    [Tags]    用户关键字
    
    # 用户关键字（紫色）
    自定义关键字示例	参数1	参数2
    Another Custom Keyword
    
    # 在用户关键字中使用内置关键字
    Log	在用户关键字中使用内置关键字

*** Keywords ***
自定义关键字示例
    [Arguments]    ${param1}    ${param2}
    [Documentation]    用户自定义关键字示例
    
    # 在用户关键字中使用内置关键字（应该正确高亮）
    Log	参数1: ${param1}
    Log	参数2: ${param2}
    Set Variable	${param1}_${param2}
    Should Be True	True

Another Custom Keyword
    [Documentation]    另一个用户关键字
    
    # 制表符分割的内置关键字
    Log	执行另一个用户关键字
    ${current_time}=	Get Time
    Log	当前时间: ${current_time}
    Should Contain	${current_time}	:

复杂参数的关键字
    [Arguments]    ${complex_param}=default_value
    [Documentation]    带复杂参数的关键字
    
    # 复杂的制表符分割场景
    Log	复杂参数: ${complex_param}
    Run Keyword If	'${complex_param}' != 'default_value'	Log	参数已修改
    Should Be True	len('${complex_param}') > 0
