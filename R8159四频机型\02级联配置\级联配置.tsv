*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-5340289 【Qcell】四级级联PB1125H双上联负荷分担通用功能模式[255]满配置无线资源 NR(4_100M+2_30M ) 4TR+12_LTE 2T2R+8_UMTS（1T1R)+8_GSM，第一二级与三四级互换__RAN-5340289	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.8-5.1.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.7-5.1.2				
	修改光口速率_5818	${GNODEB}	PB_11-instance	OF3	25		
	修改光口速率_5818	${GNODEB}	PB_31-instance	OF3	25		
	创建无线接口连线_多模	VBP_1_8-instance	OF1	PB_30-instance	OF1		
	创建无线接口连线_多模	VBP_1_8-instance	OF6	PB_30-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF1	PB_30-instance	OF1	PB_11-instance	OF4
	更新无线接口连线_多模	VBP_1_8-instance	OF6	PB_30-instance	OF2	PB_11-instance	OF3
	同步规划区数据_多模	${GNODEB}					
	删除并释放无线资源_多模	${ENODEB}					
	${radioStrings}	create list	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G				
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	101-instance		
	\	创建Qcell多模小区_多模	101-instance	${freqDict}			
	sleep	900					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	${cell26}	set variable	@{cellList}[0]				
	验证NR小区	${cell26}	${CPE}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5340294 【Qcell】四级级联PB1125H单上联通用功能模式[255]满配置无线资源 NR(2_100M) 4TR+7_LTE 2T2R+8_UMTS（1T1R)+8_GSM，第一二级与三四级互换__RAN-5340294	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.8-5.1.1				
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#双上联修改单上联						
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	创建无线接口连线_多模	VBP_1_8-instance	OF1	PB_30-instance	OF1		
	更新无线接口连线_多模	VBP_1_8-instance	OF1	PB_30-instance	OF1	PB_11-instance	OF4
	同步规划区数据_多模	${GNODEB}					
	删除并释放无线资源_多模	${ENODEB}					
	${radioStrings}	create list	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G				
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	101-instance		
	\	创建Qcell多模小区_多模	101-instance	${freqDict}			
	sleep	900					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	${cell26}	set variable	@{cellList}[0]				
	验证NR小区	${cell26}	${CPE}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623417 双上联场景四级级联双上双下的同一光链路的第三四级与第一二级PB互换__RAN-5623417	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.4-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.3-5.1.6				
	修改光口速率_5818	${GNODEB}	PB_11-instance	OF3	25		
	修改光口速率_5818	${GNODEB}	PB_31-instance	OF3	25		
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	创建无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	PB_31-instance	OF4
	更新无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2	PB_31-instance	OF3
	同步规划区数据_多模	${GNODEB}					
	删除并释放无线资源_多模	${ENODEB}					
	${radioStrings}	create list	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G				
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	301-instance		
	\	创建Qcell多模小区_多模	301-instance	${freqDict}			
	sleep	900					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	${cell49}	set variable	@{cellList}[0]				
	验证NR小区	${cell49}	${CPE2}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5586674 【Qcell】跨基带板，PB单级双上联负荷分担__RAN-5586674	删除并释放无线资源_多模	${ENODEB}					
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.6				
	#删除二级级联中的第二级PB						
	${filterDict}	create dictionary	mocName=ReplaceableUnit	moId=PB_11			
	${filterDict2}	create dictionary	mocName=RiCable	moId=5			
	${filterDict3}	create dictionary	mocName=RiCable	moId=6			
	${filterDict4}	create dictionary	mocName=RiCable	moId=11			
	${filterDict5}	create dictionary	mocName=RiCable	moId=12			
	__删除节点	${ENODEB}	${filterDict}	planarea			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict4}	planarea			
	__删除节点	${ENODEB}	${filterDict5}	planarea			
	#配置跨基带板负荷分担						
	修改VBP光口协议_多模	VBP_1_4-instance	OF1	1			
	修改VBP光口速率_多模	VBP_1_4-instance	OF1	25			
	创建无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF1		
	更新无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF1	VBP_1_4-instance	OF1
	同步规划区数据_多模	${GNODEB}					
	${radioStrings}	create list	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G				
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	101-instance		
	\	创建Qcell多模小区_多模	101-instance	${freqDict}			
	sleep	15min					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	${cell26}	set variable	nrCell1				
	验证NR小区	${cell26}	${CPE}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-4211846 双上双下，其中1路未配置，PB未配置告警上报__RAN-4211846	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#双上联修改单上联						
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${ENODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	10min	30sec	检查预期告警未上报	${GNODEB}	198097811	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-4211854 双上双下，所有PB都未配置，PB自发现__RAN-4211854	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#修改光接口连线						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.3-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.6				
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	修改VBP光口协议_多模	${VBP7}	OF1	1			
	修改VBP光口速率_多模	${VBP7}	OF1	25			
	#配置跨基带板负荷分担						
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	VBP_1_4-instance	OF1
	创建无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2	0	
	修改无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2	VBP_1_7-instance	OF1
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-4218895 双上双下，非第1级其中1路未配置，PB未配置告警上报__RAN-4218895	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#双上联修改单上联						
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${ENODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	10min	30sec	检查预期告警未上报	${GNODEB}	198097811	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-4218904 双上双下，非第1级PB都未配置，PB未配置告警上报__RAN-4218904	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${pb10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pb11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	删除并释放无线资源_多模	${ENODEB}					
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#删除二级级联中的第二级PB						
	${filterDict1}	create dictionary	mocName=ReplaceableUnit	moId=PB_11			
	${filterDict2}	create dictionary	mocName=RiCable	moId=11			
	${filterDict3}	create dictionary	mocName=RiCable	moId=12			
	${filterDict4}	create dictionary	mocName=RiCable	moId=5			
	${filterDict5}	create dictionary	mocName=RiCable	moId=6			
	__删除节点	${ENODEB}	${filterDict1}	planarea			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict4}	planarea			
	__删除节点	${ENODEB}	${filterDict5}	planarea			
	同步规划区数据_多模	${GNODEB}					
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5586523 增加PB单板__RAN-5586523	log	RAN-5623417已配置					
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5586561 删除PB单板__RAN-5586561	log	RAN-4218904已覆盖					
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5586652 跨基带板，PB四级级联双上联负荷分担，每级配置双上双下__RAN-5586652	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.4-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.3-5.1.6				
	修改光口速率_5818	${GNODEB}	PB_11-instance	OF3	25		
	修改光口速率_5818	${GNODEB}	PB_31-instance	OF3	25		
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	创建无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	PB_31-instance	OF4
	更新无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2	PB_31-instance	OF3
	同步规划区数据_多模	${GNODEB}					
	#配置跨基带板负荷分担						
	创建无线接口连线_多模	VBP_1_8-instance	OF1	PB_30-instance	OF1		
	更新无线接口连线_多模	VBP_1_8-instance	OF1	PB_10-instance	OF1	VBP_1_4-instance	OF1
	同步规划区数据_多模	${GNODEB}					
	${cellAlias}	获取TDD小区别名_多模	${ENODEB}				
	保存LTE-TDD小区信息_多模	${ENODEB}					
	删除指定LTE-TDD小区_多模	${ENODEB}	5				
	同步规划区数据_多模	${ENODEB}					
	sleep	10min					
	#2告警验证						
	Comment	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}
	Comment	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}		
	Comment	验证NR小区	${cell1}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell7}	${CPE2}	${PDN}		
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5586658 双上联负荷分担场景支持功能模式13__RAN-5586658	${almStart}	查询基站当前告警_多模	${ENODEB}				
	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#修改功能模式						
	: FOR	${board}	IN	@{boards}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	13	
	同步规划区数据_多模	${ENODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5586694 跨基带板，PB两级级联双上联负荷分担，双上双下__RAN-5586694	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.2				
	#配置跨基带板负荷分担						
	创建无线接口连线_多模	VBP_1_8-instance	OF6	PB_30-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF6	PB_30-instance	OF2	VBP_1_4-instance	OF1
	同步规划区数据_多模	${GNODEB}					
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5159382 [Qcell] PB1125H双上联负荷分担场景增删多模制式小区__RAN-5159382	${almStart}	查询基站当前告警_多模	${ENODEB}				
	#删除2.3GLTE载波						
	${cell}	获取TDD小区别名_多模	${GNODEB}				
	保存LTE-TDD小区信息_多模	${ENODEB}					
	删除指定LTE-TDD小区_多模	${ENODEB}	3				
	创建指定LTE-TDD小区_多模	${ENODEB}	3				
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#删建NR小区						
	保存NR小区信息_多模	${GNODEB}					
	删除指定NR小区_多模	${GNODEB}	1				
	创建指定NR小区_多模	${GNODEB}	1				
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
							
RAN-5586670 PB1125H双上联负荷分担场景支持功能模式27__RAN-5586670	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF1			
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	27	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623153 [Qcell] PB1125H双上联配置通用功能模式255场景，反复操作执行VBP+PB+Prru单板动态命令__RAN-5623153	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF2			
	${prrus}	create list	${prru}	${prru2}			
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	255	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#复位PRRU						
	: FOR	${board}	IN	@{prrus}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	掉电复位PRRU_多模	${board}				
	#复位PB						
	: FOR	${board}	IN	@{pbs}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	Run Keyword If	'${status}' == 'Normal'	掉电复位PB_多模	${board}		
	#复位基带板						
	: FOR	${i}	IN RANGE	2			
	\	掉电复位VBP_多模	${VBP8}				
	#告警验证						
	sleep	15min					
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	删除配置					
							
RAN-5623158 [Qcell] PB1125H双上联配置通用功能模式255场景，反复操作VBP+PB+Prru+光纤光模块单板诊断后查询告警__RAN-5623158	log	RAN-5291464已覆盖					
	[Teardown]						
							
RAN-5623163 [Qcell] PB1125H双上联配置通用功能模式255场景，四级级联光链路满配置32Prru，第一三级射频合并和第二四级射频合并__RAN-5623163	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.4-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.3-5.1.6				
	修改光口速率_5818	${GNODEB}	PB_11-instance	OF3	25		
	修改光口速率_5818	${GNODEB}	PB_31-instance	OF3	25		
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	创建无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	PB_31-instance	OF4
	更新无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2	PB_31-instance	OF3
	同步规划区数据_多模	${GNODEB}					
	删除并释放无线资源_多模	${ENODEB}					
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruList}	create list					
	${prruList1}	创建指定PB下的多个PRRU_多模	${ENODEB}	PB_30-instance	R8159 *********	${dictPrru}	8
	${prruList2}	创建指定PB下的多个PRRU_多模	${ENODEB}	PB_31-instance	R8159 *********	${dictPrru}	8
	${prruList3}	创建指定PB下的多个PRRU_多模	${ENODEB}	PB_10-instance	R8159 *********	${dictPrru}	8
	${prruList4}	创建指定PB下的多个PRRU_多模	${ENODEB}	PB_11-instance	R8159 *********	${dictPrru}	8
	${prruAllList1}	create list	301-instance	302-instance	101-instance	102-instance	
	${prruAllList2}	create list	311-instance	312-instance	111-instance	112-instance	
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	VBP_1_8-instance	${prruAllList1}	100
	...	0	9,10,11,12	${False}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	VBP_1_4-instance	${prruAllList1}	100
	...	0	3,4,5,6	${False}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	VBP_1_8-instance	${prruAllList2}	100
	...	0	9,10,11,12	${False}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	VBP_1_4-instance	${prruAllList2}	100
	...	0	3,4,5,6	${False}	${False}		
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623168 [Qcell] PB1125H双上联配置通用功能模式255场景，支持PB+Prru节能__RAN-5623168	log	RAN-4956805已覆盖					
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623174 [Qcell] 同基带板，PB1125H单级双上联负荷分担__RAN-5623174	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#删除二级级联中的第二级PB						
	${filterDict}	create dictionary	mocName=ReplaceableUnit	moId=PB_11			
	${filterDict2}	create dictionary	mocName=RiCable	moId=5			
	${filterDict3}	create dictionary	mocName=RiCable	moId=6			
	${filterDict4}	create dictionary	mocName=RiCable	moId=11			
	${filterDict5}	create dictionary	mocName=RiCable	moId=12			
	__删除节点	${ENODEB}	${filterDict}	planarea			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict4}	planarea			
	__删除节点	${ENODEB}	${filterDict5}	planarea			
	同步规划区数据_多模	${GNODEB}					
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF9	${PB10}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF9	${PB10}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.6				
	sleep	8min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2创建PRRU劈裂						
	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruLists}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB10}	R8159 *********	${dictPrru}	8
	${prruLists1}	create list	101-instance	102-instance			
	${prruLists2}	create list	Prru_R8159*********_PB_10_ETH3-instance	Prru_R8159*********_PB_10_ETH4-instance			
	${prruLists3}	create list	Prru_R8159*********_PB_10_ETH5-instance	Prru_R8159*********_PB_10_ETH6-instance			
	${prruLists4}	create list	Prru_R8159*********_PB_10_ETH7-instance	Prru_R8159*********_PB_10_ETH8-instance			
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.1W)@2.3G	(1V*4T4R*100M*0.8W)@2.6G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prruLists1}	${freqDict}			
	\	创建Qcell多模小区_多模	${prruLists2}	${freqDict}			
	\	创建Qcell多模小区_多模	${prruLists3}	${freqDict}			
	\	创建Qcell多模小区_多模	${prruLists4}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623175 [Qcell] 同基带板，PB1125H两级级联双上联负荷分担，双上双下__RAN-5623175	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF9	${PB10}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF9	${PB10}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.6				
	sleep	8min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2创建PRRU劈裂						
	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruLists}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB10}	R8159 *********	${dictPrru}	8
	${prruLists2}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB11}	R8159 *********	${dictPrru}	8
	${prruLists1}	create list	101-instance	102-instance			
	${prruLists2}	create list	111-instance	112-instance			
	: FOR	${radioString}	IN	(1F*2T2R*20M*0.1W)@1.8G	(3T*2T2R*10M*0.1W)@2.3G	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prruLists1}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(1F*2T2R*15M*0.1W)@1.8G	(3T*2T2R*10M*0.1W)@2.3G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prruLists2}	${freqDict}			
	: FOR	${radioString}	IN	(1V*4T4R*80M*0.2W+1V*4T4R*80M*0.2W)@2.6G			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prruLists2}	${freqDict}	VBP_1_4-instance		
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	删除并释放无线资源_多模	${ENODEB}					
	#改配2级射频合并						
	${prruLists3}	create list	101-instance	102-instance	111-instance	112-instance	
	: FOR	${radioString}	IN	(1F*2T2R*20M*0.1W)@1.8G	(3T*2T2R*10M*0.1W)@2.3G	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prruLists3}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623176 [Qcell] 同基带板，PB1125H四级级联双上联负荷分担，每级配置双上双下__RAN-5623176	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${PB31}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_31			
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.4-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	5.1.3-5.1.6				
	修改光口速率_5818	${GNODEB}	PB_11-instance	OF3	25		
	修改光口速率_5818	${GNODEB}	PB_31-instance	OF3	25		
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	创建无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	PB_31-instance	OF4
	更新无线接口连线_多模	VBP_1_8-instance	OF9	PB_10-instance	OF2	PB_31-instance	OF3
	同步规划区数据_多模	${GNODEB}					
	#2创建PRRU满配置						
	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruLists1}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB10}	R8159 *********	${dictPrru}	8
	${prruLists2}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB11}	R8159 *********	${dictPrru}	8
	${prruLists3}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB30}	R8159 *********	${dictPrru}	8
	${prruLists4}	创建指定PB下的多个PRRU_多模	${ENODEB}	${PB31}	R8159 *********	${dictPrru}	8
	${prruAllLists1}	create list	101-instance	102-instance			
	${prruAllLists2}	create list	111-instance	112-instance			
	${prruAllLists3}	create list	301-instance	302-instance			
	${prruAllLists4}	create list	311-instance	312-instance			
	: FOR	${radioString}	IN	(1F*2T2R*20M*0.1W)@1.8G	(3T*2T2R*10M*0.1W)@2.3G	(1V*4T4R*100M*0.2W)@2.6G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru1}		
	\	创建Qcell多模小区_多模	${prruAllLists1}	${freqDict}			
	\	创建Qcell多模小区_多模	${prruAllLists2}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#改配前2级射频合并						
	删除并释放无线资源_多模	${ENODEB}					
	${prruAllLists1}	create list	101-instance	102-instance	111-instance	112-instance	
	${prruAllLists2}	create list	301-instance	302-instance	311-instance	312-instance	
	: FOR	${radioString}	IN	(1F*2T2R*20M*0.1W)@1.8G	(3T*2T2R*10M*0.1W)@2.3G	(1V*4T4R*100M*0.2W+1V*4T4R*60M*0.2W)@2.6G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru1}		
	\	创建Qcell多模小区_多模	${prruAllLists1}	${freqDict}			
	\	创建Qcell多模小区_多模	${prruAllLists2}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623177 [Qcell] PB1125H双上联负荷分担场景功能模式13与功能模式27相互改配__RAN-5623177	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF1			
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	13	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#改功能模式						
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	27	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623221 [Qcell] PB1125H双上联负荷分担场景功能模式13与通用功能模式255相互改配__RAN-5623221	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF1			
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	13	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623234 [Qcell] PB1125H双上联负荷分担场景功能模式27与通用功能模式255相互改配__RAN-5623234	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF1			
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	27	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	255	
	\	Run Keyword And Return Status	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	同步规划区数据_多模	${ENODEB}					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
							
RAN-5623338 [Qcell] PB1125H双上联组网类型改配，单光纤上联与多光纤负荷分担互相改配__RAN-5623338	log	RAN-5586674已覆盖					
							
RAN-5623409 [Qcell] 场景6：第1级PB1125H同板跨片成对接错，正常配置改配__RAN-5623409	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	${nrCells}	获取NR小区别名_多模	${ENODEB}				
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.2-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.2				
	sleep	3min					
	run keyword and ignore error	Wait Until Keyword Succeeds	10min	30sec	检查预期告警是否上报	${GNODEB}	198092292
	#恢复环境						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.2-5.1.2				
	sleep	6min					
	#修改光接口连线						
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	1	
	更新无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP8}	OF6
	创建无线接口连线_多模	${VBP8}	OF6	${PB30}	OF2	1	
	更新无线接口连线_多模	${VBP8}	OF6	${PB30}	OF2	${VBP8}	OF1
	同步规划区数据_多模	${GNODEB}					
	sleep	3min					
	run keyword and ignore error	Wait Until Keyword Succeeds	10min	30sec	检查预期告警未上报	${GNODEB}	198092292
	run keyword and ignore error	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-5623410 [Qcell] 场景7：第1级PB1125H跨板成对接错，正常配置改配__RAN-5623410	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru101}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru301}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	${nrCells}	获取NR小区别名_多模	${ENODEB}				
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	创建无线接口连线_多模	${VBP8}	OF6	${PB30}	OF2	0	
	修改无线接口连线_多模	${VBP8}	OF6	${PB30}	OF2	${VBP4}	OF2
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.2.8-5.1.2				
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	run keyword and ignore error	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}
	#成对接错						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	1.4.4				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.5				
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	run keyword and ignore error	Wait Until Keyword Succeeds	10min	30sec	检查预期告警是否上报	${GNODEB}	198092292
	#恢复正常						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	1.4.4				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	run keyword and ignore error	Wait Until Keyword Succeeds	10min	30sec	检查预期告警未上报	${GNODEB}	198092292
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-6013622 [大连达沃斯] VBPe2单板满配出光口，PB单上联和PB1125H双上联负荷分担混配，各制式小区分别配置6CP超级小区__RAN-6013622	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru101}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru301}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	#双上联修改单上联						
	@{pbAlias}	create list	PB_30-instance	PB_31-instance	PB_10-instance	PB_11-instance	
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${ENODEB}					
	#PB10创建双上联						
	创建双上联初始配置_多模	${VBP8}	OF2				
	sleep	5					
	#创建双上联						
	@{pbAlias}	create list	PB_10-instance	PB_11-instance			
	: FOR	${board}	IN	@{pbAlias}			
	\	run keyword and ignore error	修改PB功能模式_多模	${board}	0	255	
	\	run keyword and ignore error	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	2
	run keyword and ignore error	删除组网配置_多模	${ENODEB}	2	False		
	@{pbAlias}	create list	PB_30-instance	PB_31-instance			
	: FOR	${board}	IN	@{pbAlias}			
	\	run keyword and ignore error	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0
	run keyword and ignore error	创建双上联无线接口连线_多模	${VBP8}	OF9	PB_10-instance	OF2	${False}
	run keyword and ignore error	创建双上联无线接口连线_多模	PB_10-instance	OF3	PB_11-instance	OF2	${False}
	sleep	6min					
	同步规划区数据_多模	${ENODEB}					
	#创建小区						
	${PRRUS1}	create list	301-instance	302-instance			
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS1}	100
	...	0	9,10,11,12	${True}	${False}		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.5W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru101}		
	\	创建Qcell多模小区_多模	${PRRUS1}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	#PB10双上联						
	${PRRUS2}	create list	101-instance	102-instance	111-instance	112-instance	
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	${radioStrings}	create list	(1V*4T4R*100M*0.5W+1V*4T4R*60M*0.3W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G			
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	101-instance		
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	30sec	验证FDD小区_多模	${ENODEB}		
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	#配置2CP超级小区						
	${subCellLocalIdList}	create list	2				
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	1	${subCellLocalIdList}		
	${subCellLocalIdList}	create list	4				
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	3	${subCellLocalIdList}		
	${subCellLocalIdList}	create list	2				
	run keyword and ignore error	组合LTE超级小区_多模	${GNODEB}	1	${subCellLocalIdList}	fdd	
	sleep	5min					
	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}		
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-6026905 [大连达沃斯]单基带板对接PB1125H双上联，4.9G载波配置6CP超级小区__RAN-6026905	删除并释放无线资源_多模	${ENODEB}					
	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru101}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru301}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	#配置6CP超级小区						
	${radioStrings}	create list	(1V*4T4R*100M*0.5W)@4.9G				
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	101-instance		
	\	创建Qcell多模小区_多模	101-instance	${freqDict}			
	\	创建Qcell多模小区_多模	102-instance	${freqDict}			
	\	创建Qcell多模小区_多模	111-instance	${freqDict}			
	\	创建Qcell多模小区_多模	112-instance	${freqDict}			
	\	创建Qcell多模小区_多模	301-instance	${freqDict}			
	\	创建Qcell多模小区_多模	302-instance	${freqDict}			
	sleep	10min					
	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#创建超级小区						
	${subCellLocalIdList}	create list	2	3	4	5	6
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	1	${subCellLocalIdList}		
	sleep	6min					
	run keyword and ignore error	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}	
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复光交换机到初始配置					
							
RAN-6012548 [大连达沃斯]单基带板对接PB1125H双上联，各制式小区分别配置4CP超级小区__RAN-6012548	删除并释放无线资源_多模	${ENODEB}					
	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${PRRUS1}	create list	101-instance	102-instance			
	${PRRUS2}	create list	111-instance	112-instance			
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS1}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.5W+1V*4T4R*60M*0.3W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS1}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	sleep	6min					
	同步规划区数据_多模	${GNODEB}					
	run keyword and ignore error	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}	
	run keyword and ignore error	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}	
	#PB30链路						
	${PRRUS1}	create list	301-instance	311-instance			
	${PRRUS2}	create list	302-instance	312-instance			
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS1}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.5W+1V*4T4R*60M*0.3W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS1}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	sleep	6min					
	同步规划区数据_多模	${GNODEB}					
	run keyword and ignore error	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}	
	run keyword and ignore error	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}	
	#创建超级小区2.6G/4.9G/1.8G						
	${subCellLocalIdList}	create list	2	3	4		
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	1	${subCellLocalIdList}		
	${subCellLocalIdList}	create list	6	7	8		
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	5	${subCellLocalIdList}		
	${subCellLocalIdList}	create list	2	3	4		
	run keyword and ignore error	组合LTE超级小区_多模	${GNODEB}	1	${subCellLocalIdList}	fdd	
	sleep	10min					
	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}		
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
RAN-6026706 [大连达沃斯]单基带板对接PB1125H双上联，无4.9G载波配置__RAN-6026706	删除并释放无线资源_多模	${ENODEB}					
	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	#PB10链路						
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${PRRUS1}	create list	101-instance	102-instance	111-instance	112-instance	
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.5W+1V*4T4R*60M*0.3W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS1}	${freqDict}			
	#PB30链路						
	${PRRUS2}	create list	301-instance	311-instance			
	${PRRUS3}	create list	302-instance	312-instance			
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.5W+1V*4T4R*60M*0.3W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	sleep	6min					
	同步规划区数据_多模	${GNODEB}					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	#创建超级小区2.6G/1.8G						
	${subCellLocalIdList}	create list	2				
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	1	${subCellLocalIdList}		
	${subCellLocalIdList}	create list	5				
	run keyword and ignore error	组合NR超级小区_多模	${GNODEB}	4	${subCellLocalIdList}		
	sleep	10min					
	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	30sec	验证FDD小区_多模	${ENODEB}		
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#业务验证						
	验证NR小区	nrCell1	${CPE}	${PDN}			
	验证TDL小区业务	fddCell-1	${CPE3}	${PDN}			
	动态创建Qcell-UMTS小区_多模						
	[Teardown]	run keywords	删除并释放无线资源_多模	${ENODEB}			
	...	AND	恢复光交换机到初始配置				
							
恢复光交换机到初始配置	恢复光交换机到初始配置						
	[Teardown]						
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	创建光交换机_多模	${FIBERCONNECT}					
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	Comment	导入基站数据_多模	${GNODEB}	${XML_PATH}			
	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
修改光口速率_5818	[Arguments]	${gnbAlias}	${boardMoId}	${riport}	${value}		
	[Documentation]	[功能说明]：					
	...	\ \ 修改光口速率，对应网管配置riport->bitRateOnIrLine					
	...	[入参]：					
	...	1、${boardMoId}：单板的moId，如VBP_1_3，51，PB_1；					
	...	2、${riport}：单板光口号，如OF1，OPT1，ETH1。					
	...	3、${value}:光口速率					
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=RiPort				
	${attrDict}	create dictionary	bitRateOnIrLine	${value}			
	${keyMoPathDict}	create dictionary	ReplaceableUnit	${boardMoId}	RiPort	${riport}	
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}	${True}	plan			
							
恢复光交换机到初始配置	${vbpConnectedPortList}	create list	2.6.1	2.6.2	2.6.3	2.6.6	5.1.7
	...	5.1.8	5.1.3	5.1.4	1.4.4	1.2.8	
	删除当前环境所有光口的连接	${FIBERCONNECT}	${vbpConnectedPortList}				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.1-5.1.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.2-5.1.2				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.6-5.1.6				
	导入基站数据_多模	${ENODEB}	${XML_PATH}				
							
删除当前环境所有光口的连接	[Arguments]	${fiberAlias}	${portList}				
	: FOR	${port}	IN	@{portList}			
	\	删除某个端口的光交换连接_多模	${fiberAlias}	${port}			
							
恢复环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	240					
							
VBP_8_OF1	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${pbAliasList}	创建VBP特定光口下的多级PB级联_多模	${ENODEB}	${VBP8}	OF1	PB1125H	4
	...	${dictPb}					
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruList}	create list					
	${prruList1}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[0]	R8159 *********	${dictPrru}	6
	${prruList2}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[1]	R8159 *********	${dictPrru}	6
	${prruList3}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[2]	R8159 *********	${dictPrru}	8
	${prruList4}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[3]	R8159 *********	${dictPrru}	8
	[Return]	${prruList1}	${prruList2}	${prruList3}	${prruList4}		
							
VBP光口下配PB、PRRU	[Arguments]	${PORT}	${scaseNumb}	${prruNumb}			
	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${pbAliasList}	创建VBP特定光口下的多级PB级联_多模	${ENODEB}	${VBP8}	${PORT}	PB1125H	${scaseNumb}
	...	${dictPb}					
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruList}	create list					
	: FOR	${pbAlias}	IN	@{pbAliasList}			
	\	${prruAliasList}	创建指定PB下的多个PRRU_多模	${ENODEB}	${pbAlias}	R8159 *********	${dictPrru}
	...	${prruNumb}					
	\	${prruList}	Combine Lists	${prruList}	${prruAliasList}		
	log	${prruList}					
	[Return]	${prruList}					
							
获取VBP特定光口上的PRRUS	[Arguments]	${VBP}	${port}				
	${prrus}	create list					
	${pbAliases}	获取VBP特定光口下的PB别名_多模	${ENODEB}	${VBP}	${port}		
	: FOR	${pbAliase}	IN	@{pbAliases}			
	\	${tmp}	根据pb单板别名获取其上的prrus_多模	${pbAliase}			
	\	${prrus}	Combine Lists	${prrus}	${tmp}		
	[Return]	${prrus}					
							
获取VBP特定光口上的PB	[Arguments]	${VBP}	${port}				
	${pbAliases}	获取VBP特定光口下的PB别名_多模	${ENODEB}	${VBP}	${port}		
	[Return]	${pbAliases}					
							
基于pb的位置创建双上联	[Arguments]	${pbPosition}	${VBP}				
	@{pbAlias}	获取VBP特定光口下的PB别名_多模	${ENODEB}	${VBP}	OF2		
	${vbpOfTopo}	set variable	@{pbAlias}				
	${up}	set variable	OF2				
	${down}	set variable	OF2				
	${pbToDes}	Get Slice From List	${vbpOfTopo}	${pbPosition}			
	Reverse List	${pbToDes}					
	: FOR	${i}	IN RANGE	${pbPosition}			
	\	${up}	set variable if	'@{vbpOfTopo}[${i}]' == '${VBP}'	OF2	OF3	
	\	run keyword and ignore error	创建双上联无线接口连线_多模	@{vbpOfTopo}[${i}]	${up}	@{pbAlias}[${i}]	${down}
	${up}	set variable	OF2				
	: FOR	${pb}	IN	@{pbToDes}			
	\	${up}	set variable if	'${pb}' == '${VBP8}'	OF2	OF3	
	\	run keyword and ignore error	删除无线接口连线_多模	${pb}	${up}		
