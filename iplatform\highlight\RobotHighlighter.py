# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     RobotHighlighter
   Description :   RobotFramework语法高亮器
   Author :       Assistant
   date：          2025/01/23
-------------------------------------------------
   Change Activity:
                   2025/01/23: 创建RobotFramework语法高亮器
-------------------------------------------------
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor
from PyQt5.Qsci import QsciLexerCustom, QsciScintilla
from settings.UserSettings import UserSettings
import re


class RobotHighlighter(QsciLexerCustom):
    # 定义样式ID
    Default = 0
    Section = 1
    Keyword = 2
    Variable = 3
    Comment = 4
    TestCase = 5
    Setting = 6
    Number = 7
    String = 8
    BuiltinKeyword = 9
    Tag = 10

    def __init__(self, parent=None):
        super(RobotHighlighter, self).__init__(parent)

        # 设置默认字体
        font = QFont()
        font.setStyleName('Normal')
        font.setPointSize(UserSettings().get_value("TEXT_EDIT_FONT_SIZE"))
        font.setFamily(UserSettings().get_value("FONT"))
        self.setDefaultFont(font)

        # 设置各种样式的颜色
        self.setColor(QColor("#000000"), self.Default)  # 黑色 - 默认文本
        self.setColor(QColor("#0000FF"), self.Section)  # 蓝色 - 部分标题
        self.setColor(QColor("#800080"), self.Keyword)  # 紫色 - 关键字
        self.setColor(QColor("#FF8C00"), self.Variable)  # 深橙色 - 变量
        self.setColor(QColor("#008000"), self.Comment)  # 绿色 - 注释
        self.setColor(QColor("#8B0000"), self.TestCase)  # 深红色 - 测试用例名
        self.setColor(QColor("#4B0082"), self.Setting)  # 靛蓝色 - 设置项
        self.setColor(QColor("#FF00FF"), self.Number)  # 洋红色 - 数字
        self.setColor(QColor("#DC143C"), self.String)  # 深红色 - 字符串
        self.setColor(QColor("#0000CD"), self.BuiltinKeyword)  # 中蓝色 - 内置关键字
        self.setColor(QColor("#006400"), self.Tag)  # 深绿色 - 标签

        # 设置字体样式
        self.setFont(font, self.Default)
        bold_font = QFont(font)
        bold_font.setBold(True)
        self.setFont(bold_font, self.Section)
        self.setFont(bold_font, self.TestCase)
        self.setFont(bold_font, self.Keyword)

        # 定义内置关键字
        self.builtin_keywords = [
            # BuiltIn库关键字
            'log', 'sleep', 'should be true', 'should be false',
            'should be equal', 'should not be equal', 'should contain',
            'should not contain', 'run keyword', 'run keyword if',
            'run keyword and return status', 'set variable', 'get variable value',
            'create list', 'create dictionary', 'append to list',
            'get from dictionary', 'set to dictionary', 'comment',
            'pass execution', 'fail', 'fatal error', 'import library',
            'import resource', 'import variables', 'set test variable',
            'set suite variable', 'set global variable', 'get time',
            'evaluate', 'call method', 'convert to integer', 'convert to string',
            'should be empty', 'should not be empty', 'length should be',
            'should match', 'should not match', 'should match regexp',
            'should not match regexp', 'get length', 'count values in list',
            'get from list', 'get index from list', 'copy list',
            'reverse list', 'sort list', 'combine lists', 'remove from list',
            'remove duplicates', 'get dictionary keys', 'get dictionary values',
            'dictionary should contain key', 'dictionary should not contain key',
            'lists should be equal', 'dictionaries should be equal',

            # 控制流关键字
            'for', 'in range', 'in', 'end', 'if', 'else', 'else if', 'while', 'continue for loop',
            'exit for loop', 'return from keyword', 'return from keyword if',

            # 测试用例和套件相关
            'setup', 'teardown', 'test setup', 'test teardown', 'suite setup',
            'suite teardown', 'test timeout', 'suite timeout',

            # 变量和参数
            'set test documentation', 'set suite documentation', 'set test metadata',
            'set suite metadata', 'get test documentation', 'get suite documentation',

            # 字符串操作
            'should start with', 'should end with', 'should not start with',
            'should not end with', 'get substring', 'replace string', 'split string',
            'strip string', 'convert to lowercase', 'convert to uppercase',

            # 数值操作
            'should be greater than', 'should be less than', 'should be greater than or equal',
            'should be less than or equal', 'convert to number', 'convert to binary',
            'convert to hex', 'convert to octal',

            # 文件和目录操作
            'file should exist', 'file should not exist', 'directory should exist',
            'directory should not exist', 'create file', 'remove file', 'copy file',
            'move file', 'get file size', 'get file', 'list directory',

            # 时间相关
            'get current date', 'add time to date', 'subtract time from date',
            'convert date', 'convert time',

            # 其他常用关键字
            'no operation', 'repeat keyword', 'wait until keyword succeeds',
            'run keyword and ignore error', 'run keyword and expect error',
            'run keyword and continue on failure', 'run keywords'
        ]

        # 编译正则表达式以提高性能
        # 使用更全面的正则表达式匹配中文
        self.section_pattern = re.compile(r'^\*{3}\s*[\w\u4e00-\u9fa5\s]*\*{3}', re.IGNORECASE | re.UNICODE)  # 匹配含中文的章节头
        self.variable_pattern = re.compile(r'\$\{[^}]*\}|@\{[^}]*\}|&\{[^}]*\}|%\{[^}]*\}', re.UNICODE)  # 非贪婪匹配包含中文的变量
        self.setting_pattern = re.compile(r'\[[\u4e00-\u9fa5A-Za-z\s]+\]', re.IGNORECASE | re.UNICODE)  # 支持中文设置项
        self.number_pattern = re.compile(r'(?<!\$)\b\d+\.?\d*\b', re.UNICODE)  # 排除变量中的数字
        self.string_pattern = re.compile(r'["“](.*?)["”]|[\'‘](.*?)[\'’]', re.UNICODE)  # 支持中文引号
        # 改进的中文注释正则表达式，支持：
        # 1. 行首任意空白+#号
        # 2. Comment关键字后跟中文
        # 3. 中文注释内容
        self.chinese_comment_pattern = re.compile(
            r'^([\s\t]*#.*|Comment\s+.*|#.*[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]+.*)',
            re.UNICODE
        )

    def language(self):
        return "RobotFramework"

    def description(self, style):
        descriptions = {
            self.Default: "Default",
            self.Section: "Section",
            self.Keyword: "Keyword",
            self.Variable: "Variable",
            self.Comment: "Comment",
            self.TestCase: "TestCase",
            self.Setting: "Setting",
            self.Number: "Number",
            self.String: "String",
            self.BuiltinKeyword: "BuiltinKeyword",
            self.Tag: "Tag"
        }
        return descriptions.get(style, "")

    def styleText(self, start, end):
        # 获取要处理的文本
        editor = self.parent()
        if not editor:
            return

        # 获取文本内容
        text = editor.text()

        # 将文本按行分割
        lines = text.split('\n')

        # 计算起始行和结束行
        text_before_start = text[:start]
        text_before_end = text[:end]
        start_line = text_before_start.count('\n')
        end_line = text_before_end.count('\n')

        # 计算起始行在文档中的位置
        if start_line == 0:
            line_start_in_doc = 0
        else:
            line_start_in_doc = len('\n'.join(lines[:start_line])) + 1

        # 逐行处理
        in_documentation = False
        current_pos = line_start_in_doc

        for line_num in range(start_line, min(end_line + 1, len(lines))):
            line = lines[line_num]
            line_start_pos = current_pos
            line_length = len(line)

            # 跳过空行
            if not line.strip():
                self.startStyling(line_start_pos)
                self.setStyling(line_length, self.Default)
                current_pos += line_length + 1  # +1 for newline
                continue

            # 检查是否是章节标题
            section_match = self.section_pattern.match(line)
            if section_match:
                self.startStyling(line_start_pos)
                self.setStyling(line_length, self.Section)
            # 检查是否是注释行
            elif self.chinese_comment_pattern.match(line):
                comment_start = line.find('#') if '#' in line else line.find('Comment')
                if comment_start >= 0:
                    self.startStyling(line_start_pos)
                    self.setStyling(comment_start, self.Default)  # 行首空格保持默认
                    self.startStyling(line_start_pos + comment_start)
                    self.setStyling(line_length - comment_start, self.Comment)
                else:
                    self.startStyling(line_start_pos)
                    self.setStyling(line_length, self.Comment)
            # 处理文档字符串
            elif line.strip().startswith('[Documentation]'):
                in_documentation = True
                doc_start = line.find('[Documentation]')
                self.startStyling(line_start_pos)
                self.setStyling(doc_start, self.Default)
                self.startStyling(line_start_pos + doc_start)
                self.setStyling(len('[Documentation]'), self.Setting)
                self.startStyling(line_start_pos + doc_start + len('[Documentation]'))
                self.setStyling(line_length - doc_start - len('[Documentation]'), self.String)
            elif in_documentation:
                # 文档字符串延续行
                self.startStyling(line_start_pos)
                self.setStyling(line_length, self.String)
                if not line.strip().endswith('\\'):
                    in_documentation = False
            # 检查是否是测试用例名（不以空格或制表符开头的行）
            elif not line.startswith((' ', '\t')) and not line.startswith('['):
                self.startStyling(line_start_pos)
                self.setStyling(line_length, self.TestCase)
            else:
                # 处理其他行内容（关键字、变量、设置等）
                self._style_line_content(line, line_start_pos)

            # 移动到下一行
            current_pos += line_length + 1  # +1 for newline

    def _style_line_content(self, line, line_start):
        """处理行内的各种语法元素"""
        # 首先设置整行为默认样式
        self.startStyling(line_start)
        self.setStyling(len(line), self.Default)

        # 创建一个样式映射列表，按优先级排序
        style_ranges = []

        # 1. 处理设置项 [Setting]
        for match in self.setting_pattern.finditer(line):
            style_ranges.append((match.start(), match.end(), self.Setting))

            # 特别处理[Tags]
            if 'Tags' in match.group() or '标签' in match.group():
                # 查找标签内容
                tag_content = line[match.end():].strip()
                if tag_content:
                    style_ranges.append((match.end(), len(line), self.Tag))

        # 2. 处理变量 ${var}, @{list}, &{dict}
        for match in self.variable_pattern.finditer(line):
            style_ranges.append((match.start(), match.end(), self.Variable))

        # 3. 处理字符串
        for match in self.string_pattern.finditer(line):
            style_ranges.append((match.start(), match.end(), self.String))

        # 4. 处理数字
        for match in self.number_pattern.finditer(line):
            # 确保数字不在变量或字符串内
            if not self._is_in_variable_or_string(line, match.start(), match.end()):
                style_ranges.append((match.start(), match.end(), self.Number))

        # 5. 处理内置关键字
        for keyword in self.builtin_keywords:
            keyword_lower = keyword.lower()
            line_lower = line.lower()

            start_pos = 0
            while True:
                pos = line_lower.find(keyword_lower, start_pos)
                if pos == -1:
                    break

                # 检查关键字边界
                if self._is_keyword_boundary(line, pos, pos + len(keyword)):
                    # 确保不在变量或字符串内
                    if not self._is_in_variable_or_string(line, pos, pos + len(keyword)):
                        style_ranges.append((pos, pos + len(keyword), self.BuiltinKeyword))

                start_pos = pos + 1

        # 6. 处理用户定义的关键字（以空格或制表符开头的行，第一个非空白词）
        stripped = line.lstrip()
        if stripped and line.startswith((' ', '\t')):
            # 找到第一个词（关键字）
            words = stripped.split()
            if words:
                first_word = words[0]
                # 如果不是设置项、变量或内置关键字，则可能是用户关键字
                if (not first_word.startswith('[') and
                    not first_word.startswith(('${', '@{', '&{')) and
                    first_word.lower() not in [kw.lower() for kw in self.builtin_keywords]):

                    # 找到关键字在原行中的位置
                    keyword_start = line.find(first_word)
                    if keyword_start >= 0:
                        style_ranges.append((keyword_start, keyword_start + len(first_word), self.Keyword))

        # 按起始位置排序并应用样式
        style_ranges.sort(key=lambda x: x[0])

        # 应用所有样式
        for start_pos, end_pos, style in style_ranges:
            print('start_pos, end_pos, style', start_pos, end_pos, style)
            self.startStyling(line_start + start_pos)
            self.setStyling(end_pos - start_pos, style)



    def _is_keyword_boundary(self, line, start, end):
        """检查是否是有效的关键字边界"""
        # 检查前面的字符
        if start > 0:
            prev_char = line[start - 1]
            if not (prev_char in ' \t' or prev_char == ''):
                return False

        # 检查后面的字符
        if end < len(line):
            next_char = line[end]
            if not (next_char in ' \t\n' or next_char == ''):
                return False

        return True

    def _is_in_variable_or_string(self, line, start, end):
        """检查给定位置是否在变量或字符串内"""
        # 检查是否在变量内
        for match in self.variable_pattern.finditer(line):
            if match.start() <= start < match.end() or match.start() < end <= match.end():
                return True

        # 检查是否在字符串内
        for match in self.string_pattern.finditer(line):
            if match.start() <= start < match.end() or match.start() < end <= match.end():
                return True

        return False
