# 库关键字语法高亮实现说明

## 功能概述

成功实现了库关键字的语法高亮功能，参考编辑页签的染色方案，将在库中找到的自定义关键字染色为淡蓝色 `RGB(25, 105, 225)`，与编辑页签保持一致。

## 实现特点

### 1. 新增样式类型

#### LibraryKeyword 样式
```python
class RobotHighlighter(QsciLexerCustom):
    # 定义样式ID
    LibraryKeyword = 11  # 新增：库关键字样式
    
    # 设置颜色（与编辑页签一致）
    self.setColor(QColor(25, 105, 225), self.LibraryKeyword)  # 淡蓝色
```

### 2. 库关键字检查机制

#### 集成现有仓库系统
```python
def _is_library_keyword(self, keyword_name):
    """检查是否是库关键字"""
    try:
        # 检查内置库关键字
        builtin_result = BuildInKeyWordRepository().query(keyword_name)
        if builtin_result and builtin_result.get(keyword_name):
            return True
        
        # 检查本地库关键字（包括导入的库）
        local_result = LocalKeyWordRepository().query(keyword_name)
        if local_result and local_result.get(keyword_name):
            return True
            
        return False
    except Exception:
        return False
```

#### 仓库系统说明
- **BuildInKeyWordRepository**: 存储 Robot Framework 内置库关键字
- **LocalKeyWordRepository**: 存储项目中导入的库关键字和用户定义的关键字
- **查询机制**: 与编辑页签使用相同的查询逻辑

### 3. 关键字分类和染色

#### 三种关键字类型
| 关键字类型 | 颜色 | RGB值 | 示例 |
|------------|------|-------|------|
| **内置关键字** | 中蓝色 | `#0000CD` | `Log`, `Set Variable`, `Should Be Equal` |
| **库关键字** | 淡蓝色 | `RGB(25, 105, 225)` | `Open Browser`, `Get Length`, `Create List` |
| **用户关键字** | 紫色 | `#800080` | 用户自定义的关键字 |

#### 优先级处理
```python
# 6. 处理用户定义的关键字和库关键字
if stripped and line.startswith((' ', '\t')):
    words = stripped.split()
    if words:
        first_word = words[0]
        # 检查是否是库关键字
        if self._is_library_keyword(first_word):
            style_ranges.append((keyword_start, keyword_start + len(first_word), self.LibraryKeyword))
        else:
            # 默认作为用户关键字
            style_ranges.append((keyword_start, keyword_start + len(first_word), self.Keyword))
```

### 4. 支持的库类型

#### 常见 Robot Framework 库
- **SeleniumLibrary**: Web 自动化测试
  - `Open Browser`, `Click Element`, `Input Text`, `Get Title`
- **Collections**: 集合操作
  - `Create List`, `Append To List`, `Get From Dictionary`
- **String**: 字符串操作
  - `Convert To Uppercase`, `Should Start With`, `Replace String`
- **DateTime**: 日期时间操作
  - `Get Current Date`, `Add Time To Date`, `Convert Date`
- **OperatingSystem**: 操作系统交互
  - `Create File`, `Directory Should Exist`, `Get Environment Variable`
- **RequestsLibrary**: HTTP 请求
  - `Create Session`, `GET On Session`, `POST On Session`

#### 自定义库支持
- 项目中导入的自定义 Python 库
- 通过 `Library` 关键字导入的第三方库
- 动态加载的库关键字

### 5. 与编辑页签的一致性

#### 颜色方案统一
```python
# 编辑页签颜色定义（SystemConfig.py）
KEYWORD = [25, 105, 225]  # 淡蓝色

# 语法高亮器颜色设置
self.setColor(QColor(25, 105, 225), self.LibraryKeyword)  # 完全一致
```

#### 检查逻辑统一
```python
# 编辑页签检查逻辑（Colorizer.py）
def _is_keyword(self, cell):
    if LocalKeyWordRepository().query(cell).get(cell):
        return True

# 语法高亮器检查逻辑（RobotHighlighter.py）
def _is_library_keyword(self, keyword_name):
    local_result = LocalKeyWordRepository().query(keyword_name)
    if local_result and local_result.get(keyword_name):
        return True
```

## 技术实现

### 1. 导入依赖
```python
from model.data_file.Repository import BuildInKeyWordRepository, LocalKeyWordRepository
```

### 2. 样式定义
```python
# 新增样式ID
LibraryKeyword = 11

# 设置颜色
self.setColor(QColor(25, 105, 225), self.LibraryKeyword)
```

### 3. 关键字检查
```python
def _is_library_keyword(self, keyword_name):
    # 双重检查：内置库 + 本地库
    # 异常处理：确保稳定性
```

### 4. 样式应用
```python
# 在关键字处理逻辑中
if self._is_library_keyword(first_word):
    style_ranges.append((keyword_start, keyword_start + len(first_word), self.LibraryKeyword))
```

## 测试验证

### 1. 测试文件
创建了 `test_library_keyword_highlight.robot` 包含：
- **SeleniumLibrary 关键字**: `Open Browser`, `Click Element`, `Input Text`
- **Collections 关键字**: `Create List`, `Append To List`, `Get Length`
- **String 关键字**: `Convert To Uppercase`, `Should Start With`
- **DateTime 关键字**: `Get Current Date`, `Add Time To Date`
- **OperatingSystem 关键字**: `Create File`, `Directory Should Exist`
- **RequestsLibrary 关键字**: `Create Session`, `GET On Session`

### 2. 验证要点
- ✅ **库关键字**: 显示为淡蓝色 `RGB(25, 105, 225)`
- ✅ **内置关键字**: 显示为中蓝色 `#0000CD`
- ✅ **用户关键字**: 显示为紫色 `#800080`
- ✅ **颜色一致性**: 与编辑页签完全一致
- ✅ **中文支持**: 正确处理中文关键字名称

### 3. 混合场景测试
```robot
测试混合关键字类型
    # 内置关键字（中蓝色）
    Log    这是内置关键字
    
    # 库关键字（淡蓝色）
    Open Browser    about:blank    chrome
    
    # 用户关键字（紫色）
    自定义关键字示例
```

## 性能考虑

### 1. 查询优化
- **异常处理**: 避免查询失败导致的性能问题
- **缓存机制**: 利用仓库系统的内置缓存
- **快速返回**: 查询失败时立即返回 False

### 2. 冲突避免
```python
# 检查是否已经被其他样式覆盖
already_styled = False
for start_pos, end_pos, _ in style_ranges:
    if start_pos <= keyword_start < end_pos:
        already_styled = True
        break

if not already_styled:
    # 应用库关键字样式
```

### 3. 优先级处理
1. **设置项**: `[Documentation]`, `[Tags]` 等
2. **变量**: `${var}`, `@{list}` 等
3. **字符串**: 引号包围的内容
4. **内置关键字**: Robot Framework 基础关键字
5. **库关键字**: 导入库的关键字
6. **用户关键字**: 自定义关键字

## 使用效果

### 1. 视觉区分
- **一目了然**: 不同类型的关键字有明显的颜色区分
- **层次清晰**: 内置 < 库 < 用户的层次关系
- **一致体验**: 与编辑页签保持完全一致

### 2. 开发效率
- **快速识别**: 立即识别关键字来源
- **减少错误**: 避免关键字类型混淆
- **提升体验**: 更好的代码可读性

### 3. 兼容性
- **向后兼容**: 不影响现有功能
- **扩展性**: 支持新增库的自动识别
- **稳定性**: 异常处理确保不会崩溃

## 总结

成功实现了库关键字的语法高亮功能：

1. **完美集成**: 与现有编辑页签染色方案完全一致
2. **智能识别**: 自动区分内置、库、用户三种关键字类型
3. **性能优化**: 高效的查询机制和异常处理
4. **全面支持**: 支持所有常见的 Robot Framework 库
5. **中文友好**: 完整支持中文关键字和内容

现在用户可以在文本编辑器中享受与编辑页签一致的语法高亮体验，库关键字以淡蓝色显示，提升了代码的可读性和开发效率！🎉
