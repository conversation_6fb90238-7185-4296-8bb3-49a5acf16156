# 蓝色字体和下划线功能说明

## 功能需求

**需求**: 按Ctrl键后，将下划线颜色改成蓝色，关键字的字体也改成蓝色

## 实现方案

### 双指示器方案

使用两个独立的指示器来分别处理字体颜色和下划线：

1. **指示器8**: 负责字体颜色
2. **指示器9**: 负责下划线

### 技术实现

#### 1. 字体颜色指示器

```python
# 指示器1：字体颜色
color_indicator_id = 8
self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, color_indicator_id)

# 尝试使用INDIC_TEXTFORE改变字体颜色（如果支持）
try:
    self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, color_indicator_id, 22)  # INDIC_TEXTFORE = 22
    self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, color_indicator_id, 0x0000FF)  # 蓝色
except:
    # 如果不支持INDIC_TEXTFORE，使用INDIC_ROUNDBOX作为替代
    self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, color_indicator_id, QsciScintilla.INDIC_ROUNDBOX)
    self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, color_indicator_id, 0xE6F3FF)  # 浅蓝色背景
    self.SendScintilla(QsciScintilla.SCI_INDICSETALPHA, color_indicator_id, 100)  # 半透明
```

#### 2. 下划线指示器

```python
# 指示器2：下划线
underline_indicator_id = 9
self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, underline_indicator_id)
self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, underline_indicator_id, QsciScintilla.INDIC_PLAIN)
self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, underline_indicator_id, 0x0000FF)  # 蓝色下划线
```

#### 3. 同时应用两个指示器

```python
# 添加字体颜色指示器
self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, color_indicator_id)
self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_byte_pos, end_byte_pos - start_byte_pos)

# 添加下划线指示器
self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, underline_indicator_id)
self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_byte_pos, end_byte_pos - start_byte_pos)
```

### QsciScintilla指示器类型

#### INDIC_TEXTFORE (22)
- **功能**: 直接改变文本前景色（字体颜色）
- **优点**: 最直接的字体颜色改变方式
- **缺点**: 可能在某些QsciScintilla版本中不支持

#### INDIC_ROUNDBOX (备选方案)
- **功能**: 在文本周围显示圆角矩形背景
- **优点**: 兼容性好，所有版本都支持
- **效果**: 浅蓝色背景高亮，视觉效果明显

#### INDIC_PLAIN
- **功能**: 简单的下划线
- **用途**: 专门用于下划线显示
- **颜色**: 蓝色 (0x0000FF)

### 指示器管理

#### 记录指示器信息
```python
# 记录两个指示器的信息，便于清除
self._underlined_indicators.append({
    'indicator_id': color_indicator_id,
    'start_pos': start_byte_pos,
    'length': end_byte_pos - start_byte_pos
})
self._underlined_indicators.append({
    'indicator_id': underline_indicator_id,
    'start_pos': start_byte_pos,
    'length': end_byte_pos - start_byte_pos
})
```

#### 清除指示器
```python
def _clear_underlines(self):
    """清除所有下划线和字体颜色"""
    try:
        for indicator_info in self._underlined_indicators:
            indicator_id = indicator_info['indicator_id']
            start_pos = indicator_info['start_pos']
            length = indicator_info['length']
            
            self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
            self.SendScintilla(QsciScintilla.SCI_INDICATORCLEARRANGE, start_pos, length)
        
        self._underlined_indicators.clear()
    except Exception as e:
        print(f"清除下划线时出错: {e}")
```

## 视觉效果

### 按住Ctrl键前
- 普通的黑色字体
- 无下划线
- 普通鼠标指针

### 按住Ctrl键后（悬停在可跳转函数上）
- **蓝色字体颜色** (如果支持INDIC_TEXTFORE)
- **浅蓝色背景高亮** (如果使用INDIC_ROUNDBOX备选方案)
- **蓝色下划线**
- **手型鼠标指针**

### 释放Ctrl键后
- 恢复普通黑色字体
- 清除所有下划线和背景
- 恢复普通鼠标指针

## 兼容性处理

### 版本兼容
- **优先使用**: INDIC_TEXTFORE (直接改变字体颜色)
- **备选方案**: INDIC_ROUNDBOX (背景高亮)
- **异常处理**: 完整的try-catch机制

### 错误回退
如果INDIC_TEXTFORE不支持，自动回退到INDIC_ROUNDBOX：
- 浅蓝色背景 (0xE6F3FF)
- 半透明效果 (alpha = 100)
- 保持良好的视觉效果

## 测试验证

### 测试场景
1. **英文函数名**: `test_function()`
2. **中文函数名**: `测试函数()`
3. **混合函数名**: `中英文混合函数()`
4. **带下划线**: `function_with_underscore()`

### 验证要点
1. ✅ 字体颜色变为蓝色（或背景变为浅蓝色）
2. ✅ 下划线显示为蓝色
3. ✅ 下划线完全覆盖函数名（包括中文字符）
4. ✅ 鼠标指针变为手型
5. ✅ 释放Ctrl键后所有效果消失
6. ✅ 点击跳转功能正常工作

## 技术优势

1. **双重视觉反馈**: 字体颜色 + 下划线
2. **兼容性强**: 支持多种QsciScintilla版本
3. **中文支持**: 正确处理中文字符的字节位置
4. **性能优化**: 高效的指示器管理
5. **错误处理**: 完整的异常处理机制
