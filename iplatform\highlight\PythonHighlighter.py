# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     PythonHighlighter
   Description :
   Author :       10140129
   date：          2019/10/24
-------------------------------------------------
   Change Activity:
                   2019/10/24:
-------------------------------------------------
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from settings.UserSettings import UserSettings
from PyQt5.Qsci import QsciLexerPython
class PythonHighlighter(QsciLexerPython):

    def __init__(self,parent):
        QsciLexerPython.__init__(self,parent)
        self.setColor(Qt.darkBlue, QsciLexerPython.ClassName)
        self.setColor(Qt.darkBlue, QsciLexerPython.Keyword)
        self.setColor(Qt.darkGreen, QsciLexerPython.Comment)
        self.setColor(Qt.darkMagenta, QsciLexerPython.Number)
        self.setColor(Qt.black, QsciLexerPython.FunctionMethodName)
        font=QFont()
        font.setStyleName('Normal')
        font.setPointSize(UserSettings().get_value("TEXT_EDIT_FONT_SIZE"))
        font.setFamily((UserSettings().get_value("FONT")))
        self.setFont(font)

