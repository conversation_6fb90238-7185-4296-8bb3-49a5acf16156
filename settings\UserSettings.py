# encoding=utf-8
'''
Create on  2019年10月14日

@author:10240349
'''
from PyQt5.QtCore import QSettings

from settings.SettingRepository import SettingRepository
from settings.SystemSettings import SystemSettings, SYSTEM_ROOT_DIR, CONFIG_DIR
from utility.Singleton import Singleton


PLUGIN_CONFIG = "PLUGIN"


@Singleton
class UserSettings(object):

    def __init__(self):
        self._load_plugins = self.get_value(PLUGIN_CONFIG)

    def get_value(self, key, get_value_from_system_when_fail=True):
        if SettingRepository().find(key):
            return SettingRepository().find(key)
        settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
        value = settings.value(key)
        if not value and get_value_from_system_when_fail:
            value = self._get_value_from_system_settings(key)
        SettingRepository().add(key, value)
        return value

    @staticmethod
    def _get_value_from_system_settings(key):
        return SystemSettings().get_value(key)

    def add_plugin(self, plugin_name):
        if not plugin_name:
            return
        currentPlugins = UserSettings.get_plugins()
        if plugin_name in currentPlugins:
            return
        if not currentPlugins:
            currentPlugins = plugin_name
        else:
            currentPlugins.append(plugin_name)
        settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
        settings.setValue(PLUGIN_CONFIG, currentPlugins)
        self._load_plugins = currentPlugins if isinstance(currentPlugins, list) else [currentPlugins]
        settings.sync()

    def get_plugins(self):
        if self._load_plugins and isinstance(self._load_plugins, list):
            return self._load_plugins
        settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
        result = settings.value(PLUGIN_CONFIG)
        if not isinstance(result, list):
            result = [] if not result else [result]
        self._load_plugins = result
        return self._load_plugins

    def delete_plugin(self, plugin_name):
        if not plugin_name:
            return
        currentPlugins = UserSettings.get_plugins()
        if plugin_name not in currentPlugins:
            return
        currentPlugins.remove(plugin_name)
        settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
        if not currentPlugins:
            settings.remove(PLUGIN_CONFIG)
            self._load_plugins = []
            settings.sync()
            return
        self._load_plugins = currentPlugins
        settings.setValue(PLUGIN_CONFIG, currentPlugins)
        settings.sync()

    def set_value(self, key, value):
        """设置配置值"""
        try:
            settings = QSettings(CONFIG_DIR, QSettings.IniFormat)
            settings.setValue(key, value)
            settings.sync()
            # 更新内存中的缓存
            SettingRepository().add(key, value)
            return True
        except Exception as e:
            print(f"设置配置值时出错: {e}")
            return False


if __name__ == "__main__":
    # UserSettings.add_plugin("help")
    # print(UserSettings.get_plugins())
    # UserSettings.delete_plugin("help")
    # print(UserSettings.get_plugins())
    print(UserSettings().get_value("Editor/ERROR"))
