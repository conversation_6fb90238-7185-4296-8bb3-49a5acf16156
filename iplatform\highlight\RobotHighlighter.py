# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     RobotHighlighter
   Description :   RobotFramework语法高亮器
   Author :       Assistant
   date：          2025/01/23
-------------------------------------------------
   Change Activity:
                   2025/01/23: 创建RobotFramework语法高亮器
-------------------------------------------------
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor
from PyQt5.Qsci import QsciLexerCustom, QsciScintilla
from settings.UserSettings import UserSettings
from model.data_file.Repository import BuildInKeyWordRepository, LocalKeyWordRepository
import re


class RobotHighlighter(QsciLexerCustom):
    # 定义样式ID
    Default = 0
    Section = 1
    Keyword = 2
    Variable = 3
    Comment = 4
    TestCase = 5
    Setting = 6
    Number = 7
    String = 8
    BuiltinKeyword = 9
    Tag = 10
    LibraryKeyword = 11  # 新增：库关键字样式

    def __init__(self, parent=None):
        super(RobotHighlighter, self).__init__(parent)

        # 设置默认字体
        font = QFont()
        font.setStyleName('Normal')
        font.setPointSize(UserSettings().get_value("TEXT_EDIT_FONT_SIZE"))
        font.setFamily(UserSettings().get_value("FONT"))
        self.setDefaultFont(font)

        # 设置各种样式的颜色
        self.setColor(QColor("#000000"), self.Default)  # 黑色 - 默认文本
        self.setColor(QColor("#0000FF"), self.Section)  # 蓝色 - 部分标题
        self.setColor(QColor("#800080"), self.Keyword)  # 紫色 - 用户关键字
        self.setColor(QColor("#FF8C00"), self.Variable)  # 深橙色 - 变量
        self.setColor(QColor("#008000"), self.Comment)  # 绿色 - 注释
        self.setColor(QColor("#8B0000"), self.TestCase)  # 深红色 - 测试用例名
        self.setColor(QColor("#4B0082"), self.Setting)  # 靛蓝色 - 设置项
        self.setColor(QColor("#FF00FF"), self.Number)  # 洋红色 - 数字
        self.setColor(QColor("#DC143C"), self.String)  # 深红色 - 字符串
        self.setColor(QColor("#0000CD"), self.BuiltinKeyword)  # 中蓝色 - 内置关键字
        self.setColor(QColor("#006400"), self.Tag)  # 深绿色 - 标签
        self.setColor(QColor(25, 105, 225), self.LibraryKeyword)  # 淡蓝色 - 库关键字（与编辑页签一致）

        # 设置字体样式
        self.setFont(font, self.Default)
        bold_font = QFont(font)
        bold_font.setBold(True)
        self.setFont(bold_font, self.Section)
        self.setFont(bold_font, self.TestCase)
        self.setFont(bold_font, self.Keyword)

        # 定义内置关键字
        self.builtin_keywords = [
            # BuiltIn库关键字
            'log', 'sleep', 'should be true', 'should be false',
            'should be equal', 'should not be equal', 'should contain',
            'should not contain', 'run keyword', 'run keyword if',
            'run keyword and return status', 'set variable', 'get variable value',
            'create list', 'create dictionary', 'append to list',
            'get from dictionary', 'set to dictionary', 'comment',
            'pass execution', 'fail', 'fatal error', 'import library',
            'import resource', 'import variables', 'set test variable',
            'set suite variable', 'set global variable', 'get time',
            'evaluate', 'call method', 'convert to integer', 'convert to string',
            'should be empty', 'should not be empty', 'length should be',
            'should match', 'should not match', 'should match regexp',
            'should not match regexp', 'get length', 'count values in list',
            'get from list', 'get index from list', 'copy list',
            'reverse list', 'sort list', 'combine lists', 'remove from list',
            'remove duplicates', 'get dictionary keys', 'get dictionary values',
            'dictionary should contain key', 'dictionary should not contain key',
            'lists should be equal', 'dictionaries should be equal',

            # 控制流关键字
            'for', 'in range', 'in', 'end', 'if', 'else', 'else if', 'while', 'continue for loop',
            'exit for loop', 'return from keyword', 'return from keyword if', ':FOR', ':FOR', ':END', ':IF', ':ELSE', ':ELSE IF', ':WHILE',

            # 测试用例和套件相关
            'setup', 'teardown', 'test setup', 'test teardown', 'suite setup',
            'suite teardown', 'test timeout', 'suite timeout',

            # 变量和参数
            'set test documentation', 'set suite documentation', 'set test metadata',
            'set suite metadata', 'get test documentation', 'get suite documentation',

            # 字符串操作
            'should start with', 'should end with', 'should not start with',
            'should not end with', 'get substring', 'replace string', 'split string',
            'strip string', 'convert to lowercase', 'convert to uppercase',

            # 数值操作
            'should be greater than', 'should be less than', 'should be greater than or equal',
            'should be less than or equal', 'convert to number', 'convert to binary',
            'convert to hex', 'convert to octal',

            # 文件和目录操作
            'file should exist', 'file should not exist', 'directory should exist',
            'directory should not exist', 'create file', 'remove file', 'copy file',
            'move file', 'get file size', 'get file', 'list directory',

            # 时间相关
            'get current date', 'add time to date', 'subtract time from date',
            'convert date', 'convert time',

            # 其他常用关键字
            'no operation', 'repeat keyword', 'wait until keyword succeeds',
            'run keyword and ignore error', 'run keyword and expect error',
            'run keyword and continue on failure', 'run keywords'
        ]

        # 编译正则表达式以提高性能
        # 使用更全面的正则表达式匹配中文
        self.section_pattern = re.compile(r'^\*{3}\s*[\w\u4e00-\u9fa5\s]*\*{3}', re.IGNORECASE | re.UNICODE)  # 匹配含中文的章节头
        self.variable_pattern = re.compile(r'\$\{[^}]*\}|@\{[^}]*\}|&\{[^}]*\}|%\{[^}]*\}', re.UNICODE)  # 非贪婪匹配包含中文的变量
        self.setting_pattern = re.compile(r'\[[\u4e00-\u9fa5A-Za-z\s]+\]', re.IGNORECASE | re.UNICODE)  # 支持中文设置项
        self.number_pattern = re.compile(r'(?<!\$)\b\d+\.?\d*\b', re.UNICODE)  # 排除变量中的数字
        self.string_pattern = re.compile(r'["“](.*?)["”]|[\'‘](.*?)[\'’]', re.UNICODE)  # 支持中文引号
        # 改进的中文注释正则表达式，支持：
        # 1. 行首任意空白+#号
        # 2. Comment关键字后跟中文
        # 3. 中文注释内容
        self.chinese_comment_pattern = re.compile(
            r'^([\s\t]*#.*|Comment\s+.*|#.*[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]+.*)',
            re.UNICODE
        )

    def language(self):
        return "RobotFramework"

    def description(self, style):
        descriptions = {
            self.Default: "Default",
            self.Section: "Section",
            self.Keyword: "Keyword",
            self.Variable: "Variable",
            self.Comment: "Comment",
            self.TestCase: "TestCase",
            self.Setting: "Setting",
            self.Number: "Number",
            self.String: "String",
            self.BuiltinKeyword: "BuiltinKeyword",
            self.Tag: "Tag",
            self.LibraryKeyword: "LibraryKeyword"
        }
        return descriptions.get(style, "")

    def _char_to_byte_pos(self, text, char_pos):
        """将字符位置转换为字节位置（UTF-8编码）- 优化版本"""
        if char_pos <= 0:
            return 0
        if char_pos >= len(text):
            return len(text.encode('utf-8'))
        return len(text[:char_pos].encode('utf-8'))

    def _byte_to_char_pos(self, text, byte_pos):
        """将字节位置转换为字符位置 - 优化版本"""
        if byte_pos <= 0:
            return 0

        # 使用二分查找优化性能
        left, right = 0, len(text)
        while left < right:
            mid = (left + right) // 2
            mid_byte_pos = len(text[:mid].encode('utf-8'))
            if mid_byte_pos < byte_pos:
                left = mid + 1
            else:
                right = mid
        return left

    def _build_byte_char_map(self, text):
        """为长文本构建字节-字符位置映射表"""
        if len(text) < 1000:  # 短文本不需要映射表
            return None

        byte_to_char = {}
        char_to_byte = {}
        byte_pos = 0

        for char_pos, char in enumerate(text):
            char_to_byte[char_pos] = byte_pos
            byte_to_char[byte_pos] = char_pos
            byte_pos += len(char.encode('utf-8'))

        char_to_byte[len(text)] = byte_pos
        return {'byte_to_char': byte_to_char, 'char_to_byte': char_to_byte}

    def styleText(self, start, end):
        # 使用 QsciScintilla 原生方法获取准确的行信息
        editor = self.parent()
        if not editor:
            return

        # 获取完整文档文本
        full_text = editor.text()

        # 获取起始行和结束行
        start_line = editor.SendScintilla(editor.SCI_LINEFROMPOSITION, start)
        end_line = editor.SendScintilla(editor.SCI_LINEFROMPOSITION, end)

        # 限制处理的行数，避免大文件卡死
        max_lines = 100  # 一次最多处理100行
        if end_line - start_line > max_lines:
            end_line = start_line + max_lines

        in_documentation = False

        for line_num in range(start_line, end_line + 1):
            # 使用 QsciScintilla API 获取行的准确位置（字节位置）
            line_start_byte = editor.SendScintilla(editor.SCI_POSITIONFROMLINE, line_num)
            line_end_byte = editor.SendScintilla(editor.SCI_GETLINEENDPOSITION, line_num)

            # 将字节位置转换为字符位置
            line_start_char = self._byte_to_char_pos(full_text, line_start_byte)
            line_end_char = self._byte_to_char_pos(full_text, line_end_byte)

            # 获取行内容（使用字符位置）
            line = full_text[line_start_char:line_end_char]
            line_byte_length = line_end_byte - line_start_byte

            # 跳过空行
            if not line.strip():
                self.startStyling(line_start_byte)
                self.setStyling(line_byte_length, self.Default)
                continue

            # 检查是否是章节标题
            section_match = self.section_pattern.match(line)
            if section_match:
                self.startStyling(line_start_byte)
                self.setStyling(line_byte_length, self.Section)
            # 检查是否是注释行
            elif self.chinese_comment_pattern.match(line):
                comment_start_char = line.find('#') if '#' in line else line.find('Comment')
                if comment_start_char >= 0:
                    comment_start_byte = self._char_to_byte_pos(line, comment_start_char)
                    self.startStyling(line_start_byte)
                    self.setStyling(comment_start_byte, self.Default)  # 行首空格保持默认
                    self.startStyling(line_start_byte + comment_start_byte)
                    self.setStyling(line_byte_length - comment_start_byte, self.Comment)
                else:
                    self.startStyling(line_start_byte)
                    self.setStyling(line_byte_length, self.Comment)
            # 处理文档字符串
            elif line.strip().startswith('[Documentation]'):
                in_documentation = True
                doc_start_char = line.find('[Documentation]')
                doc_start_byte = self._char_to_byte_pos(line, doc_start_char)
                doc_keyword_byte_len = len('[Documentation]'.encode('utf-8'))

                self.startStyling(line_start_byte)
                self.setStyling(doc_start_byte, self.Default)
                self.startStyling(line_start_byte + doc_start_byte)
                self.setStyling(doc_keyword_byte_len, self.Setting)
                self.startStyling(line_start_byte + doc_start_byte + doc_keyword_byte_len)
                self.setStyling(line_byte_length - doc_start_byte - doc_keyword_byte_len, self.String)
            elif in_documentation:
                # 文档字符串延续行
                self.startStyling(line_start_byte)
                self.setStyling(line_byte_length, self.String)
                if not line.strip().endswith('\\'):
                    in_documentation = False
            # 检查是否是测试用例名（不以空格或制表符开头的行）
            elif not line.startswith((' ', '\t')) and not line.startswith('['):
                self.startStyling(line_start_byte)
                self.setStyling(line_byte_length, self.TestCase)
            else:
                # 处理其他行内容（关键字、变量、设置等）
                self._style_line_content(line, line_start_byte)

    def _style_line_content(self, line, line_start_byte):
        """处理行内的各种语法元素"""
        # 首先设置整行为默认样式
        line_byte_length = len(line.encode('utf-8'))
        self.startStyling(line_start_byte)
        self.setStyling(line_byte_length, self.Default)

        # 创建一个样式映射列表，按优先级排序（字符位置）
        style_ranges = []

        # 1. 处理设置项 [Setting]
        for match in self.setting_pattern.finditer(line):
            style_ranges.append((match.start(), match.end(), self.Setting))

            # 特别处理[Tags]
            if 'Tags' in match.group() or '标签' in match.group():
                # 查找标签内容
                tag_content = line[match.end():].strip()
                if tag_content:
                    style_ranges.append((match.end(), len(line), self.Tag))

        # 2. 处理变量 ${var}, @{list}, &{dict} - 同时收集位置信息
        var_ranges = []
        for match in self.variable_pattern.finditer(line):
            var_ranges.append((match.start(), match.end()))
            style_ranges.append((match.start(), match.end(), self.Variable))

        # 3. 处理字符串 - 同时收集位置信息
        str_ranges = []
        for match in self.string_pattern.finditer(line):
            str_ranges.append((match.start(), match.end()))
            style_ranges.append((match.start(), match.end(), self.String))

        # 5. 处理内置关键字 - 使用制表符分割的优化版本
        line_lower = line.lower()

        # 使用制表符分割行内容
        parts = line.split('\t')
        current_pos = 0

        for part in parts:
            part_stripped = part.strip()
            if part_stripped:
                part_lower = part_stripped.lower()

                # 检查这部分是否是内置关键字
                for keyword in self.builtin_keywords:
                    keyword_lower = keyword.lower()
                    if part_lower == keyword_lower:
                        # 找到关键字在原行中的位置
                        keyword_start = line.find(part_stripped, current_pos)
                        if keyword_start >= 0:
                            # 检查是否在变量或字符串内
                            if not self._is_in_ranges(keyword_start, keyword_start + len(part_stripped), var_ranges + str_ranges):
                                style_ranges.append((keyword_start, keyword_start + len(part_stripped), self.BuiltinKeyword))
                        break  # 找到匹配的关键字后跳出循环

            # 移动到下一个部分的位置
            current_pos += len(part) + 1  # +1 for tab character

        # 6. 处理用户定义的关键字和库关键字（以空格或制表符开头的行，第一个非空白词）
        stripped = line.lstrip()
        if stripped and line.startswith((' ', '\t')):
            # 找到第一个词（关键字）
            words = stripped.split()
            if words:
                first_word = words[0]
                # 如果不是设置项、变量或内置关键字
                if (not first_word.startswith('[') and
                    not first_word.startswith(('${', '@{', '&{')) and
                    first_word.lower() not in [kw.lower() for kw in self.builtin_keywords]):

                    # 找到关键字在原行中的位置
                    keyword_start = line.find(first_word)
                    if keyword_start >= 0:
                        # 检查是否已经被其他样式覆盖
                        already_styled = False
                        for start_pos, end_pos, _ in style_ranges:
                            if start_pos <= keyword_start < end_pos:
                                already_styled = True
                                break

                        if not already_styled:
                            # 检查是否是库关键字
                            if self._is_library_keyword(first_word):
                                style_ranges.append((keyword_start, keyword_start + len(first_word), self.LibraryKeyword))
                            else:
                                # 默认作为用户关键字
                                style_ranges.append((keyword_start, keyword_start + len(first_word), self.Keyword))

        # 按起始位置排序并应用样式
        style_ranges.sort(key=lambda x: x[0])

        # 应用所有样式（转换为字节位置）
        for start_char, end_char, style in style_ranges:
            # 将字符位置转换为字节位置
            start_byte = self._char_to_byte_pos(line, start_char)
            end_byte = self._char_to_byte_pos(line, end_char)
            byte_length = end_byte - start_byte
            self.startStyling(line_start_byte + start_byte)
            self.setStyling(byte_length, style)

    def _is_in_ranges(self, start, end, ranges):
        """快速检查位置是否在给定的范围列表内"""
        for range_start, range_end in ranges:
            if range_start <= start < range_end or range_start < end <= range_end:
                return True
        return False

    def _is_keyword_boundary(self, line, start, end):
        """检查是否是有效的关键字边界"""
        # 检查前面的字符
        if start > 0:
            prev_char = line[start - 1]
            if not (prev_char in ' \t' or prev_char == ''):
                return False

        # 检查后面的字符
        if end < len(line):
            next_char = line[end]
            if not (next_char in ' \t\n' or next_char == ''):
                return False

        return True

    def _is_library_keyword(self, keyword_name):
        """检查是否是库关键字（在 BuildInKeyWordRepository 或 LocalKeyWordRepository 中）"""
        try:
            # 检查内置库关键字
            builtin_result = BuildInKeyWordRepository().query(keyword_name)
            if builtin_result and builtin_result.get(keyword_name):
                return True

            # 检查本地库关键字（包括导入的库）
            local_result = LocalKeyWordRepository().query(keyword_name)
            if local_result and local_result.get(keyword_name):
                return True

            return False
        except Exception:
            # 如果查询出错，返回 False
            return False


