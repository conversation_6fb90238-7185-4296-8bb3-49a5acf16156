# 深色主题文本编辑器修复说明

## 问题描述

在深色主题下，文本编辑器的编辑区域仍然显示为白色背景，与整体深色主题不协调，影响用户体验。

## 解决方案

### 1. 更新深色主题QSS文件

在 `resources/qss/dark_theme.qss` 中添加了对 QsciScintilla（代码编辑器）的样式定义：

```css
/* 代码编辑器 QsciScintilla */
QsciScintilla {
    background-color: #2B2B2B;
    color: #E0E0E0;
    border: 1px solid #555555;
    selection-background-color: #4A90E2;
    selection-color: #FFFFFF;
}

/* 代码编辑器滚动条 */
QsciScintilla QScrollBar:vertical {
    background-color: #3C3C3C;
    border: none;
    width: 12px;
}

QsciScintilla QScrollBar::handle:vertical {
    background-color: #666666;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QsciScintilla QScrollBar::handle:vertical:hover {
    background-color: #777777;
}

/* 水平滚动条样式 */
QsciScintilla QScrollBar:horizontal {
    background-color: #3C3C3C;
    border: none;
    height: 12px;
}

QsciScintilla QScrollBar::handle:horizontal {
    background-color: #666666;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QsciScintilla QScrollBar::handle:horizontal:hover {
    background-color: #777777;
}
```

### 2. 增强主题管理器

#### 添加文本编辑器通知功能

在 `ThemeManager.py` 中添加了 `notify_text_editors` 方法：

```python
def notify_text_editors(self, theme_id):
    """通知所有文本编辑器应用主题"""
    try:
        from utility.PluginRepository import PluginRepository
        
        # 1. 查找主要的文本编辑器
        text_editor = PluginRepository().find('TEXT_EDIT')
        if text_editor and hasattr(text_editor, '_apply_theme_from_manager'):
            text_editor._apply_theme_from_manager(theme_id)
        
        # 2. 查找编辑插件控制器中的编辑器
        edit_plugin_controller = PluginRepository().find('edit_plugin_controller')
        if edit_plugin_controller and hasattr(edit_plugin_controller, '_editor'):
            editor = edit_plugin_controller._editor
            if editor and hasattr(editor, '_apply_theme_from_manager'):
                editor._apply_theme_from_manager(theme_id)
            elif editor and hasattr(editor, '_set_theme'):
                editor._set_theme('dark' if self.is_dark_theme(theme_id) else 'white')
                
    except Exception as e:
        print(f"通知文本编辑器应用主题时出错: {e}")
```

#### 集成到主题切换流程

在 `set_theme` 方法中添加了对文本编辑器的通知：

```python
# 通知文本编辑器应用主题
self.notify_text_editors(theme_id)

# 发送主题改变信号
self.theme_changed.emit(theme_id)
```

### 3. 更新文本编辑器

#### 添加主题管理器集成

在 `TextEditor.py` 中添加了以下方法：

```python
def _load_initial_theme(self):
    """加载初始主题"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()
        current_theme = theme_manager.get_current_theme()
        
        # 根据主题管理器的主题设置编辑器主题
        if theme_manager.is_dark_theme(current_theme):
            self._set_theme("dark")
        else:
            self._set_theme("white")
            
    except Exception as e:
        print(f"加载初始主题时出错: {e}")
        # 默认使用白色主题
        self._set_theme("white")

def _apply_theme_from_manager(self, theme_id):
    """从主题管理器接收主题变更通知"""
    try:
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()
        
        # 根据主题ID设置编辑器主题
        if theme_manager.is_dark_theme(theme_id):
            self._set_theme("dark")
        else:
            self._set_theme("white")
            
    except Exception as e:
        print(f"应用主题管理器主题时出错: {e}")
```

#### 初始化时加载主题

在 `__init__` 方法中添加了主题初始化：

```python
# 主题相关初始化
self._current_theme = "white"  # 默认白色主题
self._load_initial_theme()
```

## 实现效果

### 1. 深色主题下的文本编辑器

- **背景色**: 深灰色 (#2B2B2B)
- **文字色**: 浅灰色 (#E0E0E0)
- **选中背景**: 蓝色 (#4A90E2)
- **选中文字**: 白色 (#FFFFFF)
- **滚动条**: 深色风格，与整体主题协调

### 2. 主题切换体验

- **即时生效**: 切换主题时，文本编辑器立即应用新主题
- **自动同步**: 所有打开的文本编辑器都会同步应用新主题
- **状态保持**: 重启应用后，文本编辑器会自动加载保存的主题

### 3. 兼容性

- **向后兼容**: 不影响现有的文本编辑器功能
- **多编辑器支持**: 支持主文本编辑器和编辑插件中的编辑器
- **错误处理**: 完善的异常处理，确保主题切换不会影响编辑功能

## 测试方法

### 1. 基本功能测试

1. **启动应用**
2. **打开一个文件** 在文本编辑器中
3. **切换到深色主题**：
   - 方式一：菜单栏 → 样式 → 深色主题
   - 方式二：工具 → 偏好设置 → Theme → 深色主题
4. **验证效果**：
   - 文本编辑器背景变为深色
   - 文字变为浅色
   - 滚动条变为深色风格

### 2. 多编辑器测试

1. **打开多个文件** 在不同的编辑器中
2. **切换主题**
3. **验证** 所有编辑器都应用了新主题

### 3. 重启测试

1. **切换到深色主题**
2. **重启应用**
3. **验证** 文本编辑器自动加载深色主题

## 技术要点

### 1. QSS样式优先级

- QsciScintilla 的样式需要明确指定，因为它是一个特殊的编辑器组件
- 滚动条样式需要单独定义，确保与主题协调

### 2. 主题通知机制

- 使用插件仓库查找所有相关的编辑器组件
- 通过方法调用的方式通知主题变更，确保及时响应

### 3. 初始化时机

- 在编辑器初始化时就加载主题，确保一致性
- 使用异常处理确保主题加载失败时有合理的默认行为

## 总结

这次修复解决了深色主题下文本编辑器显示不协调的问题，实现了：

- ✅ **完整的深色主题体验**：文本编辑器与整体界面风格一致
- ✅ **智能主题同步**：主题切换时自动通知所有编辑器
- ✅ **良好的用户体验**：即时生效，无需重启
- ✅ **稳定的功能**：完善的错误处理和兼容性

现在用户在深色主题下可以享受完整统一的界面体验，特别适合长时间编程工作！🌙
