# -*- coding: utf-8 -*-
"""
测试Python函数跳转功能的示例文件

使用方法：
1. 在TextEditor中打开此文件
2. 按住Ctrl键，鼠标悬停在函数名上会显示下划线
3. 按住Ctrl键并点击函数名，会跳转到函数定义
4. 跳转后不会选中文字，只是将光标定位到函数定义行

测试场景：
- test_function_1() -> 跳转到第12行
- test_function_2() -> 跳转到第17行
- helper_function() -> 跳转到第22行
- class_method_1() -> 跳转到第32行
- class_method_2() -> 跳转到第37行
"""

def test_function_1():
    """第一个测试函数"""
    print("这是第一个测试函数")
    test_function_2()  # 调用第二个函数
    return "function_1_result"

def test_function_2():
    """第二个测试函数"""
    print("这是第二个测试函数")
    helper_function()  # 调用辅助函数
    return "function_2_result"

def helper_function():
    """辅助函数"""
    print("这是辅助函数")
    return "helper_result"

class TestClass:
    """测试类"""

    def __init__(self):
        """构造函数"""
        self.value = 0

    def class_method_1(self):
        """类方法1"""
        self.class_method_2()  # 调用类方法2
        return self.value

    def class_method_2(self):
        """类方法2"""
        self.value = 42
        helper_function()  # 调用全局函数
        return self.value

def main():
    """主函数"""
    print("开始测试")

    # 调用各种函数来测试跳转功能
    result1 = test_function_1()
    print(f"结果1: {result1}")

    test_obj = TestClass()
    result2 = test_obj.class_method_1()
    print(f"结果2: {result2}")

    print("测试完成")

if __name__ == "__main__":
    main()
