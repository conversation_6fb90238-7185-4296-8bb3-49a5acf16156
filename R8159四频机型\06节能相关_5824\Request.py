dataset = \
{
        'Master':
        {
            'resources':
            {
                'omc': {'reqType': 'UME'},
                'gnb': {'reqType': 'GNODEB'},
                'enb': {'reqType': 'ENODEB'},
                'fddFunction': {'reqType': 'ENBFUNCTION', 'filter': {'isAttributeEqual': ['enbRadioMode', 'FDD']}},
                'tddFunction': {'reqType': 'ENBFUNCTION', 'filter': {'isAttributeEqual': ['enbRadioMode', 'TDD']}},
                'vsw': {'reqType': 'VSW'},
                'cpe': {'reqType': 'UE', 'filter': {'isAttributeEqual': ['ueId', '1']}},
                'cpe2': {'reqType': 'UE', 'filter': {'isAttributeEqual': ['ueId', '2']}},
                'cpe3': {'reqType': 'UE', 'filter': {'isAttributeEqual': ['ueId', '3']}},
                'cpe4': {'reqType': 'UE', 'filter': {'isAttributeEqual': ['ueId', '4']}},
                'pdn': {'reqType': 'PDN'},
                'pc': {'reqType': 'CONTROLPC'},
                'enblinkvsw': {'reqType': 'link', 'nodes': [{'device': 'gnb'}, {'device': 'vsw'}]},
                'cpelinkpc': {'reqType': 'link', 'nodes': [{'device': 'cpe'}, {'device': 'pc'}]},
                'cpelinkpc2': {'reqType': 'link', 'nodes': [{'device': 'cpe2'}, {'device': 'pc'}]},
                'cpelinkpc3': {'reqType': 'link', 'nodes': [{'device': 'cpe3'}, {'device': 'pc'}]},
                'cpelinkpc4': {'reqType': 'link', 'nodes': [{'device': 'cpe4'}, {'device': 'pc'}]},
            }
        },
    }
