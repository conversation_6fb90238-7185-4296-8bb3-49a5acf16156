# 中文语法高亮调试说明

## 问题分析

从您提供的调试输出 `处理行 0: "刘", 起始位置: 48, 长度: 1` 可以看出：

1. **问题**: 单个中文字符"刘"被单独处理，说明文本被错误分割
2. **原因**: 之前的实现使用了错误的文本分割方法
3. **修复**: 现在使用 QsciScintilla 原生 API 获取准确的行信息

## 调试信息

现在的代码会输出以下调试信息：

### 1. 总体信息
```
styleText: start=0, end=100, start_line=0, end_line=5
```

### 2. 行级别调试
```
处理行 0: "中文测试用例", 起始位置: 0, 结束位置: 6, 长度: 6
```

### 3. 样式应用调试
```
  样式应用: "中文测试用例" 位置:0-6 文档位置:0 样式:8
```

## 修复策略

### 1. 简化位置计算
- 使用 `editor.text()[start:end]` 获取要处理的文本段
- 避免复杂的行位置计算
- 使用简单的字符计数方式

### 2. 调试步骤

请按以下步骤进行调试：

1. **打开包含中文的 .robot 文件**
2. **查看控制台输出**，寻找类似这样的信息：
   ```
   处理行 0: "中文测试用例", 起始位置: 0, 长度: 6
   行内位置: 0-6, 文档位置: 0, 长度: 6, 样式: 8
   行内容: "中文测试用例"
   ```

3. **检查问题模式**：
   - 位置计算是否正确？
   - 长度计算是否准确？
   - 中文字符是否被正确识别？

### 3. 常见问题

#### 问题1：位置偏移
**症状**: 高亮位置与实际文本不匹配
**原因**: 中文字符的字节长度与字符长度不一致
**解决**: 确保使用字符长度而不是字节长度

#### 问题2：长度计算错误
**症状**: 高亮范围过长或过短
**原因**: `len()` 函数在不同编码下返回不同结果
**解决**: 统一使用 Python 字符串的 `len()` 函数

#### 问题3：编码不一致
**症状**: 某些中文字符无法正确高亮
**原因**: QsciScintilla 内部编码与 Python 字符串编码不一致
**解决**: 确保文件以 UTF-8 编码保存

## 测试用例

我创建了 `test_chinese_highlight.robot` 文件，包含：

1. **纯中文内容**
   ```robot
   中文测试用例
       中文关键字    ${中文变量}
   ```

2. **纯英文内容**
   ```robot
   English Test Case
       English Keyword    ${english_var}
   ```

3. **混合内容**
   ```robot
   Mixed中英文Test
       Mixed中英文Keyword    ${mixed变量}
   ```

## 预期行为

正确的语法高亮应该显示：

1. **测试用例名** (不以空格开头): 深红色粗体
2. **关键字** (以空格开头): 紫色粗体
3. **变量** (`${变量}`): 深橙色
4. **注释** (`# 注释`): 绿色

## 调试输出示例

### 正确的输出
```
处理行 0: "中文测试用例", 起始位置: 0, 长度: 6
行内位置: 0-6, 文档位置: 0, 长度: 6, 样式: 8
行内容: "中文测试用例"
```

### 错误的输出
```
处理行 0: "中文测试用例", 起始位置: 0, 长度: 18  # 错误：长度应该是6，不是18
行内位置: 0-6, 文档位置: 0, 长度: 6, 样式: 8
行内容: "中文测试用例"
```

## 下一步

请：

1. **打开 `test_chinese_highlight.robot` 文件**
2. **查看控制台输出**
3. **报告具体的调试信息**，特别是：
   - 哪一行出现问题？
   - 位置和长度的具体数值是什么？
   - 高亮效果与预期有什么差异？

这样我就能精确定位问题并提供针对性的修复方案。

## 临时解决方案

如果问题仍然存在，可以尝试：

1. **关闭语法高亮**: 在设置中临时禁用 Robot 语法高亮
2. **使用纯英文**: 暂时避免在关键位置使用中文
3. **重启编辑器**: 有时重新加载可以解决缓存问题

## 技术细节

### 字符编码处理
```python
# 错误的方式
byte_length = len(line.encode('utf-8'))  # 中文字符会是3个字节

# 正确的方式
char_length = len(line)  # 中文字符是1个字符
```

### 位置计算
```python
# 简化的位置计算
current_pos = start
for i, line in enumerate(lines):
    line_start = current_pos
    # 处理当前行
    current_pos += len(line) + (1 if i < len(lines) - 1 else 0)
```

这种方法确保了位置计算的一致性和准确性。
