# Python函数跳转功能说明

## 功能概述

已为TextEditor添加了Python函数按Ctrl+左键点击的跳转功能，用户可以像在现代IDE中一样快速导航到函数定义。

## 修改的文件

- `controller/system_plugin/text_edit/TextEditor.py`

## 新增功能

### 1. 鼠标事件处理
- **方法**: `mousePressEvent(self, event)`
- **功能**: 检测Ctrl+左键点击组合，触发跳转功能

### 2. 点击位置文本提取
- **方法**: `_get_function_name_at_position(self, pos)`
- **功能**: 
  - 使用QsciScintilla的SendScintilla API获取鼠标位置
  - 智能提取点击位置的函数名
  - 验证函数名的有效性

### 3. 函数定义查找
- **方法**: `_find_function_definition_in_current_file(self, function_name)`
- **功能**: 
  - 使用正则表达式在当前文件中查找函数定义
  - 支持各种函数定义格式（带空格、缩进等）

### 4. 跳转功能
- **方法**: `_jump_to_function_definition(self, function_name)`
- **功能**: 
  - 优先在当前文件中查找函数定义
  - 如果找到，直接跳转并高亮显示
  - 如果没找到，使用现有的关键字跳转机制

### 5. 集成现有系统
- **方法**: `_try_keyword_jump(self, function_name)`
- **功能**: 
  - 利用项目中已有的SpecifiedKeywordJumper类
  - 支持跨文件的函数跳转

## 使用方法

1. 在TextEditor中打开Python文件
2. 按住Ctrl键
3. 左键点击任何函数名
4. 系统会自动跳转到函数定义位置

## 支持的功能

- ✅ 当前文件内的函数跳转
- ✅ 类方法跳转
- ✅ 全局函数跳转
- ✅ 跨文件函数跳转（通过现有的关键字跳转机制）
- ✅ 智能函数名识别
- ✅ 错误处理和异常捕获

## 技术实现细节

### API修复
原始实现中使用了错误的API调用：
```python
# 错误的方式
line, index = self.lineIndexFromPosition(pos)  # pos是QPoint对象
```

修复后使用正确的QsciScintilla API：
```python
# 正确的方式
position = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINT, pos.x(), pos.y())
line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, position)
column = self.SendScintilla(QsciScintilla.SCI_GETCOLUMN, position)
```

### 函数名识别算法
1. 从点击位置开始，向前和向后扩展
2. 只包含字母、数字和下划线的字符
3. 验证函数名必须以字母或下划线开头

### 函数定义查找
使用正则表达式匹配函数定义：
```python
pattern = r'^\s*def\s+' + re.escape(function_name) + r'\s*\('
```

## 测试文件

创建了测试文件 `test_python_jump.py`，包含：
- 全局函数
- 类方法
- 函数调用链
- 各种函数定义格式

## 错误处理

所有方法都包含完整的异常处理，确保：
- 不会因为错误的点击位置而崩溃
- 提供有用的错误信息用于调试
- 优雅地处理边界情况

## 兼容性

- 完全兼容现有的TextEditor功能
- 不影响其他鼠标事件处理
- 集成现有的关键字跳转系统
