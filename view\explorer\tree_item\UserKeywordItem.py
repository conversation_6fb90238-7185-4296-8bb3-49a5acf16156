# coding=utf-8
'''
Created on 2019年11月1日

@author: 10240349
'''
import os
import traceback

from PyQt5.Qt import QIcon
from PyQt5.QtWidgets import QMenu

from controller.system_plugin.SignalDistributor import SignalDistributor
from model.CurrentItem import CurrentItem
from model.ProjectTree import ProjectTree
from resources.Loader import Loader
from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from utility.UIRepository import UIRepository
from view.common.MessageBox import message_box_dec
from view.explorer.tree_item.TreeItem import TreeItem


class UserKeywordItem(TreeItem):

    def __init__(self, parent, data_file=None, path=None):
        self._path = path
        super(UserKeywordItem, self).__init__(parent, data_file, path)
        self._data_file = data_file

    def reload_children(self):
        pass

    def set_ui(self):
        self._name = self._data_file.name
        self.setText(0, self._name)
        self.setIcon(0, QIcon(Loader().get_path('USERKEYWORD')))

    def update_local_repository(self):
        self.parent().update_local_repository()

    def refresh_children(self):
        pass

    def reload_children_once(self):
        pass

    def get_path(self):
        return self._data_file.path + os.path.sep + self._name

    def show_context_menu(self, parent, pos):
        self._tree = parent
        menu = QMenu(parent)
        menu.setToolTipsVisible(True)
        self._set_home_page = menu.addAction(LanguageLoader().get('SET_HOME_PAGE'))
        self._copy_item = menu.addAction(LanguageLoader().get('COPY'))
        self._rename_item = menu.addAction(LanguageLoader().get('RENAME'))
        self._delete_item = menu.addAction(LanguageLoader().get('DELETE'))
        self._move_up_item = menu.addAction(LanguageLoader().get('MOVE_UP'))
        self._move_down_item = menu.addAction(LanguageLoader().get('MOVE_DOWN'))
        
        self._set_home_page.setToolTip(LanguageLoader().get('SET_HOME_PAGE_TOOLTIP'))
        self._action = menu.exec_(parent.mapToGlobal(pos))
        self._execute_action()

    def _execute_action(self):
        if self._action == self._delete_item:
            self._delete()
        elif self._action == self._rename_item:
            self._set_edit_status(self._tree.currentItem().text(0))
        elif self._action == self._copy_item:
            self._copy()
        elif self._action == self._move_up_item:
            self._move_up()
        elif self._action == self._move_down_item:
            self._move_down()
        elif self._action == self._set_home_page:
            self._set_home_page_handler()

    @message_box_dec(LanguageLoader().get('DELETE_TIPS'))
    def _delete(self):
        selected_items = self._tree.selectedItems()
        deleted_items = []

        for item in selected_items:
            if isinstance(item, UserKeywordItem):
                parent = item.parent()
                index = parent.indexOfChild(item) - parent._variable_num - parent._testcase_num
                parent._data_file.delete_keyword(index)
                parent._data_file.save()
                parent.removeChild(item)
                parent._keyword_num -= 1
                deleted_items.append(item)

        # If no items were deleted, return
        if not deleted_items:
            return

        # Find the next item to focus on and update the text editor
        current = ProjectTreeRepository().find("PROJECT_TREE").currentItem()
        SignalDistributor().show(current)

        # Update the text editor with the parent's path of the last deleted item
        if deleted_items:
            parent_path = deleted_items[-1].parent().get_path()
            self.update_text_edit(parent_path)  # 树上节点删除更新text_edit

    def _copy(self):
        UIRepository().update('copyed_data_file', [item for item in self._tree.selectedItems()])

    def _move_up(self):
        parent = self.parent()
        variable_num = parent._variable_num
        testcase_num = parent._testcase_num
        if variable_num + testcase_num >= parent.indexOfChild(self):
            return
        if isinstance(parent._data_file, ProjectTree):
            parent._data_file.init_file.move_up(self._data_file)
        else:
            parent._data_file.move_up(self._data_file)
        index = parent.indexOfChild(self) - 1
        parent.removeChild(self)
        parent.insertChild(index, self)
        self._refresh_content(self)

    def _move_down(self):
        parent = self.parent()
        variable_num = parent._variable_num
        testcase_num = parent._testcase_num
        keyword_num = parent._keyword_num
        if parent.indexOfChild(self) + 1 >= variable_num + testcase_num + keyword_num:
            return
        if isinstance(parent._data_file, ProjectTree):
            parent._data_file.init_file.move_down(self._data_file)
        else:
            parent._data_file.move_down(self._data_file)
        index = parent.indexOfChild(self) + 1
        parent.removeChild(self)
        parent.insertChild(index, self)
        self._refresh_content(self)
