# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     RobotHighlighter
   Description :   RobotFramework语法高亮器
   Author :       Assistant
   date：          2025/01/23
-------------------------------------------------
   Change Activity:
                   2025/01/23: 创建RobotFramework语法高亮器
-------------------------------------------------
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor
from PyQt5.Qsci import QsciLexerCustom, QsciScintilla
from settings.UserSettings import UserSettings
import re


class RobotHighlighter(QsciLexerCustom):
    # 定义样式ID
    Default = 0
    Section = 1
    Keyword = 2
    Variable = 3
    Comment = 4
    TestCase = 5
    Setting = 6
    Number = 7
    String = 8
    BuiltinKeyword = 9
    Tag = 10
    
    def __init__(self, parent=None):
        super(RobotHighlighter, self).__init__(parent)
        
        # 设置默认字体
        font = QFont()
        font.setStyleName('Normal')
        font.setPointSize(UserSettings().get_value("TEXT_EDIT_FONT_SIZE"))
        font.setFamily(UserSettings().get_value("FONT"))
        self.setDefaultFont(font)
        
        # 设置各种样式的颜色
        self.setColor(QColor("#000000"), self.Default)  # 黑色 - 默认文本
        self.setColor(QColor("#0000FF"), self.Section)  # 蓝色 - 部分标题
        self.setColor(QColor("#800080"), self.Keyword)  # 紫色 - 关键字
        self.setColor(QColor("#FF8C00"), self.Variable)  # 深橙色 - 变量
        self.setColor(QColor("#008000"), self.Comment)  # 绿色 - 注释
        self.setColor(QColor("#8B0000"), self.TestCase)  # 深红色 - 测试用例名
        self.setColor(QColor("#4B0082"), self.Setting)  # 靛蓝色 - 设置项
        self.setColor(QColor("#FF00FF"), self.Number)  # 洋红色 - 数字
        self.setColor(QColor("#DC143C"), self.String)  # 深红色 - 字符串
        self.setColor(QColor("#0000CD"), self.BuiltinKeyword)  # 中蓝色 - 内置关键字
        self.setColor(QColor("#006400"), self.Tag)  # 深绿色 - 标签
        
        # 设置字体样式
        self.setFont(font, self.Default)
        bold_font = QFont(font)
        bold_font.setBold(True)
        self.setFont(bold_font, self.Section)
        self.setFont(bold_font, self.TestCase)
        self.setFont(bold_font, self.Keyword)
        
        # 定义内置关键字
        self.builtin_keywords = [
            'log', 'sleep', 'should be true', 'should be false', 
            'should be equal', 'should not be equal', 'should contain',
            'should not contain', 'run keyword', 'run keyword if',
            'run keyword and return status', 'set variable', 'get variable value',
            'create list', 'create dictionary', 'append to list',
            'get from dictionary', 'set to dictionary', 'comment',
            'pass execution', 'fail', 'fatal error', 'import library',
            'import resource', 'import variables', 'set test variable',
            'set suite variable', 'set global variable', 'get time',
            'evaluate', 'call method', 'convert to integer', 'convert to string',
            'should be empty', 'should not be empty', 'length should be',
            'should match', 'should not match', 'should match regexp',
            'should not match regexp', 'get length', 'count values in list',
            'get from list', 'get index from list', 'copy list',
            'reverse list', 'sort list', 'combine lists', 'remove from list',
            'remove duplicates', 'get dictionary keys', 'get dictionary values',
            'dictionary should contain key', 'dictionary should not contain key',
            'lists should be equal', 'dictionaries should be equal'
        ]
        
        # 编译正则表达式以提高性能
        # 使用更全面的正则表达式匹配中文
        self.section_pattern = re.compile(r'^\*{3}\s*(\w+\s*?){1,3}\*{3}', re.IGNORECASE | re.UNICODE)  # 匹配含中文的章节头
        self.variable_pattern = re.compile(r'\$\{.*?\}|@\{.*?\}|&\{.*?\}|%\{.*?\}', re.UNICODE)  # 非贪婪匹配包含中文的变量
        self.setting_pattern = re.compile(r'^\s*\[([\u4e00-\u9fa5A-Za-z]+)\]', re.IGNORECASE | re.UNICODE)  # 支持中文设置项
        self.number_pattern = re.compile(r'(?<!\$)\b\d+\.?\d*\b', re.UNICODE)  # 排除变量中的数字
        self.string_pattern = re.compile(r'["“](.*?)["”]|[\'‘](.*?)[\'’]', re.UNICODE)  # 支持中文引号
        # 改进的中文注释正则表达式，支持：
        # 1. 行首任意空白+#号
        # 2. Comment关键字后跟中文
        # 3. 中文注释内容
        self.chinese_comment_pattern = re.compile(
            r'^([\s\t]*#.*|Comment\s+.*|#.*[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]+.*)', 
            re.UNICODE
        )
        
    def language(self):
        return "RobotFramework"
    
    def description(self, style):
        descriptions = {
            self.Default: "Default",
            self.Section: "Section",
            self.Keyword: "Keyword",
            self.Variable: "Variable",
            self.Comment: "Comment",
            self.TestCase: "TestCase",
            self.Setting: "Setting",
            self.Number: "Number",
            self.String: "String",
            self.BuiltinKeyword: "BuiltinKeyword",
            self.Tag: "Tag"
        }
        return descriptions.get(style, "")
    
    def styleText(self, start, end):
        # 初始化样式
        self.startStyling(start)
        
        # 获取要处理的文本
        text = self.parent().text()[start:end]
        
        # 按行处理
        lines = text.split('\n')
        current_pos = start
        in_documentation = False
        
        for line in lines:
            line_start = current_pos
            line_length = len(line)
            
            # 处理文档字符串
            if line.strip().startswith('[Documentation]'):
                in_documentation = True
                doc_start = line.find('[Documentation]')
                self.startStyling(line_start)
                self.setStyling(doc_start, self.Default)
                self.startStyling(line_start + doc_start)
                self.setStyling(len('[Documentation]'), self.Setting)
                self.startStyling(line_start + doc_start + len('[Documentation]'))
                self.setStyling(line_length - doc_start - len('[Documentation]'), self.String)
            elif in_documentation:
                # 文档字符串延续行
                self.startStyling(line_start)
                self.setStyling(line_length, self.String)
                if not line.strip().endswith('\\'):
                    in_documentation = False
            else:
                # 改进的注释处理逻辑
                comment_match = self.chinese_comment_pattern.match(line)
                if comment_match:
                    # 只高亮注释部分，保留行首空格
                    comment_start = line.find('#') if '#' in line else line.find('Comment')
                    if comment_start >= 0:
                        self.startStyling(line_start)
                        self.setStyling(comment_start, self.Default)  # 行首空格保持默认
                        self.startStyling(line_start + comment_start)
                        self.setStyling(line_length - comment_start, self.Comment)
                else:
                    self.startStyling(line_start)
                    self.setStyling(line_length, self.Default)
            
            # 移动到下一行（包括换行符）
            current_pos += line_length + 1
    
    def _style_line_content(self, line, line_start):
        """处理行内的各种语法元素"""
        # 首先设置整行为默认样式
        self.startStyling(line_start)
        self.setStyling(len(line), self.Default)
        
        # 处理设置项
        setting_match = self.setting_pattern.match(line)
        if setting_match:
            self.startStyling(line_start + setting_match.start())
            self.setStyling(setting_match.end() - setting_match.start(), self.Setting)
            
            # 特别处理[Tags]
            if 'Tags' in setting_match.group():
                # 查找标签内容
                tag_content = line[setting_match.end():].strip()
                if tag_content:
                    self.startStyling(line_start + setting_match.end())
                    self.setStyling(len(line) - setting_match.end(), self.Tag)
        
        # 处理变量
        for match in self.variable_pattern.finditer(line):
            self.startStyling(line_start + match.start())
            self.setStyling(match.end() - match.start(), self.Variable)
        
        # 处理字符串
        for match in self.string_pattern.finditer(line):
            self.startStyling(line_start + match.start())
            self.setStyling(match.end() - match.start(), self.String)
        
        # 处理数字
        for match in self.number_pattern.finditer(line):
            # 确保数字不在变量或字符串内
            if not self._is_in_variable_or_string(line, match.start(), match.end()):
                self.startStyling(line_start + match.start())
                self.setStyling(match.end() - match.start(), self.Number)
        
        # 处理内置关键字
        line_lower = line.lower()
        for keyword in self.builtin_keywords:
            keyword_lower = keyword.lower()
            # 使用制表符或多个空格作为分隔符
            patterns = [
                f'\t{keyword_lower}\t',
                f'\t{keyword_lower}$',
                f'  {keyword_lower}  ',
                f'  {keyword_lower}$'
            ]
            for pattern in patterns:
                index = 0
                while True:
                    index = line_lower.find(pattern, index)
                    if index == -1:
                        break
                    # 找到关键字的实际起始位置
                    # 精确匹配关键字边界（支持中文后的空白）
                    keyword_start = index + (pattern.find(keyword_lower) if pattern.startswith(('\t', '  ')) else 0)
                    if pattern.startswith('\t'):
                        keyword_start = index + 1
                    elif pattern.startswith('  '):
                        keyword_start = index + 2
                    
                    self.startStyling(line_start + keyword_start)
                    self.setStyling(len(keyword), self.BuiltinKeyword)
                    index += len(pattern)
    
    def _is_in_variable_or_string(self, line, start, end):
        """检查给定位置是否在变量或字符串内"""
        # 检查是否在变量内
        for match in self.variable_pattern.finditer(line):
            if match.start() <= start < match.end() or match.start() < end <= match.end():
                return True
        
        # 检查是否在字符串内
        for match in self.string_pattern.finditer(line):
            if match.start() <= start < match.end() or match.start() < end <= match.end():
                return True
        
        return False
