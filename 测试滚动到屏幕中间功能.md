# 测试滚动到屏幕中间功能

## 功能说明

优化了 `_show_content` 和 `_highlight_py_keyword` 方法，确保所选项滚动到屏幕中间，提供更好的用户体验。

## 测试步骤

### 1. 准备测试环境
1. 打开一个包含多个测试用例的大型测试套件文件（建议50行以上）
2. 确保文件内容足够长，可以测试滚动效果
3. 打开文本编辑器视图

### 2. 测试项目树导航（_show_content）

#### 测试场景1: 基本滚动功能
1. 在项目树中点击文件开头的测试用例
2. 观察文本编辑器的滚动位置
3. **验证**: 目标行应该出现在屏幕中间位置

#### 测试场景2: 文件中间的项目
1. 在项目树中点击文件中间的测试用例
2. 观察文本编辑器的滚动位置
3. **验证**: 目标行应该出现在屏幕中间，上下都有足够的上下文

#### 测试场景3: 文件结尾的项目
1. 在项目树中点击文件结尾的测试用例
2. 观察文本编辑器的滚动位置
3. **验证**: 目标行应该尽可能接近屏幕中间（受文件长度限制）

### 3. 测试关键字跳转（_highlight_py_keyword）

#### 测试场景1: Python关键字跳转
1. 在包含Python关键字的文件中触发关键字跳转
2. 观察文本编辑器的滚动位置
3. **验证**: 目标函数定义行应该出现在屏幕中间

#### 测试场景2: 多个关键字跳转
1. 连续跳转到不同的Python关键字
2. 观察每次跳转的滚动效果
3. **验证**: 每次跳转都应该将目标行滚动到屏幕中间

### 4. 测试边界情况

#### 测试场景1: 短文件
1. 打开一个行数少于屏幕显示行数的文件
2. 点击项目树中的项目
3. **验证**: 应该正常显示，不会出现错误

#### 测试场景2: 文件开头
1. 点击文件开头的项目（前5行）
2. 观察滚动位置
3. **验证**: 文件应该从第一行开始显示，目标行尽可能居中

#### 测试场景3: 文件结尾
1. 点击文件结尾的项目（后5行）
2. 观察滚动位置
3. **验证**: 文件应该显示到最后，目标行尽可能居中

### 5. 测试错误处理

#### 测试场景1: 异常情况模拟
1. 在复杂的文件结构中测试
2. 快速连续点击不同的项目
3. **验证**: 不应该出现错误，功能应该稳定工作

## 预期结果

### ✅ 正常情况
- 目标行出现在屏幕中间位置
- 上下文信息充足，便于阅读
- 光标正确定位到目标行
- 没有文字被选中

### ✅ 边界情况
- 短文件：正常显示，不出错
- 文件开头：目标行尽可能居中
- 文件结尾：目标行尽可能居中

### ✅ 错误处理
- 异常情况下自动回退到默认滚动
- 错误信息记录到控制台
- 不影响用户的正常操作

## 技术验证要点

### 1. 滚动算法验证
```python
# 验证计算逻辑
lines_on_screen = 30  # 假设屏幕显示30行
target_line = 50      # 目标行
center_offset = 30 // 2 = 15
target_first_visible_line = 50 - 15 = 35

# 结果：第35行开始显示，目标行50在屏幕中间
```

### 2. 边界检查验证
```python
# 文件开头情况
target_line = 5
target_first_visible_line = 5 - 15 = -10
# 修正后：max(0, -10) = 0

# 文件结尾情况
total_lines = 100
max_line = 100 - 30 = 70
target_first_visible_line = 85
# 修正后：min(85, 70) = 70
```

### 3. API调用验证
- `SendScintilla(SCI_LINESONSCREEN)` 返回正确的屏幕行数
- `SendScintilla(SCI_SETFIRSTVISIBLELINE)` 正确执行滚动
- 错误情况下 `ensureLineVisible()` 正常工作

## 用户体验验证

### 1. 视觉效果
- [ ] 滚动动作流畅自然
- [ ] 目标行清晰可见
- [ ] 上下文信息充足
- [ ] 光标位置明确

### 2. 操作便利性
- [ ] 减少手动滚动需求
- [ ] 快速定位到目标内容
- [ ] 提高代码阅读效率
- [ ] 降低视觉疲劳

### 3. 一致性
- [ ] 所有导航操作行为一致
- [ ] 与其他编辑器功能协调
- [ ] 符合用户操作习惯

## 性能测试

### 1. 响应速度
- 测试大文件（1000+行）的滚动响应时间
- 验证连续快速点击的性能表现
- 确认没有明显的延迟或卡顿

### 2. 内存使用
- 验证滚动操作不会导致内存泄漏
- 确认算法的内存使用效率
- 测试长时间使用的稳定性

### 3. CPU使用
- 验证滚动计算的CPU开销
- 确认不会影响整体应用性能
- 测试在低性能设备上的表现

## 兼容性测试

### 1. 文件类型
- [ ] Robot Framework 文件 (.robot)
- [ ] TSV 文件 (.tsv)
- [ ] Python 文件 (.py)
- [ ] 其他支持的文件类型

### 2. 文件大小
- [ ] 小文件（<50行）
- [ ] 中等文件（50-500行）
- [ ] 大文件（500-2000行）
- [ ] 超大文件（>2000行）

### 3. 内容复杂度
- [ ] 简单的测试用例
- [ ] 复杂的嵌套结构
- [ ] 包含中文的内容
- [ ] 特殊字符和格式

## 回归测试

### 1. 现有功能验证
- [ ] 文本编辑功能正常
- [ ] 语法高亮正常
- [ ] 搜索功能正常
- [ ] 其他导航功能正常

### 2. 信号系统验证
- [ ] `_show_content` 信号正常触发
- [ ] `_highlight_py_keyword` 信号正常触发
- [ ] 其他相关信号不受影响

### 3. 界面更新验证
- [ ] 文本编辑器正确更新
- [ ] 光标位置正确显示
- [ ] 滚动条位置正确
- [ ] 行号显示正确

## 日志验证

### 成功情况
```
已将第 50 行滚动到屏幕中间
```

### 异常情况
```
滚动到屏幕中间时出错: [具体错误信息]
```

### 回退情况
- 应该能看到使用 `ensureLineVisible()` 的回退行为
- 功能应该继续正常工作

## 测试工具

### 1. 手动测试
- 使用真实的项目文件进行测试
- 模拟用户的实际使用场景
- 验证用户体验的改善

### 2. 自动化测试
- 编写单元测试验证算法逻辑
- 测试边界条件和异常情况
- 验证性能指标

### 3. 压力测试
- 大量连续的滚动操作
- 长时间的使用测试
- 内存和性能监控

## 验收标准

### 必须满足
1. ✅ 目标行正确滚动到屏幕中间
2. ✅ 边界情况正确处理
3. ✅ 错误情况不影响用户操作
4. ✅ 不破坏现有功能

### 期望达到
1. ✅ 用户体验明显改善
2. ✅ 减少手动滚动操作
3. ✅ 提高代码阅读效率
4. ✅ 性能表现良好

## 总结

通过这个优化，用户在使用项目树导航和关键字跳转时将获得更好的体验：
- 目标内容始终出现在最佳的阅读位置
- 充足的上下文信息帮助理解代码结构
- 减少了额外的滚动操作，提高了工作效率
