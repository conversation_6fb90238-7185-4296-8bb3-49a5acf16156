*** Settings ***
Documentation    这是一个测试RobotFramework语法高亮的示例文件
Library          SeleniumLibrary
Library          Collections
Resource         common.robot
Variables        variables.py

Suite Setup      打开浏览器
Suite Teardown   关闭浏览器
Test Setup       准备测试数据
Test Teardown    清理测试数据

*** Variables ***
${URL}           https://www.example.com
${BROWSER}       chrome
${TIMEOUT}       10s
@{USERS}         admin    user1    user2
&{CONFIG}        host=localhost    port=8080    debug=true

*** Test Cases ***
登录测试
    [Documentation]    测试用户登录功能
    [Tags]    smoke    login    critical
    [Setup]    准备登录测试数据
    [Teardown]    清理登录测试数据
    
    打开登录页面
    输入用户名    ${USERNAME}
    输入密码      ${PASSWORD}
    点击登录按钮
    验证登录成功

搜索功能测试
    [Documentation]    测试搜索功能
    ...                支持多行文档字符串
    ...                这是第三行文档
    [Tags]    search    功能测试
    
    打开搜索页面
    输入搜索关键字    robot framework
    点击搜索按钮
    验证搜索结果    robot framework

数据驱动测试
    [Template]    验证计算结果
    [Tags]    data-driven
    
    # 测试数据：第一个数字  第二个数字  期望结果
    1    2    3
    5    5    10
    10   -3   7

循环测试示例
    [Documentation]    演示FOR循环语法
    
    FOR    ${user}    IN    @{USERS}
        Log    当前用户: ${user}
        验证用户权限    ${user}
    END
    
    FOR    ${i}    IN RANGE    1    6
        Log    循环次数: ${i}
        IF    ${i} == 3
            Log    到达中间值
        ELSE IF    ${i} > 3
            Log    超过中间值
        ELSE
            Log    小于中间值
        END
    END

条件判断测试
    [Documentation]    演示IF条件判断
    
    ${status}=    Get Test Status
    IF    '${status}' == 'PASS'
        Log    测试通过
    ELSE
        Log    测试失败
        Fail    测试执行失败
    END

*** Keywords ***
打开登录页面
    [Documentation]    打开登录页面的关键字
    [Arguments]    ${url}=${URL}
    
    Open Browser    ${url}    ${BROWSER}
    Maximize Browser Window
    Wait Until Page Contains    登录    timeout=${TIMEOUT}

输入用户名
    [Arguments]    ${username}
    Input Text    id=username    ${username}
    Sleep    0.5s

输入密码
    [Arguments]    ${password}
    Input Password    id=password    ${password}

点击登录按钮
    Click Button    id=login-btn
    Sleep    1s

验证登录成功
    Wait Until Page Contains    欢迎    timeout=${TIMEOUT}
    Page Should Contain    用户中心
    ${current_url}=    Get Location
    Should Contain    ${current_url}    dashboard

验证计算结果
    [Arguments]    ${num1}    ${num2}    ${expected}
    ${result}=    Evaluate    ${num1} + ${num2}
    Should Be Equal As Numbers    ${result}    ${expected}
    Log    ${num1} + ${num2} = ${result}

验证用户权限
    [Arguments]    ${username}
    Log    验证用户 ${username} 的权限
    Run Keyword If    '${username}' == 'admin'    验证管理员权限
    ...    ELSE    验证普通用户权限

验证管理员权限
    Log    验证管理员权限
    Should Be True    True    管理员权限验证通过

验证普通用户权限
    Log    验证普通用户权限
    Should Be True    True    普通用户权限验证通过

准备测试数据
    [Documentation]    准备测试所需的数据
    Log    准备测试数据
    Set Test Variable    ${USERNAME}    testuser
    Set Test Variable    ${PASSWORD}    testpass123

清理测试数据
    [Documentation]    清理测试产生的数据
    Log    清理测试数据
    # 这里可以添加清理逻辑

打开浏览器
    [Documentation]    套件级别的浏览器初始化
    Log    初始化浏览器环境
    Set Global Variable    ${BROWSER_OPENED}    True

关闭浏览器
    [Documentation]    套件级别的浏览器清理
    Run Keyword If    '${BROWSER_OPENED}' == 'True'    Close All Browsers
    Log    浏览器环境已清理

# 这是单行注释
# 支持中文注释：这是中文注释测试

*** Comments ***
这是注释部分
可以写多行注释
支持中文注释内容
用于说明测试套件的整体信息
