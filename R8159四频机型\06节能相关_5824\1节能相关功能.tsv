*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-5584812 多频段prru：各通道各制式配置NR和LTE载波，打开所有频段符号关断（其中2.6G LV配置）__RAN-5584812	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown
	...	Start ES	30				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1
	...	Symbol Shutdown	Start ES				
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	#计数器C37058						
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	101	
	should be true	${result}					
	打开所有频段符号关断开关						
	sleep	240					
	关闭所有频段符号关断开关						
	sleep	60					
	Wait Until Keyword Succeeds	5min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown
	...	Stop ES	30				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1
	...	Symbol Shutdown	Stop ES				
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and continue on failure	should be true	${esPower} < ${power}				
	sleep	120					
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	#计数器C37058						
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	101	
	should not be true	${result}					
	[Teardown]	恢复环境					
							
RAN-5584865 多频段prru：各通道各制式配置NR和LTE载波，打开2.6G TNR符号关断，对其他频段无影响（其中2.6G LV配置）__RAN-5584865	关闭NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	5min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown
	...	Start ES	30				
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1	Symbol Shutdown	Start ES	100
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	101	
	should be true	${result}					
	关闭NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	101	
	should not be true	${result}					
	[Teardown]	恢复环境					
							
RAN-5584847 多频段prru：各通道各制式配置NR和LTE载波，打开4.9G TNR符号关断，对其他频段无影响（其中2.6G LV配置）__RAN-5584847	关闭NR符号关断开关	${cell4}					
	关闭NR符号关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR符号关断开关	${cell4}					
	打开NR符号关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Start ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	打开NR符号关断开关	${cell4}					
	打开NR符号关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	关闭NR符号关断开关	${cell4}					
	关闭NR符号关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	确认sonm节能监控上报正确	${GNODEB}	DTX ES	4	Symbol Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584875 多频段prru：各通道各制式配置NR和LTE载波，打开FDL符号关断, 对其他频段无影响（其中2.6G LV配置）__RAN-5584875	ITRAN-LTE-FDD符号关断开关	1					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	ITRAN-LTE-FDD符号关断开关	0					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584890 多频段prru：各通道各制式配置NR和LTE载波，打开TDL符号关断,对其他频段无影响（其中2.6G LV配置）__RAN-5584890	ITRAN-LTE-TDD符号关断开关	1					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Start
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	ITRAN-LTE-TDD符号关断开关	0					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585177 共通道prru：2.6G配置NR+L载波共通道，NR进入符号关断后，删除LTE小区，再新建LTE小区__RAN-5585177	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	关闭NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1
	...	Symbol Shutdown	Start ES				
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证NR小区	${cell1}	${CPE}	${PDN}			
	删除指定LTE-TDD小区_多模	${ENODEB}	5				
	删除指定LTE-TDD小区_多模	${ENODEB}	6				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	创建指定LTE-TDD小区_多模	${ENODEB}	5				
	创建指定LTE-TDD小区_多模	${ENODEB}	6				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585187 共通道prru：2.6G配置NR+L载波共通道，NR进入符号关断后，闭塞LTE小区__RAN-5585187	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	关闭NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	打开NR符号关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1
	...	Symbol Shutdown	Start ES				
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证NR小区	${cell1}	${CPE}	${PDN}			
	修改TDL小区管理状态	5	1				
	修改TDL小区管理状态	6	1				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	修改TDL小区管理状态	5	0				
	修改TDL小区管理状态	6	0				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585195 共通道prru：2.6G配置NR+L载波共通道，LTE进入符号关断后，删除NR小区，再新建NR小区__RAN-5585195	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	ITRAN-LTE-TDD符号关断开关	1					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start
	...	20	5				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	删除指定NR小区_多模	${GNODEB}	1				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	创建指定NR小区_多模	${GNODEB}	1				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585198 共通道prru：2.6G配置NR+L载波共通道，LTE进入符号关断后，闭塞NR小区__RAN-5585198	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	ITRAN-LTE-TDD符号关断开关	1					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start
	...	20	5				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	${esPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${esPower} < ${power}				
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	闭塞NR小区_多模	${cell1}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	解闭塞NR小区_多模	${cell1}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5585201 符号关断与工具：所有频段NR LTE进入符号关断和退出符号关断后，操作NI扫描__RAN-5585201	获取所有小区别名_5824						
	ITRAN-LTE-FDD符号关断开关	0					
	ITRAN-LTE-TDD符号关断开关	0					
	关闭所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	ITRAN-LTE-FDD符号关断开关	1					
	ITRAN-LTE-TDD符号关断开关	1					
	打开所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	${subscriptionId}	创建小区频谱扫描任务_多模	${cell1}				
	sleep	90					
	${filePath}	导出频谱扫描数据_多模	${GNODEB}	${subscriptionId}			
	验证频谱扫描结果CSV文件	${filePath}	1				
	删除小区频谱扫描任务_多模	${cell1}	${subscriptionId}				
	ITRAN-LTE-FDD符号关断开关	0					
	ITRAN-LTE-TDD符号关断开关	0					
	关闭所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	${subscriptionId2}	创建小区频谱扫描任务_多模	${cell2}				
	sleep	90					
	${filePath2}	导出频谱扫描数据_多模	${GNODEB}	${subscriptionId2}			
	验证频谱扫描结果CSV文件	${filePath2}	2				
	删除小区频谱扫描任务_多模	${cell2}	${subscriptionId2}				
	[Teardown]	恢复环境					
							
RAN-5585203 符号关断与诊断：所有频段NR LTE进入符号关断和退出符号关断后，小区功率查询、查询prru实际发射功率__RAN-5585203	获取所有小区别名_5824						
	ITRAN-LTE-FDD符号关断开关	1					
	ITRAN-LTE-TDD符号关断开关	1					
	打开所有NR小区符号关断开关	${ENODEB}					
	同步规划区数据_多模	${ENODEB}					
	sleep	180					
	${powerTx1}	查询PRRU发射功率_多模	101-instance	4			
	${cellPower1}	查询NR小区功率_多模	${cell1}				
	should be true	${cellPower1}> -15	小区功率异常				
	should be true	${powerTx1}> -5	PRRU发射功率异常				
	ITRAN-LTE-FDD符号关断开关	0					
	ITRAN-LTE-TDD符号关断开关	0					
	关闭所有NR小区符号关断开关	${ENODEB}					
	sleep	180					
	${powerTx2}	查询PRRU发射功率_多模	101-instance	4			
	${cellPower2}	查询NR小区功率_多模	${cell1}				
	should be true	${cellPower2}> -15	小区功率异常				
	should be true	${powerTx2}> -5	PRRU发射功率异常				
	run keyword and ignore error	should be true	${powerTx1} < ${powerTx2}				
	run keyword and ignore error	should be true	${cellPower1}} < ${cellPower2}				
	[Teardown]	恢复环境					
							
RAN-5585255 符号关断与性能测量：所有频段NR LTE进入符号关断和退出符号关断后，验证性能测量数据正常上报__RAN-5585255	获取所有小区别名_5824						
	ITRAN-LTE-FDD符号关断开关	0					
	ITRAN-LTE-TDD符号关断开关	0					
	关闭所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	ITRAN-LTE-FDD符号关断开关	1					
	ITRAN-LTE-TDD符号关断开关	1					
	打开所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	Wait Until Keyword Succeeds	15min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown
	...	Start ES	30				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	3	
	Wait Until Keyword Succeeds	10min	60sec	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1
	...	Symbol Shutdown	Start ES				
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	1;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Start
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfSymbolShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfSymbolShutdown	
	#射频通道测量	#通道级载波测量					
	性能数据测量测试模板	AauCarrier	AauChannel				
	ITRAN-LTE-FDD符号关断开关	0					
	ITRAN-LTE-TDD符号关断开关	0					
	关闭所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	60					
	Wait Until Keyword Succeeds	5min	60sec	判断sonm节能上报是否上报成功	${GNODEB}	DTX ES	Symbol Shutdown
	...	Stop ES	30				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认sonm节能监控上报正确	${GNODEB}	DTX ES	1
	...	Symbol Shutdown	Stop ES				
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	DTX ES	ES Stop
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认LTE sonm节能上报成功判断	${GNODEB}	DTX ES	ES Stop
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	#射频通道测量	#通道级载波测量					
	性能数据测量测试模板	AauCarrier	AauChannel				
	[Teardown]						
							
RAN-5584536 共通道prru：2.6G配置NR+L载波共通道，NR进入载波关断后，删除LTE小区，再新建LTE小区__RAN-5584536	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	关闭NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	120					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1
	...	Carrier Shutdown	Start ES				
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	${result}	PRRU诊断测试_多模	101-instance				
	${PA}	set variable	${result['pa']}				
	${ant3PaState}	set variable	${PA[2][-1]}				
	${ant4PaState}	set variable	${PA[3][-1]}				
	${ant5PaState}	set variable	${PA[4][-1]}				
	${ant6PaState}	set variable	${PA[5][-1]}				
	should be equal	${ant3PaState}	Open				
	should be equal	${ant4PaState}	Open				
	should be equal	${ant5PaState}	Close				
	should be equal	${ant6PaState}	Close				
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	删除指定LTE-TDD小区_多模	${ENODEB}	5				
	删除指定LTE-TDD小区_多模	${ENODEB}	6				
	sleep	100					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Close	
	创建指定LTE-TDD小区_多模	${ENODEB}	5				
	创建指定LTE-TDD小区_多模	${ENODEB}	6				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	sleep	200					
	${result}	PRRU诊断测试_多模	101-instance				
	${PA}	set variable	${result['pa']}				
	${ant3PaState}	set variable	${PA[2][-1]}				
	${ant4PaState}	set variable	${PA[3][-1]}				
	${ant5PaState}	set variable	${PA[4][-1]}				
	${ant6PaState}	set variable	${PA[5][-1]}				
	should be equal	${ant3PaState}	Open				
	should be equal	${ant4PaState}	Open				
	should be equal	${ant5PaState}	Close				
	should be equal	${ant6PaState}	Close				
	验证TDL小区	${tddCell5}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584553 共通道prru：2.6G配置NR+L载波共通道，NR进入载波关断后，闭塞LTE小区(PA关闭状态)__RAN-5584553	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	关闭NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	120					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1
	...	Carrier Shutdown	Start ES				
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	${result}	PRRU诊断测试_多模	101-instance				
	${PA}	set variable	${result['pa']}				
	${ant3PaState}	set variable	${PA[2][-1]}				
	${ant4PaState}	set variable	${PA[3][-1]}				
	${ant5PaState}	set variable	${PA[4][-1]}				
	${ant6PaState}	set variable	${PA[5][-1]}				
	should be equal	${ant3PaState}	Open				
	should be equal	${ant4PaState}	Open				
	should be equal	${ant5PaState}	Close				
	should be equal	${ant6PaState}	Close				
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	修改TDL小区管理状态	5	1				
	修改TDL小区管理状态	6	1				
	sleep	100					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Close	
	修改TDL小区管理状态	5	0				
	修改TDL小区管理状态	6	0				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	sleep	200					
	${result}	PRRU诊断测试_多模	101-instance				
	${PA}	set variable	${result['pa']}				
	${ant3PaState}	set variable	${PA[2][-1]}				
	${ant4PaState}	set variable	${PA[3][-1]}				
	${ant5PaState}	set variable	${PA[4][-1]}				
	${ant6PaState}	set variable	${PA[5][-1]}				
	should be equal	${ant3PaState}	Open				
	should be equal	${ant4PaState}	Open				
	should be equal	${ant5PaState}	Close				
	should be equal	${ant6PaState}	Close				
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584588 共通道prru：2.6G配置NR+L载波共通道，LTE进入载波关断后，删除NR小区，再新建NR小区__RAN-5584588	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
	sleep	30					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell6}					
	sleep	120					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start
	...	20	5				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Open	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	删除指定NR小区_多模	${GNODEB}	1				
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Close	
	创建指定NR小区_多模	${GNODEB}	1				
	sleep	200					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Open	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
	[Teardown]	恢复环境					
							
RAN-5584595 共通道prru：2.6G配置NR+L载波共通道，LTE进入载波关断后，闭塞NR小区(PA关闭状态)__RAN-5584595	保存NR小区信息_多模	${GNODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
	sleep	30					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell6}					
	sleep	120					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start
	...	20	5				
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Open	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	闭塞NR小区_多模	${cell1}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Close	
	解闭塞NR小区_多模	${cell1}					
	sleep	200					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Open	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
	[Teardown]	恢复环境					
							
RAN-5583926 多频段prru：各通道各制式配置NR和LTE载波，打开所有频段载波关断（其中2.6G LV配置）__RAN-5583926	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Start ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Close	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	301-instance	Close	
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Carrier Shutdown Duration(ms)	Replaceable unit ID	101	
	should be true	${result}					
	打开所有频段载波关断开关						
	sleep	240					
	关闭所有频段载波关断开关						
	sleep	90					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Open	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	301-instance	Open	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Carrier Shutdown Duration(ms)	Replaceable unit ID	101	
	should not be true	${result}					
	[Teardown]	恢复环境					
							
RAN-5583928 多频段prru：各通道各制式配置NR和LTE载波，打开4.9G TNR载波关断，对其他频段无影响（其中2.6G LV配置）__RAN-5583928	关闭NR载波关断开关	${cell4}					
	关闭NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	${cell4}					
	打开NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4
	...	Carrier Shutdown	Start ES				
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	20sec	确认4.9G TNR载波PA开关状态	101-instance	Close	
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Carrier Shutdown Duration(ms)	Replaceable unit ID	101	
	should be true	${result}					
	关闭NR载波关断开关	${cell4}					
	关闭NR载波关断开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	15min	90sec	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	4
	...	Carrier Shutdown	Stop ES				
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	20sec	确认4.9G TNR载波PA开关状态	101-instance	Open	
	sleep	200					
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Carrier Shutdown Duration(ms)	Replaceable unit ID	101	
	should not be true	${result}					
	[Teardown]	恢复环境					
							
RAN-5583930 多频段prru：各通道各制式配置NR和LTE载波，打开2.6G TNR载波关断，对其他频段无影响（其中2.6G LV配置）__RAN-5583930	关闭NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	90sec	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1
	...	Carrier Shutdown	Start ES				
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	关闭NR载波关断开关	${cell1}					
	同步规划区数据_多模	${GNODEB}					
	sleep	100					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1
	...	Carrier Shutdown	Stop ES				
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认2.6G TNR载波PA开关状态	101-instance	Open	
	sleep	200					
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5583932 多频段prru：各通道各制式配置NR和LTE载波，打开FDL载波关断, 对其他频段无影响（其中2.6G LV配置）__RAN-5583932	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	sleep	30					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认1.8G FDL载波PA开关状态	101-instance	Close	
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop
	...	20	1				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认1.8G FDL载波PA开关状态	101-instance	Open	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5583934 多频段prru：各通道各制式配置NR和LTE载波，打开TDL载波关断,对其他频段无影响（其中2.6G LV配置）__RAN-5583934	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	30					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	120					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell7}					
	sleep	100					
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop
	...	20	3				
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU的PA通道状态	101-instance	1,2	Open
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5545092 闭塞通道：进入载波关断后，闭塞所有prru通道，再解闭塞所有prru通道，关闭载波关断__RAN-5545092	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	3	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Start ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Close	
	关闭PRRU功放_多模	101-instance					
	关闭PRRU功放_多模	111-instance					
	sleep	120					
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Close	
	打开PRRU功放_多模	101-instance					
	打开PRRU功放_多模	111-instance					
	sleep	120					
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;3;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Close	
	打开所有频段载波关断开关						
	sleep	240					
	关闭所有频段载波关断开关						
	sleep	90					
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	50	2	
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	50	3	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	101-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU非节能状态	301-instance		
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	101-instance	Open	
	Wait Until Keyword Succeeds	15min	60sec	确认所有频段载波PA开关状态	301-instance	Open	
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584439 闭塞通道：闭塞prru上每个频段的部分通道，开启载波关断，再解闭塞prru通道，关闭载波关断__RAN-5584439	闭塞PRRU部分通道	301-instance	1,3,4,7,9,10				
	sleep	45					
	Wait Until Keyword Succeeds	5min	30sec	确认PRRU的PA通道状态	301-instance	1,3,4,7,9,10	Close
	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Start	100	4	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;3;0;0;0
	判断sonm节能上报是否上报成功	${GNODEB}	Carrier Shutdown ES	Carrier Shutdown	Start ES	18	
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	2	Carrier Shutdown	Start ES	100
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	1	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	解闭塞PRRU部分通道	301-instance	1,3,4,7,9,10				
	sleep	45					
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	1	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;3;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	301-instance	rfCarrierShutdown	
	打开所有频段载波关断开关						
	sleep	30					
	关闭所有频段载波关断开关						
	sleep	60					
	判断sonm节能上报是否上报成功	${GNODEB}	Carrier Shutdown ES	Carrier Shutdown	Stop ES	30	
	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	2	Carrier Shutdown	Stop ES	50
	Wait Until Keyword Succeeds	10min	60sec	确认小区节能状态	${cell2}	0	
	确认LTE sonm节能上报成功判断	${ENODEB}	Carrier Shutdown ES	ES Stop	20	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	确认LTE sonm节能上报成功判断	${GNODEB}	Carrier Shutdown ES	ES Stop	20	4	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU非节能状态	301-instance		
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584607 载波关断与工具：所有频段NR LTE进入载波关断和退出载波关断后，操作NI扫描__RAN-5584607	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开所有LTE载波关断开关						
	打开所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell1}	1	
	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell2}	0	
	${subscriptionId2}	创建小区频谱扫描任务_多模	${cell2}				
	sleep	90					
	${filePath2}	导出频谱扫描数据_多模	${GNODEB}	${subscriptionId2}			
	验证频谱扫描结果CSV文件	${filePath2}	2				
	删除小区频谱扫描任务_多模	${cell2}	${subscriptionId2}				
	[Teardown]	恢复环境					
							
RAN-5584624 载波关断与诊断：所有频段NR LTE进入载波关断和退出载波关断后，小区功率查询、查询prru实际发射功率__RAN-5584624	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开所有LTE载波关断开关						
	打开所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	360					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	1	
	${powerTx1}	查询PRRU发射功率_多模	101-instance	4			
	${cellPower1}	查询NR小区功率_多模	${cell1}				
	Wait Until Keyword Succeeds	15min	60sec	should be true	${powerTx1} < -5		
	Wait Until Keyword Succeeds	15min	60sec	should be true	${cellPower1} < -15		
	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	120					
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	${powerTx2}	查询PRRU发射功率_多模	101-instance	4			
	${cellPower2}	查询NR小区功率_多模	${cell1}				
	Wait Until Keyword Succeeds	15min	60sec	should be true	${cellPower2} > -15		
	Wait Until Keyword Succeeds	15min	60sec	should be true	${powerTx2} > -5		
	[Teardown]	恢复环境					
							
RAN-5584736 载波关断与性能测量：所有频段NR LTE进入载波关断和退出载波关断后，验证性能测量数据正常上报__RAN-5584736	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开所有LTE载波关断开关						
	打开所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell1}	1	
	#通道级载波测量						
	创建测量任务_多模	${GNODEB}	AauCarrier				
	sleep	30min					
	${result}	查询测量数据_多模	${GNODEB}	AauCarrier			
	${status}	${value}	Run Keyword And Ignore Error	Should Not Contain	${result}	no result	
	Run Keyword IF	'${status}'=='FAIL'	sleep	15min			
	${result2}	Run Keyword IF	'${status}'=='FAIL'	查询测量数据_多模	${GNODEB}	AauCarrier	
	...	ELSE	Set Variable	${result}			
	Should Not Contain	${result2}	no result	AauCarrier任务没有性能数据上报			
	#射频通道测量						
	创建测量任务_多模	${GNODEB}	AauChannel				
	sleep	30min					
	${result}	查询测量数据_多模	${GNODEB}	AauChannel			
	${status}	${value}	Run Keyword And Ignore Error	Should Not Contain	${result}	no result	
	Run Keyword IF	'${status}'=='FAIL'	sleep	15min			
	${result2}	Run Keyword IF	'${status}'=='FAIL'	查询测量数据_多模	${GNODEB}	AauChannel	
	...	ELSE	Set Variable	${result}			
	Should Not Contain	${result2}	no result	AauChannel任务没有性能数据上报			
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell2}	0	
	#通道级载波测量						
	创建测量任务_多模	${GNODEB}	AauCarrier				
	sleep	30min					
	${result}	查询测量数据_多模	${GNODEB}	AauCarrier			
	${status}	${value}	Run Keyword And Ignore Error	Should Not Contain	${result}	no result	
	Run Keyword IF	'${status}'=='FAIL'	sleep	15min			
	${result2}	Run Keyword IF	'${status}'=='FAIL'	查询测量数据_多模	${GNODEB}	AauCarrier	
	...	ELSE	Set Variable	${result}			
	Should Not Contain	${result2}	no result	AauCarrier任务没有性能数据上报			
	#射频通道测量						
	创建测量任务_多模	${GNODEB}	AauChannel				
	sleep	30min					
	${result}	查询测量数据_多模	${GNODEB}	AauChannel			
	${status}	${value}	Run Keyword And Ignore Error	Should Not Contain	${result}	no result	
	Run Keyword IF	'${status}'=='FAIL'	sleep	15min			
	${result2}	Run Keyword IF	'${status}'=='FAIL'	查询测量数据_多模	${GNODEB}	AauChannel	
	...	ELSE	Set Variable	${result}			
	Should Not Contain	${result2}	no result	AauChannel任务没有性能数据上报			
	[Teardown]	恢复环境					
							
RAN-5533410 【Qcell通用】prru预下电阶段、真实下电阶段、上电恢复正常阶段，发起PB诊断__RAN-5533410	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	prru定时下电预下电阶段	${GNODEB}	180				
	PB诊断测试_上电	PB_10-instance					
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	240					
	PB诊断测试_下电	PB_10-instance					
	prru上电阶段	${GNODEB}	900				
	PB诊断测试_上电	PB_10-instance					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5532519 【Qcell通用】复位PB：prru真实下电阶段，复位/硬复位/掉电复位PB，新机型prru会上电恢复正常，老机型会再次进入下电__RAN-5532519	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电真实下电阶段	${GNODEB}	35min				
	Comment	PRRU立即进入定时下电_多模	${VSW}				
	Comment	sleep	240				
	复位PB_多模	PB_11-instance					
	掉电复位PB_多模	PB_10-instance					
	硬复位PB_多模	PB_30-instance					
	sleep	900					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should not contain	${RRUSaveEnegyStatus}	savingElectricity	
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5533424 【Qcell通用】prru预下电阶段、真实下电阶段、上电恢复正常阶段，查询prru资产__RAN-5533424	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	prru定时下电预下电阶段	${GNODEB}	180				
	查询PRRU资产信息	${GNODEB}					
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	240					
	查询PRRU资产信息	${GNODEB}					
	prru上电阶段	${GNODEB}	900				
	查询PRRU资产信息	${GNODEB}					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5533867 【Qcell通用】prru预下电阶段、真实下电阶段、上电恢复正常阶段，观测计数器上报值__RAN-5533867	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	prru定时下电预下电阶段	${GNODEB}	180				
	PRRU电源功率测量C37016	${GNODEB}					
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	240					
	PRRU电源功率测量C37016	${GNODEB}					
	prru上电阶段	${GNODEB}	900				
	PRRU电源功率测量C37016	${GNODEB}					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5532582 【Qcell通用】复位prru：prru真实下电阶段，软复位/硬复位/掉电复位prru，返回失败__RAN-5532582	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	180				
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	240					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	\	${result}	run keyword and return status	复位PRRU_多模	${board}		
	\	should not be true	${result}				
	prru上电阶段	${GNODEB}	900				
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5496795 【Qcell4.0及之后机型】复位prru：prru预下电阶段，软复位/硬复位/掉电复位prru，返回失败__RAN-5496795	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	480				
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	\	${result}	run keyword and return status	复位PRRU_多模	${board}		
	\	should not be true	${result}				
	prru上电阶段	${GNODEB}	900				
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5496789 【Qcell4.0及之后机型】复位PB：prru预下电阶段，复位/硬复位/掉电复位PB，prru会上电恢复正常，符合下电条件后当天可再次进入下电__RAN-5496789	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	240				
	@{pbBoards}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	#复位PB单板						
	: FOR	${pbBoard}	IN	@{pbBoards}			
	\	${status}	查询单板信息_多模	${pbBoard}	operState		
	\	Run Keyword If	'${status}' == 'Normal'	复位PB_多模	${pbBoard}		
	sleep	180					
	#复位PB单板后，PRRU退出节能						
	: FOR	${i}	IN RANGE	60			
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	101-instance	availStatus		
	\	sleep	10				
	\	run keyword if	'${RRUSaveEnegyStatus}' == 'onbusiness'	exit for loop			
	should contain	${RRUSaveEnegyStatus}	onbusiness				
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5532674 【Qcell4.0及之后机型】prru预下电阶段，定时上电时间到，prru会上电恢复正常（覆盖正常prru、pb正常但prru断链的prru）__RAN-5532674	${pbEthPower}	PB网口供电功率查询	PB1125H+PB_10	1			
	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	240				
	${pbEthPower1}	PB网口供电功率查询	PB1125H+PB_10	1			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	\	should be true	${pbEthPower1} < ${pbEthPower}				
	prru定时下电真实下电阶段	${GNODEB}	40min				
	${pbEthPower2}	PB网口供电功率查询	PB1125H+PB_10	1			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	\	should be true	${pbEthPower2}==0				
	prru上电阶段	${GNODEB}	900				
	${pbEthPower3}	PB网口供电功率查询	PB1125H+PB_10	1			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	onbusiness	
	\	should be true	${pbEthPower3}!=0				
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5558283 【Qcell4.0及之后机型】人工上电prru：prru预下电阶段，网管发起人工上电，prru返回失败，节能流程继续__RAN-5558283	创建并设置PRRU节能下电持续时间	${GNODEB}	20min				
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	300				
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	\	${result}	run keyword and return status	上电PRRU_多模	${board}		
	\	should be true	${result}==False				
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	360					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	sleep	1300					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	run keywords	恢复环境				
	...	AND	复位基站_多模	${GNODEB}			
	...	AND	sleep	2400			
							
RAN-5558279 【Qcell通用】人工上电prru：prru真实下电阶段，网管发起人工上电，prru会退节能，上电恢复正常，本次下电时间区间不再进入节能__RAN-5558279	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	创建并设置PRRU节能下电持续时间	${GNODEB}	2h				
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	40min					
	#真实下电后网管下发PRRU人工上电						
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${power} == 0				
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	\	${result}	run keyword and return status	上电PRRU_多模	${board}		
	\	should be true	${result}==True				
	sleep	300					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	onbusiness	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	#本次下电区间不再进入节能						
	sleep	900					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	onbusiness	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	run keywords	恢复环境				
	...	AND	复位基站_多模	${GNODEB}			
	...	AND	sleep	2400			
							
RAN-5558282 【Qcell4.0及之后机型】人工下电prru：prru预下电阶段，网管发起人工下电，prru会立马下电，prru退出节电状态。再发起人工上电，prru运行正常__RAN-5558282	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	创建并设置PRRU节能下电持续时间	${GNODEB}	40min				
	sleep	300					
	#预下电阶段网管下发人工下电						
	: FOR	${board}	IN	@{boards}			
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	should contain	${RRUSaveEnegyStatus}	savingElectricity			
	\	${result}	run keyword and return status	下电PRRU_多模	${board}		
	\	should be true	${result}				
	Comment	Run Keyword And Continue On Failure	确认告警在当前告警中_多模	${GNODEB}	SYS	PoePoweredDown	
	sleep	60					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Abnormal'	should contain	${RRUSaveEnegyStatus}	powerOff	
	sleep	40min					
	#再发起人工上电，PRRU运行正常						
	: FOR	${board}	IN	@{boards}			
	\	run keyword and ignore error	上电PRRU_多模	${board}			
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	onbusiness	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	run keywords	恢复环境				
	...	AND	复位基站_多模	${GNODEB}			
	...	AND	sleep	2400			
							
RAN-5558281 【Qcell通用】人工下电prru：prru真实下电后，网管发起人工下电，返回成功，prru退出节电状态__RAN-5558281	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	创建并设置PRRU节能下电持续时间	${GNODEB}	50min				
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	240					
	#真实下电后网管下发人工下电						
	${power}	PB网口供电功率查询	PB1125H+PB_10	1			
	run keyword and ignore error	should be true	${power} == 0				
	: FOR	${board}	IN	@{boards}			
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	should contain	${RRUSaveEnegyStatus}	savingElectricity			
	\	${result}	run keyword and return status	下电PRRU_多模	${board}		
	\	should be true	${result}				
	Comment	Run Keyword And Continue On Failure	确认告警在当前告警中_多模	${GNODEB}	SYS	PoePoweredDown	
	sleep	300					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Abnormal'	should contain	${RRUSaveEnegyStatus}	powerOff	
	#在下电区间发起人工上电，PRRU运行正常						
	: FOR	${board}	IN	@{boards}			
	\	run keyword and ignore error	上电PRRU_多模	${board}			
	sleep	900					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	onbusiness	
	验证NR小区	${cell1}	${CPE}	${PDN}			
	[Teardown]	run keywords	恢复环境				
	...	AND	复位基站_多模	${GNODEB}			
	...	AND	sleep	2400			
							
RAN-5533125 【Qcell4.0及之后机型】prru断链后建链：prru预下电、真实下电时，prru不再上报数据，BBU存储的温度数据保留。prru上电后prru温度在半个小时后开始上报__RAN-5533125	创建PRRU定时节电参数_多模	${GNODEB}					
	修改PRRU工作日节电时间_多模	${GNODEB}	00:00	23:59			
	修改PRRU休息日节电时间_多模	${GNODEB}	00:00	23:59			
	修改定时下电温差	30					
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
	${almStart}	查询基站当前告警_多模	${GNODEB}				
	${boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	prru定时下电预下电阶段	${GNODEB}	240				
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	#添加prru不再上报数据						
	${tempList}	获取pRRU单板24小时内温度数据_多模	${VSW}	101-instance			
	should be true	${tempList}					
	prru定时下电真实下电阶段	${GNODEB}	35min				
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	Run Keyword If	'${status}' == 'Normal'	should contain	${RRUSaveEnegyStatus}	savingElectricity	
	#添加prru不再上报数据						
	${tempList}	获取pRRU单板24小时内温度数据_多模	${VSW}	101-instance			
	should be true	${tempList}					
	prru上电阶段	${GNODEB}	900				
	#添加prru继续上报数据						
	${tempList}	获取pRRU单板24小时内温度数据_多模	${VSW}	101-instance			
	should be true	${tempList}					
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${GNODEB}	${almStart}	
	验证NR小区_多模	${GNODEB}	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5532727 【Qcell4.0及之后机型】prru删除：BBU存储的温度数据清除__RAN-5532727	#确认BBU存储的温度数据是正常且有数据						
	${tempList}	获取pRRU单板24小时内温度数据_多模	${VSW}	101-instance			
	should be true	${tempList}					
	删除真实PRRU_多模	101-instance					
	sleep	600					
	#确认BBU存储的温度数据是否清除						
	${tempList}	获取pRRU单板24小时内温度数据_多模	${VSW}	101-instance			
	Comment	should contain	${tempList}	no data			
	[Teardown]	恢复环境					
							
RAN-5584742 各通道各制式配置NR和LTE载波，多次启动和退出NR LTE载波关断后，NR LTE业务KPI指标不受影响__RAN-5584742	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开所有LTE载波关断开关						
	打开所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell1}	1	
	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	10min					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell2}	0	
	#业务验证						
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
							
RAN-5585258 各通道各制式配置NR和LTE载波，多次启动和退出NR LTE符号关断后，NR LTE业务KPI指标不受影响__RAN-5585258	获取所有小区别名						
	ITRAN-LTE-FDD符号关断开关	0					
	ITRAN-LTE-TDD符号关断开关	0					
	关闭所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	ITRAN-LTE-FDD符号关断开关	1					
	ITRAN-LTE-TDD符号关断开关	1					
	打开所有NR小区符号关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell1}	3	
	关闭所有LTE载波关断开关						
	关闭所有NR小区载波关断开关	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	10min					
	Wait Until Keyword Succeeds	15min	90sec	确认小区节能状态	${cell2}	0	
	#业务验证						
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
							
符号关断	关闭所有频段符号关断开关						
	sleep	30					
	打开所有频段符号关断开关						
	sleep	60					
	Comment	#计数器C37058					
	Comment	创建测量任务_多模	${GNODEB}	RruEnergy			
	Comment	sleep	60				
	Comment	${measureIdList}	查询基站已有测量任务_多模	${GNODEB}			
	Comment	同步基站测量任务_多模	${GNODEB}	${measureIdList}			
	Comment	同步基站时间_多模	${GNODEB}				
	Comment	${queryModleDict}	create dictionary	5465_RruEnergy_query=me,Equipment,ReplaceableUnit			
	Comment	${resPathList1}	按模板任务获取历史性能指标数据	${queryModleDict}	15		
	Comment	Comment	${result1}	按过滤条件读取csv指定列的指标值	@{resPathList1}[0]	RRU Energy-Saving Duration(ms)	Replaceable unit ID
	...	101					
	Comment	Comment	${result2}	按过滤条件读取csv指定列的指标值	@{resPathList1}[0]	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID
	...	101					
	Comment	Comment	log	${result1}			
	Comment	Comment	log	${result2}			
	Comment	Comment	${float_result1}	Evaluate	float('${result1[0]}')		
	Comment	Comment	${float_result2}	Evaluate	float('${result2[0]}')		
	Comment	Comment	log	${float_result1}			
	Comment	Comment	log	${float_result2}			
	Comment	Comment	should be true	${float_result1} > 0			
	Comment	Comment	should be true	${float_result2} > 0			
	Comment	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128		
	Comment	: FOR	${board}	IN	@{boards}		
	Comment	${EMPTY}	${prruMoid}	evaluate	'${board}'.split('-')[0]		
	Comment	${EMPTY}	${result1}	按过滤条件读取csv指定列的指标值	@{resPathList1}[0]	RRU Energy-Saving Duration(ms)	Replaceable unit ID
	...	${prruMoid}					
	Comment	${EMPTY}	${result2}	按过滤条件读取csv指定列的指标值	@{resPathList1}[0]	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID
	...	${prruMoid}					
	Comment	${EMPTY}	log	${result1}			
	Comment	${EMPTY}	log	${result2}			
	Comment	${EMPTY}	${float_result1}	Evaluate	float('${result1[0]}')		
	Comment	${EMPTY}	${float_result2}	Evaluate	float('${result2[0]}')		
	Comment	${EMPTY}	log	${float_result1}			
	Comment	${EMPTY}	log	${float_result2}			
	Comment	${EMPTY}	should be true	${float_result1} > 0			
	Comment	${EMPTY}	should be true	${float_result2} > 0			
	Comment	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128		
	Comment	: FOR	${board}	IN	@{boards}		
	Comment	${EMPTY}	${prruMoid}	evaluate	'${board}'.split('-')[0]		
	Comment	${EMPTY}	验证C37058计数器有非0值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	${prruMoid}
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	101	
	should be true	${result}					
	打开所有频段符号关断开关						
	sleep	30					
	关闭所有频段符号关断开关						
	sleep	60					
	Comment	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128		
	Comment	: FOR	${board}	IN	@{boards}		
	Comment	${EMPTY}	${prruMoid}	evaluate	'${board}'.split('-')[0]		
	Comment	${EMPTY}	验证C37058计数器有0值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	${prruMoid}
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU OFDM Shutdown Duration(ms)	Replaceable unit ID	101	
	should not be true	${result}					
	[Teardown]	恢复环境					
							
test - copy1	${input_string}	Set Variable	311-instance				
	${prruMoid}	evaluate	'${input_string}'.split('-')[0]				
	${instance}	evaluate	'${input_string}'.split('-')[-1]				
	[Teardown]						
							
载波关断	关闭所有频段载波关断开关						
	sleep	30					
	打开所有频段载波关断开关						
	sleep	900					
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Carrier Shutdown Duration(ms)	Replaceable unit ID	101	
	should be true	${result}					
	打开所有频段载波关断开关						
	sleep	30					
	关闭所有频段载波关断开关						
	sleep	600					
	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Carrier Shutdown Duration(ms)	Replaceable unit ID	101	
	should not be true	${result}					
	[Teardown]						
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名_5824						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
打开NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
打开NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
ITRAN-LTE-FDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-FDD-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
打开所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR符号关断开关	${cell}				
							
关闭所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR符号关断开关	${cell}				
							
打开ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR载波关断开关	${cell}				
							
关闭所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR载波关断开关	${cell}				
							
恢复环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	90					
							
恢复等待环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	600					
							
关闭所有LTE载波关断开关	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
打开所有LTE载波关断开关	打开ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
prru定时下电预下电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
	sleep	${delayTime}					
							
prru定时下电真实下电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
	sleep	${delayTime}					
							
prru上电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	0				
	sleep	${delayTime}					
							
PB诊断测试_上电	[Arguments]	${pbAlias}	${port}=1				
	#资源利用率信息查询						
	${result}	查询PB资源使用率_多模	${VSW}				
	should be true	0< ${result[0]} <80					
	${total}	evaluate	${result[4]}+${result[5]}				
	should be true	${total}==100					
	#PB单板运行时间						
	${result}	查询PB工作时长_多模	${pbAlias}				
	should be true	0 < ${result}					
	#PB光/电模块诊断						
	${result}	PB光电模块诊断_多模	${pbAlias}	${port}			
	should be true	'${result[1]}'=='Success'					
	should be true	'${result[3]}'=='SFP Transceiver'					
	#PB以太网口状态检测						
	${result}	PB以太网口状态检测_多模	${pbAlias}	${port}			
	Should contain	${result}	HalfDuplex				
	#PB以太网口误码率诊断						
	${result}	PB以太网口误码率诊断_多模	${pbAlias}	${port}			
	should be true	'${result}'=='0'					
	#PB以太网口通信检测						
	${result}	PB以太网口SNR诊断_多模	${pbAlias}	${port}			
	should be true	'${result}'=='0'					
	#光/电误码率诊断						
	${result}	PB光口误码率诊断_多模	${pbAlias}	${port}			
	should be true	'${result}'=='0'					
	#PB光口状态检测						
	${result}	PB光口状态诊断_多模	${pbAlias}	${port}			
	should be true	'${result[1]}'=='In Position'					
	#PB单板测试						
	${result}	PB诊断测试_多模	${pbAlias}				
	should be true	0< ${result} <90					
	#POE供电状态检测						
	${result}	PB供电状态检测_多模	${pbAlias}				
	should be true	'${result[1]}'=='Normal'					
	#PB光纤测距						
	${result}	PB光纤测距_多模	${pbAlias}				
	should be true	'${result[2]}'=='Shorter Than 200 Meters'					
							
PB诊断测试_下电	[Arguments]	${pbAlias}					
	#资源利用率信息查询						
	${result}	查询PB资源使用率_多模	${VSW}				
	should be true	0< ${result[0]} <80					
	${total}	evaluate	${result[4]}+${result[5]}				
	should be true	${total}==100					
	#PB单板运行时间						
	${result}	查询PB工作时长_多模	${pbAlias}				
	should be true	0 < ${result}					
	#PB光/电模块诊断						
	${result}	PB光电模块诊断_多模	${pbAlias}				
	should be true	'${result[1]}'=='Success'					
	should be true	'${result[3]}'=='SFP Transceiver'					
	#PB以太网口状态检测						
	${result}	PB以太网口状态检测_多模	${pbAlias}				
	Should contain	${result}	HalfDuplex				
	#PB以太网口误码率诊断						
	${result}	PB以太网口误码率诊断_多模	${pbAlias}				
	Should contain	${result}	No Light/Electricity in Optical/Electric Port				
	#PB以太网口通信检测						
	${result}	PB以太网口SNR诊断_多模	${pbAlias}				
	Should contain	${result}	No Light/Electricity in Optical/Electric Port				
	#光/电误码率诊断						
	${result}	PB光口误码率诊断_多模	${pbAlias}				
	should be true	'${result}'=='0'					
	#PB光口状态检测						
	${result}	PB光口状态诊断_多模	${pbAlias}				
	should be true	'${result[1]}'=='In Position'					
	#PB单板测试						
	${result}	PB诊断测试_多模	${pbAlias}				
	should be true	0< ${result} <90					
	#POE供电状态检测						
	${result}	PB供电状态检测_多模	${pbAlias}				
	should be true	'${result[1]}'=='Unknown'					
	#PB光纤测距						
	${result}	PB光纤测距_多模	${pbAlias}				
	should be true	'${result[2]}'=='Shorter Than 200 Meters'					
							
查询PRRU资产信息	[Arguments]	${gnbAlias}					
	重复执行_多模	3	同步基站资产信息_多模	${gnbAlias}			
	获取基站硬件资产信息_多模	${gnbAlias}					
	@{boards}	根据类型获取实例化单板别名_多模	${gnbAlias}	256			
	: FOR	${board}	IN	@{boards}			
	\	${result}	获取单板硬件资产信息_多模	${board}			
	\	确认硬件资产上报正常_多模	${board}	${result}			
							
PRRU电源功率测量C37016	[Arguments]	${gnbAlias}					
	同步基站时间_多模	${gnbAlias}					
	${nameList}	create list	PrruPwr				
	${queryModleDict}	create dictionary	5818_prrupower_query=me,Equipment,ReplaceableUnit				
	${resPathList}	性能测量测试模板	${nameList}	${queryModleDict}			
	${colList}	create list	13				
	读取csv数据平均值，判断有值	@{resPathList}[0]	${colList}				
							
性能测量测试模板	[Arguments]	${nameList}	${queryModleDict}	${filterlayer}=me	${filterlayer2}=me		
	${time}	查询基站时间_多模	${GNODEB}				
	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	30		
	: FOR	${name}	IN	@{nameList}			
	\	创建测量任务_多模	${GNODEB}	${name}			
	sleep	35min					
	${filePathList}	create list					
	: FOR	${queryModle}	IN	@{queryModleDict}			
	\	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}		
	\	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}
	...	${endTime}	${filterlayer2}	900			
	\	append to list	${filePathList}	${filePath}			
	[Teardown]	清除测量任务	@{nameList}				
	[Return]	${filePathList}					
							
清除测量任务	[Arguments]	@{nameList}					
	: FOR	${name}	IN	@{nameList}			
	\	run keyword and continue on failure	删除测量任务_多模	${GNODEB}	${name}		
							
读取csv数据平均值，判断有值	[Arguments]	${filePath}	${colList}				
	${resList}	create list					
	: FOR	${col}	IN	@{colList}			
	\	${ave}	读取csv列数据平均值_多模	${filePath}	${col}		
	\	should be true	${ave}				
	\	append to list	${resList}	${ave}			
	[Return]	${resList}					
							
创建并设置PRRU节能下电持续时间	[Arguments]	${gnbAlias}	${delay}				
	创建PRRU定时节电参数_多模	${gnbAlias}					
	${time}	查询基站时间_多模	${GNODEB}				
	Comment	${a}	set variable	${time}			
	${tmp}	split string	${time}	T			
	${tmp}	split string	${tmp[1]}	:			
	${timeStart}	set variable	${tmp[0]}:${tmp[1]}				
	${timeEnd}	获取延时后的时间点	${delay}				
	修改PRRU工作日节电时间_多模	${gnbAlias}	${timeStart}	${timeEnd}			
	修改PRRU休息日节电时间_多模	${gnbAlias}	${timeStart}	${timeEnd}			
	修改定时下电温差	30					
	同步基站时间_多模	${gnbAlias}					
	同步测试机时间_多模	${gnbAlias}					
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
							
关闭所有频段符号关断开关	#关闭FDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	#关闭TDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	#关闭SON节能配置						
	${attr}	create dictionary	esDTXSwitch=0	notifyBBFlag=1	weekdayDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段符号关断开关	#FDD和TDD制式符号关断开关打开						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
关闭所有频段载波关断开关	#LTE制式载波关断总开关关闭						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段载波关断开关	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
合并开站文件并开站	[Arguments]	${enbAlias}	${filePath}				
	${version}	查询基站运行版本号_多模	${enbAlias}	SOFTWARE			
	${tarName}	查询TAR包名称_多模	${enbAlias}	${version}			
	${tarName}	run keyword if	'${tarName}'==''	set variable	UNI_${version}.tar		
	...	ELSE	set variable	${tarName}			
	${filePathNew}	导出开站模板文件_多模	${enbAlias}	${tarName}			
	${filePath}	合并开站模板文件_多模	${filePathNew}	${filePath}			
	按开站模板开站_多模	${enbAlias}	${filePath}	${version}			
	sleep	600					
							
性能数据测量测试模板	[Arguments]	${name1}	${name2}				
	${names}	create list	${name1}	${name2}			
	: FOR	${name}	IN	@{names}			
	\	创建测量任务_多模	${GNODEB}	${name}			
	sleep	30min					
	: FOR	${name}	IN	@{names}			
	\	${result}	查询测量数据_多模	${GNODEB}	${name}		
	\	${status}	${value}	Run Keyword And Ignore Error	Should Not Contain	${result}	no result
	\	Run Keyword IF	'${status}'=='FAIL'	sleep	15min		
	\	${result2}	Run Keyword IF	'${status}'=='FAIL'	查询测量数据_多模	${GNODEB}	${name}
	...	ELSE	Set Variable	${result}			
	\	Should Not Contain	${result2}	no result	${name}任务没有性能数据上报		
