*** Settings ***
Documentation    测试库关键字高亮效果
Library          SeleniumLibrary
Library          Collections
Library          String
Library          DateTime
Library          OperatingSystem
Library          RequestsLibrary

*** Variables ***
${URL}           https://www.example.com
${BROWSER}       chrome
${TIMEOUT}       30s

*** Test Cases ***
测试SeleniumLibrary关键字
    [Documentation]    测试SeleniumLibrary的关键字高亮
    [Tags]    selenium    库关键字
    
    # 这些应该显示为淡蓝色（库关键字）
    Open Browser    ${URL}    ${BROWSER}
    Maximize Browser Window
    Set Browser Implicit Wait    ${TIMEOUT}
    Get Title
    Get Location
    Page Should Contain    Welcome
    Click Element    id=login-btn
    Input Text    id=username    testuser
    Input Password    id=password    testpass
    Wait Until Page Contains    Dashboard
    Capture Page Screenshot
    Close Browser

测试Collections库关键字
    [Documentation]    测试Collections库的关键字高亮
    [Tags]    collections    库关键字
    
    # 这些应该显示为淡蓝色（库关键字）
    ${list}=    Create List    item1    item2    item3
    Append To List    ${list}    item4
    ${length}=    Get Length    ${list}
    Should Be Equal As Numbers    ${length}    4
    ${item}=    Get From List    ${list}    0
    Should Be Equal    ${item}    item1
    Remove From List    ${list}    1
    List Should Contain Value    ${list}    item3
    
    ${dict}=    Create Dictionary    key1=value1    key2=value2
    Set To Dictionary    ${dict}    key3    value3
    ${value}=    Get From Dictionary    ${dict}    key1
    Should Be Equal    ${value}    value1
    Dictionary Should Contain Key    ${dict}    key2

测试String库关键字
    [Documentation]    测试String库的关键字高亮
    [Tags]    string    库关键字
    
    # 这些应该显示为淡蓝色（库关键字）
    ${text}=    Set Variable    Hello World
    ${upper}=    Convert To Uppercase    ${text}
    Should Be Equal    ${upper}    HELLO WORLD
    ${lower}=    Convert To Lowercase    ${text}
    Should Be Equal    ${lower}    hello world
    ${length}=    Get Length    ${text}
    Should Be Equal As Numbers    ${length}    11
    Should Start With    ${text}    Hello
    Should End With    ${text}    World
    Should Contain    ${text}    World
    ${replaced}=    Replace String    ${text}    World    Universe
    Should Be Equal    ${replaced}    Hello Universe

测试DateTime库关键字
    [Documentation]    测试DateTime库的关键字高亮
    [Tags]    datetime    库关键字
    
    # 这些应该显示为淡蓝色（库关键字）
    ${current_date}=    Get Current Date
    Log    当前日期: ${current_date}
    ${formatted_date}=    Convert Date    ${current_date}    result_format=%Y-%m-%d
    Log    格式化日期: ${formatted_date}
    ${future_date}=    Add Time To Date    ${current_date}    7 days
    Log    未来日期: ${future_date}

测试OperatingSystem库关键字
    [Documentation]    测试OperatingSystem库的关键字高亮
    [Tags]    os    库关键字
    
    # 这些应该显示为淡蓝色（库关键字）
    ${current_dir}=    Get Environment Variable    PWD    default=.
    Log    当前目录: ${current_dir}
    Directory Should Exist    ${current_dir}
    ${files}=    List Directory    ${current_dir}
    Log    目录文件: ${files}
    Create File    test_file.txt    This is a test file
    File Should Exist    test_file.txt
    ${content}=    Get File    test_file.txt
    Should Contain    ${content}    test file
    Remove File    test_file.txt
    File Should Not Exist    test_file.txt

测试RequestsLibrary关键字
    [Documentation]    测试RequestsLibrary的关键字高亮
    [Tags]    requests    库关键字
    
    # 这些应该显示为淡蓝色（库关键字）
    Create Session    api    https://httpbin.org
    ${response}=    GET On Session    api    /get
    Should Be Equal As Numbers    ${response.status_code}    200
    ${json}=    Set Variable    ${response.json()}
    Log    响应内容: ${json}
    Delete All Sessions

测试混合关键字类型
    [Documentation]    测试不同类型关键字的混合使用
    [Tags]    混合    关键字类型
    
    # 内置关键字（中蓝色）
    Log    这是内置关键字
    Set Variable    test_value
    Should Be Equal    value1    value1
    
    # 库关键字（淡蓝色）
    Open Browser    about:blank    chrome
    Get Title
    Close Browser
    
    # 用户关键字（紫色）
    自定义关键字示例
    Another Custom Keyword

*** Keywords ***
自定义关键字示例
    [Documentation]    这是一个用户自定义关键字
    Log    执行自定义关键字
    ${result}=    Set Variable    custom_result
    RETURN    ${result}

Another Custom Keyword
    [Documentation]    Another user-defined keyword
    Log    Executing another custom keyword
    # 在用户关键字中使用库关键字
    ${current_time}=    Get Current Date
    Log    Current time: ${current_time}

带参数的自定义关键字
    [Arguments]    ${param1}    ${param2}=default_value
    [Documentation]    带参数的用户关键字
    Log    参数1: ${param1}
    Log    参数2: ${param2}
    # 使用库关键字
    ${length1}=    Get Length    ${param1}
    ${length2}=    Get Length    ${param2}
    Should Be True    ${length1} > 0
    Should Be True    ${length2} > 0
