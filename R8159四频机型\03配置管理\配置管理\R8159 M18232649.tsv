*Settings*							
Suite Setup	加载配置	TestRequest	${dataset}				
Suite Teardown	释放配置						
Resource	../../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../variable/resource.tsv						
Resource	../../../../template.tsv						
Variables	ConfigRequest.py						
Resource	../template.tsv						
Library	testlib5g.app_service.basic_multi.radio.eutran_common.EUtranCellModifyService						
Library	testlib5g.app_service.basic_multi.board.vsw.VswCommandService						
Resource	../../5818template.tsv						
Library	testlib5g.app_service.basic_multi.radio.dynamic.QcellRadioService						
							
*Variables*							
${StopOnError}	${False}	# 遇到测试失败继续测试，请设置为${False}；遇到测试失败中断测试，请设置为${True}					
							
*Test Cases*							
RAN-5562410 【Qcell】R8159 *********机型载波配置下，LTE带宽改配__RAN-5562410	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.5W+3T*2T2R*20M*0.1W)@2.6G	(3T*2T2R*10M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	5min					
	同步规划区数据_多模	${GNODEB}					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	验证NR小区	nrCell-1	${CPE2}	${PDN}			
	验证TDL小区业务	tddCell-1	${CPE4}	${PDN}			
	Comment	验证FDL小区业务	fddCell-7	${CPE4}	${PDN}		
	#修改2.3G小区频点						
	${TDDAlias}	获取TDD小区别名_多模	${ENODEB}				
	${freq1}	查询EUtran小区频点_多模	tddCell-5				
	${freq2}	查询EUtran小区频点_多模	tddCell-6				
	修改TDL小区频点	5	tddCell-5	${freq2}			
	${newFreq}	evaluate	${freq2}+20				
	修改TDL小区频点	6	tddCell-6	${newFreq}			
	同步规划区数据_多模	${GNODEB}					
	#修改带宽1						
	修改LTE小区10M带宽	1					
	修改LTE小区15M带宽	6					
	修改FDL小区10M带宽	7					
	sleep	1min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	验证NR小区	nrCell-1	${CPE2}	${PDN}			
	验证TDL小区业务	tddCell-1	${CPE4}	${PDN}			
	Comment	验证FDL小区业务	fddCell-7	${CPE4}	${PDN}		
	#修改带宽2						
	修改LTE小区15M带宽	1					
	修改LTE小区10M带宽	6					
	修改FDL小区15M带宽	7					
	sleep	1min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	验证NR小区	nrCell-1	${CPE2}	${PDN}			
	验证TDL小区业务	tddCell-1	${CPE4}	${PDN}			
	验证FDL小区业务	fddCell-7	${CPE4}	${PDN}			
	#修改带宽3						
	run keyword and ignore error	修改LTE采样速率模式配置_多模	tddCell-1	2			
	run keyword and ignore error	修改LTE采样速率模式配置_多模	tddCell-2	2			
	run keyword and ignore error	修改LTE采样速率模式配置_多模	tddCell-3	2			
	同步规划区数据_多模	${GNODEB}					
	run keyword and ignore error	修改LTE小区20M带宽	1				
	run keyword and ignore error	修改LTE小区20M带宽	6				
	run keyword and ignore error	修改FDL小区20M带宽	7				
	sleep	1min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	run keyword and ignore error	验证NR小区	nrCell-1	${CPE2}	${PDN}		
	run keyword and ignore error	验证TDL小区业务	tddCell-1	${CPE4}	${PDN}		
	Comment	run keyword and ignore error	验证FDL小区业务	fddCell-7	${CPE4}	${PDN}	
	[Teardown]	恢复环境					
							
RAN-5562412 【Qcell】R8159 *********机型帧头调整__RAN-5562412	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	无线帧偏移	1	11520	0	11520	0	
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	sleep	5min					
	#1prru诊断测试						
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['new']}				
	${result1}	set variable	@{result}[0]				
	${result}	set variable	@{result1}[1]				
	should contain	${result}	0				
	${result}	set variable	${rlt['new']}				
	${result1}	set variable	@{result}[2]				
	${result}	set variable	@{result1}[1]				
	should contain	${result}	11520				
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#无线帧偏移						
	无线帧偏移	1	8832	2688	8832	2688	
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	sleep	5min					
	#1prru诊断测试						
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['new']}				
	${result1}	set variable	@{result}[0]				
	${result}	set variable	@{result1}[1]				
	should contain	${result}	2688				
	${result}	set variable	${rlt['new']}				
	${result1}	set variable	@{result}[2]				
	${result}	set variable	@{result1}[1]				
	should contain	${result}	8832				
	无线帧偏移	1	8832	2688	11520	0	
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	sleep	5min					
	#1prru诊断测试						
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['new']}				
	${result1}	set variable	@{result}[0]				
	${result}	set variable	@{result1}[1]				
	should contain	${result}	0				
	${result}	set variable	${rlt['new']}				
	${result1}	set variable	@{result}[2]				
	${result}	set variable	@{result1}[1]				
	should contain	${result}	8832				
	[Teardown]	恢复环境					
							
RAN-5562426 【Qcell】R8159 *********机型诊断__RAN-5562426	log	上一条已验证					
	[Teardown]	恢复环境					
							
RAN-5562427 【Qcell】R8159 *********机型载波配置下，NR小区闭塞解闭塞__RAN-5562427	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	@{cellAlias}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellAlias}			
	\	确认NR小区状态正常_多模	${cell}				
	#4.9G闭塞						
	闭塞NR小区_多模	${cell6}					
	sleep	6min					
	#3小区&业务验证						
	闭塞后验证NR小区	${cell7}	${CPE2}	${PDN}			
	#闭塞4.9G所有NR小区						
	闭塞NR小区_多模	${cell6}					
	闭塞NR小区_多模	${cell7}					
	sleep	6min					
	#解闭塞4.9G所有NR小区						
	解闭塞NR小区_多模	${cell6}					
	解闭塞NR小区_多模	${cell7}					
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3小区&业务验证						
	闭塞后验证NR小区	${cell1}	${CPE}	${PDN}			
	闭塞后验证NR小区	${cell4}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5562428 【Qcell】R8159 *********机型载波配置下，NR小区删建__RAN-5562428	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#删除2.6GNR/LTE载波						
	删建全部NR小区_多模	${VSW}					
	sleep	8min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#小区&业务验证						
	sleep	5min					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5562429 【Qcell】R8159 *********机型载波配置下，帧结构改配__RAN-5562429	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.8W)@2.6G			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#修改基站2.6G小区帧结构						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	修改帧结构为ms2p5单周期	@{cellList}[0]					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	修改帧结构为ms2p5双周期	@{cellList}[0]					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#修改基站2.6G/4.9G小区帧结构						
	修改帧结构为5ms单周期	@{cellList}[0]					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#修改基站2.6G/4.9G小区帧结构						
	修改小区为ms2p5双周期1D3U帧结构	@{cellList}[0]					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#修改基站2.6G小区帧结构						
	修改小区为3+2ms双周期帧结构	@{cellList}[0]					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#验证业务						
	闭塞后验证NR小区	@{cellList}[0]	${CPE}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5562430 【Qcell】R8159 *********机型载波配置下，LTE小区删建__RAN-5562430	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#LTE小区删除部分						
	删建FDD小区	${ENODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#业务验证						
	#3小区&业务验证						
	闭塞后验证NR小区	${cell1}	${CPE}	${PDN}			
	闭塞后验证NR小区	${cell7}	${CPE2}	${PDN}			
	验证FDL小区	fddCell460-11_10882-01	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5562431 【Qcell】R8159 *********机型载波配置下，LTE小区闭塞解闭塞__RAN-5562431	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#闭塞其中1个小区						
	${tdds}	获取TDD小区别名_多模	${GNODEB}				
	关断EUtran小区_多模	tddCell5					
	#业务验证						
	Comment	验证FDL小区业务	${fddCell1}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell4}	${CPE4}	${PDN}		
	#闭塞全部LTE小区						
	关断EUtran小区_多模	tddCell5					
	关断EUtran小区_多模	tddCell6					
	#业务验证						
	Comment	验证NR小区	${cell1}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell7}	${CPE2}	${PDN}		
	#解闭塞全部LTE小区						
	解关断EUtran小区_多模	tddCell5					
	解关断EUtran小区_多模	tddCell6					
	sleep	5min					
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#业务验证						
	Comment	验证FDL小区业务	${fddCell1}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell4}	${CPE4}	${PDN}		
	[Teardown]	恢复环境					
							
RAN-5562432 【Qcell】R8159 *********机型在4.9G和2.6G频段NR小区频点带宽改配__RAN-5562432	${almStart}	查询基站当前告警_多模	${ENODEB}				
	#修改4.9G频点和带宽						
	${cell}	获取NR小区别名_多模	${GNODEB}				
	修改TNR小区频点	${cell7}	4930.5				
	模板修改NR小区带宽_多模	${GNODEB}	${cell7}	60	162		
	#修改2.6G频点和带宽						
	${freq1}	查询NR小区频点_多模	${cell1}				
	修改TNR小区频点	${cell1}	2568.3				
	模板修改NR小区带宽_多模	${GNODEB}	${cell1}	60	162		
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5562434 【Qcell】R8159 *********机型载波配置下，各频段通道闭塞解闭塞__RAN-5562434	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#闭塞2.6G所有通道						
	关闭PRRU功放_多模	${prru}	3,4,5,6				
	#业务验证						
	验证FDL小区	fddCell460-11_10882-01	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	#解闭塞2.6G所有通道						
	打开PRRU功放_多模	${prru}	3,4,5,6				
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#业务验证						
	Comment	验证FDL小区	${fddCell1}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}		
	Comment	验证NR小区	${cell1}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell7}	${CPE2}	${PDN}		
	#闭塞4.9所有通道						
	关闭PRRU功放_多模	${prru}	8,9,10,11				
	#业务验证						
	Comment	验证FDL小区	${fddCell1}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}		
	Comment	验证NR小区	${cell1}	${CPE}	${PDN}		
	#解闭塞4.9所有通道						
	打开PRRU功放_多模	${prru}	8,9,10,11				
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#业务验证						
	Comment	验证FDL小区	${fddCell1}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}		
	Comment	验证NR小区	${cell1}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell7}	${CPE2}	${PDN}		
	[Teardown]	恢复环境					
							
RAN-5562442 【Qcell】R8159 *********载波支持能力4.9G(NR100M+60M)+ 2.6G (NR100M+60M) ****G（TDL 3x20M+10M）+1.8G LTE20M__RAN-5562442	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP8}	${PRRUS}	100
	...	0	3,4,5,6	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP8}	${PRRUS}	60
	...	100	3,4,5,6	${True}	${False}		
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.1W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	6min					
	同步规划区数据_多模	${GNODEB}					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5562446 【Qcell】R8159 *********载波支持能力4.9G(NR100M+60M) + 2.6G (NR100M+3TDL 20M)****G （TDL 3x20M+10M）+1.8G LTE20M__RAN-5562446	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	Comment	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP8}	${PRRUS}
	...	100	0	3,4,5,6	${True}	${False}	
	: FOR	${radioString}	IN	(3T*2T2R*20M*0.05W+1V*4T4R*100M*0.5)@2.6G	(3T*2T2R*10M*0.1W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5562447 【Qcell】R8159 *********载波支持能力4.9G(NR 80M+80M)+ 2.6G( NR80M+80M)+ 2.3G（TDL 3x20M+10M）+ 1.8GFDL 20M__RAN-5562447	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	80
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	80
	...	80	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP8}	${PRRUS}	80
	...	0	3,4,5,6	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP8}	${PRRUS}	80
	...	80	3,4,5,6	${True}	${False}		
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.1W+1T*2T2R*10M*0.1W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5562448 【Qcell】R8159 *********载波支持能力4.9G（NR80M+80M）+ 2.6G（NR100M+3xTDL 2TR 20M）+ 2.3G（TDL 3x20M+10M）+ 1.8GFDL 20M__RAN-5562448	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	80
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	80
	...	80	9,10,11,12	${True}	${False}		
	Comment	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP8}	${PRRUS}
	...	100	0	3,4,5,6	${True}	${False}	
	: FOR	${radioString}	IN	(2T*2T2R*20M*0.05W+1V*4T4R*100M*0.5)@2.6G	(2T*2T2R*10M*0.1W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5562528 【Qcell】R8159 *********机型4.9G和2.6GNR制式载波劈裂功能验证__RAN-5562528	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP8}	${PRRUS}	100
	...	0	3,4,5,6	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP8}	${PRRUS}	60
	...	100	3,4,5,6	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	sleep	4min					
	Wait Until Keyword Succeeds	20min	60sec	确认NR小区状态正常_多模	${cell3}		
	Wait Until Keyword Succeeds	20min	60sec	确认NR小区状态正常_多模	${cell4}		
	#闭塞2.6G所有通道						
	关闭PRRU功放_多模	${prru}	3,4,5,6				
	sleep	2min					
	打开PRRU功放_多模	${prru}	3,4,5,6				
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5637486 【Qcell】R8159 *********机型4.9G通道劈裂后，闭塞解闭塞通道__RAN-5637486	log	上一条已验证					
	[Teardown]	恢复环境					
							
RAN-5637680 【Qcell】R8159 *********机型2.6G通道劈裂后，闭塞解闭塞通道__RAN-5637680	log	上一条已验证					
	[Teardown]	恢复环境					
							
RAN-5062058 【Qcell】单PB满配R8159 *********光电复合缆拉远验证__RAN-5062058	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PRRU2}	VBP光口下配PB、PRRU	OF2	1	7	${VBP8}	
	: FOR	${board}	IN	@{PRRU2}			
	\	run keyword and continue on failure	修改PRRU功能模式_多模	${board}	16;32;8192	1090519041	
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.8W)@4.9G	(2T*2T2R*20M*0.2W+1T*2T2R*10M*0.1W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	@{PRRU2}[0]		
	\	创建Qcell多模小区_多模	${PRRU2}	${freqDict}			
	sleep	8min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	复位所有PB						
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	恢复环境					
							
RAN-5249910 【RT】PB负荷分担双上联跨基带板，部分TNR小区一级交换改配为二级交换__RAN-5249910	log	下一条已覆盖					
	[Teardown]	恢复环境2					
							
RAN-5251511 【RT】PB负荷分担双上联跨基带板，部分TNR小区二级交换到非直连基带板上的场景下，分别复位每个基带板__RAN-5251511	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	sleep	8min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#复位基带板						
	掉电复位VBP_多模	${VBP7}					
	掉电复位VBP_多模	${VBP8}					
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#修改4.9G频点和带宽						
	${cell}	获取NR小区别名_多模	${GNODEB}				
	模板修改NR小区带宽_多模	${GNODEB}	${cell6}	60	162		
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境2					
							
RAN-5251645 【RT】PB负荷分担双上联跨基带板，部分TNR小区二级交换到非直连基带板上的场景下，修改TNR小区带宽，TNR小区均衡分配验证__RAN-5251645	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	sleep	8min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#复位基带板						
	掉电复位VBP_多模	${VBP7}					
	掉电复位VBP_多模	${VBP8}					
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#修改4.9G频点和带宽						
	${cell}	获取NR小区别名_多模	${GNODEB}				
	模板修改NR小区带宽_多模	${GNODEB}	${cell7}	60	162		
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#3业务验证						
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境2					
							
RAN-5291464 【Qcell】多次拔插光纤VBP和PB之间的光纤__RAN-5291464	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	: FOR	${i}	IN RANGE	3			
	\	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3			
	\	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6			
	\	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.5			
	\	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.6-5.1.6			
	sleep	20min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	验证FDL小区	fddCell460-11_10882-01	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5291565 【Qcell】PB光纤测距__RAN-5291565	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${PB1125H}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${res}	PB光纤测距_多模	${PB1125H}				
	Wait Until Keyword Succeeds	5min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5291662 【Qcell】pRRU性能管理__RAN-5291662	log	RAN-5585255已实现					
	[Teardown]	恢复环境					
							
RAN-5291678 【Qcell】频谱扫描__RAN-5291678	#NI扫描						
	${niResultA}	STA获取NI值	${cell1}				
	#NI扫描						
	打开上行增强开关_多模	${ENODEB}					
	同步规划区数据_多模	${ENODEB}					
	sleep	30					
	${niResultA}	STA获取NI值	${cell1}				
	[Teardown]	恢复环境					
							
RAN-5311903 【RT】PB双上联负荷分担跨基带板，小区配置在直连基带板上，分别插拔两路光纤__RAN-5311903	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	${nrCells}	获取NR小区别名_多模	${ENODEB}				
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	sleep	10min					
	Wait Until Keyword Succeeds	20min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	: FOR	${i}	IN RANGE	3			
	\	删除某个端口的光交换连接_多模	${FIBERCONNECT}	1.4.4			
	\	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2			
	\	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1			
	\	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.2-5.1.2			
	sleep	10min					
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#业务验证						
	Comment	验证NR小区	${cell2}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell7}	${CPE2}	${PDN}		
	Comment	验证FDL小区	${fddCell2}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell4}	${CPE4}	${PDN}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境2					
							
RAN-4956805 PB1125H定时进入和退出深休__RAN-5532115	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF1			
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${prrus}	create list	101-instance	102-instance	111-instance	112-instance	
	#打开PB深休开关						
	修改PB深度休眠开关_多模	${ENODEB}	1				
	${time}	查询基站时间_多模	${ENODEB}				
	prru定时下电-基站级	${time}	${False}	${prrus}	20min		
	PRRU立即进入定时下电_多模	${VSW}					
	sleep	8min					
	#PB进入深休后状态确认						
	${resvalue}	查询单板信息_多模	${pb}	energySavingMode			
	should contain	${resvalue}	pbDeepSleep				
	${resvalue}	查询单板信息_多模	${prru1}	operState			
	should contain	${resvalue}	Normal				
	${resvalue1}	查询单板信息_多模	${prru1}	availStatus			
	should contain	${resvalue1}	unavailable savingElectricity				
	#PB退出深休						
	sleep	20min					
	${resvalue}	查询单板信息_多模	${pb}	energySavingMode			
	${resvalue1}	查询单板信息_多模	${pb}	channelShutdownState			
	${lenResvalue}	get length	${resvalue}				
	run keyword if	${lenResvalue} >0	fail	异常			
	should contain	${resvalue1}	0				
	[Teardown]	恢复环境					
							
RAN-5533864 不支持通道关断的机型，打开通道关断开关，通道关断不生效__RAN-5533864	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF2			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#打开通道关断开关						
	FDL打开通道关断开关						
	sleep	2min					
	#2查询LTE小区节能模式						
	${result}	查询FDL小区节能模式	${GNODEB}	1			
	log	${result}					
	should be true	'${result['cellEsState']}'=='0;0;0;0;0;0'					
	#3prru节能模式						
	${resvalue}	查询单板信息_多模	${prru}	energySavingMode			
	${resvalue1}	查询单板信息_多模	${prru}	channelShutdownState			
	${lenResvalue}	get length	${resvalue}				
	run keyword if	${lenResvalue} >0	fail	异常			
	should contain	${resvalue1}	0				
	#4不上报小区退服告警						
	Wait Until Keyword Succeeds	10min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#5prru诊断测试						
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result1}	set variable	${rlt['pa']}				
	${result11}	set variable	@{result1}[6]				
	${result}	set variable	@{result11}[3]				
	should contain	${result}	Open				
	[Teardown]	恢复环境					
							
RAN-5545145 载波关断+符号关断：不同频段开启不同的节能方式（载波关断+符号关断），prru整机载波关断+符号关断状态__RAN-5545145	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${nrCells}	获取NR小区别名_多模	${ENODEB}				
	#打开载波关断开关						
	关闭NR载波关断开关	1					
	同步规划区数据_多模	${GNODEB}					
	sleep	30					
	打开NR载波关断开关	1					
	同步规划区数据_多模	${GNODEB}					
	sleep	1min					
	重复执行_多模	3	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown
	...	Start ES					
	ITRAN-LTE-FDD符号关断开关	1					
	确认LTE sonm节能上报成功	${GNODEB}	DTX ES	ES Start	100	1	
	sleep	2min					
	#2查询小区节能模式						
	${result}	查询FDL小区节能模式	${GNODEB}	1			
	should be true	'${result['cellEsState']}'=='1;0;0;0;0;0'					
	${result}	查询NR小区节能模式	${GNODEB}	1			
	should be true	'${result['cellEsState']}'=='1'					
	#4prru节能模式						
	${resvalue}	查询单板信息_多模	${prru}	energySavingMode			
	${resvalue1}	查询单板信息_多模	${prru}	channelShutdownState			
	should contain	${resvalue}	rfCarrierShutdown rfSymbolShutdown				
	should contain	${resvalue1}	0				
	#4prru诊断测试						
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['tssi']}				
	${result1}	set variable	@{result}[6]				
	${result}	set variable	@{result1}[4]				
	run keyword if	${result}>0	log	sucess			
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['pa']}				
	${result1}	set variable	@{result}[6]				
	${result}	set variable	@{result1}[3]				
	should contain	${result}	Open				
	[Teardown]	恢复环境					
							
RAN-5562433 【Qcell】PB1125H四级级联满配置R8159 *********机型射频合并__RAN-5562433	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pb11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruAliasList1}	创建指定PB下的多个PRRU_多模	${ENODEB}	${pb10}	R8159 *********	${dictPrru}	8
	${prruAliasList2}	创建指定PB下的多个PRRU_多模	${ENODEB}	${pb11}	R8159 *********	${dictPrru}	8
	${prruAllList}	create list					
	${prruAllList}	Combine Lists	${prruAliasList1}	${prruAliasList2}			
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.8W+1V*4T4R*60M*0.8W)@4.9G	(2T*2T2R*20M*0.05W+1T*2T2R*10M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prruAllList}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#业务验证						
	${TdlCells}	获取TDD小区别名_多模	${ENODEB}				
	${FdlCells}	获取FDD小区别名_多模	${ENODEB}				
	${NrCells}	获取NR小区别名_多模	${ENODEB}				
	验证NR小区	@{NrCells}[0]	${CPE}	${PDN}			
	验证FDL小区	@{FdlCells}[0]	${CPE3}	${PDN}			
	验证TDL小区业务	@{TdlCells}[0]	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5584798 通道上多载波配置，其中2.3G TDL多载波，2.6G LV配置，4.9G NR双载波配置，对通道上多载波开启不同的节能方式，载波关断和符号关断，prru整机符号关断状态__RAN-5584798	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	${nrCells}	获取NR小区别名_多模	${ENODEB}				
	#打开载波关断开关						
	关闭NR载波关断开关	1					
	sleep	30					
	打开NR载波关断开关	1					
	重复执行_多模	3	确认sonm节能监控上报正确	${GNODEB}	Carrier Shutdown ES	1	Carrier Shutdown
	...	Start ES					
	打开NR符号关断开关	${cell1}					
	sleep	8min					
	#2查询小区节能模式						
	${result}	查询NR小区节能模式	${GNODEB}	1			
	should be true	'${result['cellEsState']}'=='1'					
	#4prru节能模式						
	${resvalue}	查询单板信息_多模	${prru}	energySavingMode			
	${resvalue1}	查询单板信息_多模	${prru}	channelShutdownState			
	should contain	${resvalue}	rfCarrierShutdown				
	should contain	${resvalue1}	0				
	#4prru诊断测试						
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['tssi']}				
	${result1}	set variable	@{result}[2]				
	${result}	set variable	@{result1}[4]				
	run keyword if	${result}>0	log	sucess			
	${rlt}	PRRU诊断测试_多模	${prru}				
	${result}	set variable	${rlt['pa']}				
	${result1}	set variable	@{result}[2]				
	${result}	set variable	@{result1}[3]				
	should contain	${result}	Open				
	[Teardown]	恢复环境					
							
RAN-5637918 【Qcell】R8159 *********机型载波配置下，修改VBP和PB之间光口速率__RAN-5637918	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	双上联修改光口速率	10					
	sleep	3min					
	双上联修改光口速率	25					
	sleep	3min					
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#业务验证						
	Comment	验证NR小区	${cell1}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell4}	${CPE2}	${PDN}		
	Comment	验证FDL小区	${fddCell1}	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-3926649 多prru LV混模：配置NR+L载波共通道，2.6G单NR场景，各通道各制式都配置载波，多种节能方式同时生效，复位全部prru和部分prru__RAN-3926649	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pbs}	获取VBP特定光口上的PB	${VBP8}	OF1			
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${prrus}	create list	101-instance	111-instance			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru1}	16;32;8192	1090519041		
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru2}	16;32;8192	1090519041		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.8W+1T*2T2R*20M*0.1W)@2.6G	(2T*2T2R*20M*0.2W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru1}		
	\	创建Qcell多模小区_多模	${prrus}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	${FdlCells}	获取FDD小区别名_多模	${ENODEB}				
	${NrCells}	获取NR小区别名_多模	${ENODEB}				
	#打开节能开关						
	打开NR载波关断开关	1					
	ITRAN-LTE-TDD符号关断开关	1					
	打开ITRAN-LTE-FDD载波关断开关	@{FdlCells}[0]					
	sleep	8min					
	${RRUSaveEnegyStatus}	查询单板信息_多模	101-instance	energySavingMode			
	should be true	'${RRUSaveEnegyStatus}' == 'rfCarrierShutdown rfSymbolShutdown'					
	${resvalue1}	查询单板信息_多模	101-instance	channelShutdownState			
	should contain	${resvalue1}	0				
	[Teardown]	恢复环境					
							
RAN-3939514 QCELL多模TDD LTE 各种带宽下的业务测试，厉遍 20M、15M、10M、5M__RAN-3939514	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${tdlCell}	获取TDD小区别名_多模	${ENODEB}				
	修改LTE小区15M带宽	3					
	sleep	3min					
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	修改LTE小区10M带宽	3					
	sleep	3min					
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-4025579 共框LV配置下，TNR和LTE的频点重叠，pRRU配置错误告警上报功能验证__RAN-4025579	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.8W+3T*2T2R*20M*0.1W)@2.6G			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#修改2.6G频点						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	修改TNR小区频点	@{cellList}[0]	2568				
	sleep	5min					
	Comment	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	200201001
	...	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}
	...	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}
	...	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}
	...	${EMPTY}	${GNODEB}				
	[Teardown]	恢复环境2					
							
RAN-5496102 PB1125H定时进入和退出深休__RAN-5496102	log	RAN-4956805执行					
							
RAN-5623411 单上联场景单级PB未配置，PB自发现__RAN-5623411	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${pb10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pb11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	删除并释放无线资源_多模	${ENODEB}					
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#双上联修改单上联						
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${GNODEB}					
	#删除二级级联中的第二级PB						
	${filterDict}	create dictionary	mocName=ReplaceableUnit	moId=PB_11			
	${filterDict2}	create dictionary	mocName=RiCable	moId=5			
	${filterDict3}	create dictionary	mocName=RiCable	moId=6			
	${filterDict4}	create dictionary	mocName=RiCable	moId=9			
	${filterDict5}	create dictionary	mocName=RiCable	moId=10			
	${filterDict6}	create dictionary	mocName=RiCable	moId=11			
	${filterDict7}	create dictionary	mocName=RiCable	moId=12			
	__删除节点	${ENODEB}	${filterDict}	planarea			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict4}	planarea			
	__删除节点	${ENODEB}	${filterDict5}	planarea			
	__删除节点	${ENODEB}	${filterDict6}	planarea			
	__删除节点	${ENODEB}	${filterDict7}	planarea			
	同步规划区数据_多模	${GNODEB}					
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	#配置跨基带板负荷分担						
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	VBP_1_4-instance	OF1
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	恢复环境2					
							
RAN-5623412 单上联场景非第1级的两级PB未配置，PB自发现__RAN-5623412	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${pb10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pb11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	删除并释放无线资源_多模	${ENODEB}					
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#双上联修改单上联						
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${GNODEB}					
	#删除二级级联中的第二级PB						
	${filterDict1}	create dictionary	mocName=ReplaceableUnit	moId=PB_11			
	${filterDict2}	create dictionary	mocName=RiCable	moId=5			
	${filterDict3}	create dictionary	mocName=RiCable	moId=6			
	${filterDict6}	create dictionary	mocName=RiCable	moId=11			
	${filterDict7}	create dictionary	mocName=RiCable	moId=12			
	__删除节点	${ENODEB}	${filterDict1}	planarea			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict6}	planarea			
	__删除节点	${ENODEB}	${filterDict7}	planarea			
	同步规划区数据_多模	${GNODEB}					
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	恢复环境2					
							
RAN-5623413 单上联场景两级PB都未配置，PB自发现__RAN-5623413	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${pb10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${pb11}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_11			
	${pb30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${pb31}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_31			
	删除并释放无线资源_多模	${ENODEB}					
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	#双上联修改单上联						
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	更新组网配置_多模	${ENODEB}	${pb10}	${EMPTY}	0		
	更新组网配置_多模	${ENODEB}	${pb11}	${EMPTY}	0		
	更新组网配置_多模	${ENODEB}	${pb30}	${EMPTY}	0		
	更新组网配置_多模	${ENODEB}	${pb31}	${EMPTY}	0		
	同步规划区数据_多模	${GNODEB}					
	#删除二级级联中的第二级PB						
	${filterDict1}	create dictionary	mocName=ReplaceableUnit	moId=PB_11			
	${filterDict2}	create dictionary	mocName=ReplaceableUnit	moId=PB_10			
	${filterDict3}	create dictionary	mocName=RiCable	moId=5			
	${filterDict4}	create dictionary	mocName=RiCable	moId=6			
	${filterDict5}	create dictionary	mocName=RiCable	moId=9			
	${filterDict6}	create dictionary	mocName=RiCable	moId=10			
	${filterDict7}	create dictionary	mocName=RiCable	moId=11			
	${filterDict8}	create dictionary	mocName=RiCable	moId=12			
	${filterDict9}	create dictionary	mocName=RiCable	moId=3			
	__删除节点	${ENODEB}	${filterDict1}	planarea			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict4}	planarea			
	__删除节点	${ENODEB}	${filterDict5}	planarea			
	__删除节点	${ENODEB}	${filterDict6}	planarea			
	__删除节点	${ENODEB}	${filterDict7}	planarea			
	__删除节点	${ENODEB}	${filterDict8}	planarea			
	__删除节点	${ENODEB}	${filterDict9}	planarea			
	同步规划区数据_多模	${GNODEB}					
	sleep	5min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	恢复环境2					
							
RAN-5623416 级联场景，光纤混接场景下，PB自发现__RAN-5623416	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	修改VBP光口协议_多模	${VBP4}	OF2	1			
	修改VBP光口速率_多模	${VBP4}	OF2	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#修改连线						
	创建无线接口连线_多模	VBP_1_4-instance	OF1	PB_30-instance	OF1		
	更新无线接口连线_多模	VBP_1_4-instance	OF1	PB_30-instance	OF1	VBP_1_4-instance	OF2
	创建无线接口连线_多模	VBP_1_8-instance	OF6	PB_30-instance	OF2		
	更新无线接口连线_多模	VBP_1_8-instance	OF6	PB_30-instance	OF2	VBP_1_8-instance	OF3
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	恢复环境2					
							
RAN-5623428 双上双下，非第1级PB都未配置，PB未配置告警上报__RAN-5623428	log	下一条已覆盖					
	[Teardown]	恢复环境2					
							
RAN-5623429 双上双下，所有PB都未配置，PB自发现__RAN-5623429	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	#删除二级级联中的第二级PRRU						
	${filterDict2}	create dictionary	mocName=RiCable	moId=5			
	${filterDict3}	create dictionary	mocName=RiCable	moId=6			
	${filterDict4}	create dictionary	mocName=RiCable	moId=10			
	__删除节点	${ENODEB}	${filterDict2}	planarea			
	__删除节点	${ENODEB}	${filterDict3}	planarea			
	__删除节点	${ENODEB}	${filterDict4}	planarea			
	#配置跨基带板负荷分担						
	创建无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1		
	更新无线接口连线_多模	VBP_1_8-instance	OF2	PB_10-instance	OF1	VBP_1_4-instance	OF1
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	检查预期告警是否上报	${GNODEB}	198097811	
	[Teardown]	恢复环境2					
							
RAN-5623430 PB单上联LV场景25G光电复合缆PB功能模式1： 20：NR(300M)+3L一个电口下所有pRRU射频合并配置3NR场景__RAN-5623430	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	#双上联修改单上联						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${GNODEB}					
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${prru3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+112			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_3			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	run keyword and ignore error	修改PRRU功能模式_多模	${prru1}	16;32;8192	1090519041		
	run keyword and ignore error	修改PRRU功能模式_多模	${prru2}	16;32;8192	1090519041		
	run keyword and ignore error	修改PB功能模式_多模	PB_10-instance	0	20		
	run keyword and ignore error	修改PB功能模式_多模	PB_11-instance	0	20		
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	VBP_1_8-instance	${prru1}	100
	...	0	3,4,5,6	${False}			
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	VBP_1_4-instance	${prru2}	100
	...	0	3,4,5,6	${False}			
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	VBP_1_4-instance	${prru3}	100
	...	0	3,4,5,6	${False}			
	: FOR	${radioString}	IN	(2T*2T2R*20M*0.2W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru1}		
	\	创建Qcell多模小区_多模	${prru1}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境2					
							
RAN-5623443 PB单上联LV场景(25G光电复合缆)PB功能模式1： 20：NR(300M)+3L一个电口下所有pRRU射频合并配置3NR场景__RAN-5623443	log	上一条已覆盖					
							
RAN-5623451 PB单上联LV场景(25G光电复合缆)PB功能模式1： 20：NR(300M)+3L一个光口配置3NR场景__RAN-5623451	log	上一条已覆盖					
							
RAN-5623452 PB单上联LV场景(25G光电复合缆)PB功能模式1： 20：NR(300M)+3L一个光口配置3NR场景__RAN-5623452	log	上一条已覆盖					
							
RAN-5623453 PB单上联LV场景(25G光电复合缆)PB功能模式2： 15：NR(200M)+10L一个电口配置双载波场景__RAN-5623453	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+102			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${PRRUS2}	create list	${prru2}				
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.05W)@2.6G	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru2}		
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境2					
							
RAN-5623455 PB单上联LV场景(25G光电复合缆)PB功能模式 17：NR(320M)+2L__RAN-5623455	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP8}	${PRRUS}	100
	...	0	3,4,5,6	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP8}	${PRRUS}	60
	...	100	3,4,5,6	${True}	${False}		
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.1W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	sleep	6min					
	同步规划区数据_多模	${GNODEB}					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#双上联修改单上联						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	@{pbAlias}	根据类型获取实例化单板别名_多模	${GNODEB}	256			
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_10-instance	0	255		
	双上联模式转换为单上联模式_多模	${ENODEB}	PB_30-instance	0	255		
	: FOR	${board}	IN	@{pbAlias}			
	\	更新组网配置_多模	${ENODEB}	${board}	${EMPTY}	0	
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	恢复环境2					
							
RAN-5623456 PB单上联LV场景(25G光电复合缆)PB功能模式4：17：NR(320M)+2L__RAN-5623456	log	上一条已覆盖					
							
RAN-5637915 R8159 *********机型载波配置下，复位射频合并小区中部分pRRu__RAN-5637915	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${pb10}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_10			
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prrus}	create list	101-instance	111-instance			
	: FOR	${radioString}	IN	(1V*4T4R*100M*0.8W+1V*4T4R*60M*0.8W)@4.9G	(2T*2T2R*20M*0.05W+1T*2T2R*10M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prrus}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#业务验证						
	${TdlCells}	获取TDD小区别名_多模	${ENODEB}				
	${FdlCells}	获取FDD小区别名_多模	${ENODEB}				
	${NrCells}	获取NR小区别名_多模	${ENODEB}				
	#2告警验证						
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#掉电复位PRRU						
	掉电复位PRRU_多模	${prru}					
	sleep	10min					
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	恢复环境					
							
RAN-5685887 R8159 *********机型支持4.9(NR100+60M)+2.6(NR100M+3TDL)****(NR50M+TDL20M)+1.8G(FDL20M)__RAN-5685887	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	${PRRUS}	create list	${prru}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP8}	${PRRUS}	50
	...	0	1,2	${False}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	#修改2.3G频点						
	${cells}	获取NR小区别名_多模	${ENODEB}				
	${freq}	查询NR小区频点_多模	@{cells}[0]				
	${newFreq}	evaluate	${freq}+40				
	${attrDic}	create dictionary	CarrierDL_frequency	${newFreq}	CarrierUL_frequency	${newFreq}	
	模板修改NR小区参数_多模	@{cells}[0]	NRCarrier	${attrDic}			
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.05W)@2.6G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${prru}	${freqDict}			
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5685891 R8159 *********机型2.3G频段小区删建__RAN-5685891	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	#删除2.3GLTE载波						
	${cell}	获取TDD小区别名_多模	${GNODEB}				
	保存LTE-TDD小区信息_多模	${ENODEB}					
	删除指定LTE-TDD小区_多模	${ENODEB}	3				
	创建指定LTE-TDD小区_多模	${ENODEB}	3				
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	#小区&业务验证						
	sleep	5min					
	验证TDL小区业务	${tddCell3}	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5686065 R8159 *********机型2.3G频段载波改配2.3(NR50M+TDL20M)****(3TDL20M+TDL10M)__RAN-5686065	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_3			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.1W)@2.3G			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru2}		
	\	创建Qcell多模小区_多模	${prru2}	${freqDict}			
	sleep	6min					
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	恢复环境					
							
RAN-5686101 R8159 *********机型2.3G频段LV混模配置下，LTE小区带宽改配__RAN-5686101	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_3			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	#修改2.3G带宽						
	${cells}	获取TDD小区别名_多模	${GNODEB}				
	修改LTE小区15M带宽	7					
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	验证TDL小区业务	tddCell7	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5686107 R8159 *********机型2.3G频段LV混模配置下，LTE小区闭塞解闭塞__RAN-5686107	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_3			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	#闭塞其中1个小区						
	${tdds}	获取TDD小区别名_多模	${GNODEB}				
	关断EUtran小区_多模	tddCell7					
	sleep	2min					
	解关断EUtran小区_多模	tddCell7					
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#1告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	验证TDL小区业务	tddCell7	${CPE4}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-5686114 R8159 *********机型2.3G频段LV混模配置下，NR小区闭塞解闭塞__RAN-5686114	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_3			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	@{cellAlias}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellAlias}			
	\	确认NR小区状态正常_多模	${cell}				
	#2.3G闭塞						
	${cellAlias}	获取NR小区别名_多模	${GNODEB}				
	闭塞NR小区_多模	nrCell460-11_10881-7-46011-10881-7					
	sleep	3min					
	解闭塞NR小区_多模	nrCell460-11_10881-7-46011-10881-7					
	#1告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5686120 R8159 *********机型2.3G频段LV混模配置下，频点改配__RAN-5686120	${almStart}	查询基站当前告警_多模	${ENODEB}				
	${prru1}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+101			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+111			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_3			
	${VBP7}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPd0+VBP_1_7			
	#修改2.3G频点						
	${cell}	获取NR小区别名_多模	${ENODEB}				
	${freq}	查询NR小区频点_多模	nrCell460-11_10881-7-46011-10881-7				
	${newFreq}	evaluate	${freq}+0.3				
	${attrDic}	create dictionary	CarrierDL_frequency	${newFreq}	CarrierUL_frequency	${newFreq}	
	模板修改NR小区参数_多模	nrCell460-11_10881-7-46011-10881-7	NRCarrier	${attrDic}			
	同步规划区数据_多模	${ENODEB}					
	sleep	3min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	[Teardown]	恢复环境					
							
RAN-5585258 各通道各制式配置NR和LTE载波，多次启动和退出NR LTE符号关断后，NR LTE业务KPI指标不受影响__RAN-5585258	log	已覆盖					
	[Teardown]	恢复环境					
							
RAN-5584742 各通道各制式配置NR和LTE载波，多次启动和退出NR LTE载波关断后，NR LTE业务KPI指标不受影响__RAN-5584742	log	已覆盖					
	[Teardown]	恢复环境					
							
RAN-4969211 【Qcell】PB1125H双上联负荷分担场景功能模式13与功能模式27相互改配__RAN-4969211	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+302			
	${prru3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+311			
	${prru4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+312			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${vbp8}	OF6			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	13	
	同步规划区数据_多模	${ENODEB}					
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	sleep	4min					
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	${PRRUS}	create list	${prru}				
	${PRRUS2}	create list	${prru2}				
	${PRRUS3}	create list	${prru3}				
	${PRRUS4}	create list	${prru4}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP4}	${PRRUS2}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell5}	动态创建Qcell-NR小区_多模	${GNODEB}	5	${VBP8}	${PRRUS3}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell6}	动态创建Qcell-NR小区_多模	${GNODEB}	6	${VBP8}	${PRRUS4}	100
	...	0	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G			
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	27	
	同步规划区数据_多模	${ENODEB}					
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	Comment	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}
	...	20	100	9,10,11,12	${True}	${False}	
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	Comment	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP4}	${PRRUS2}
	...	20	100	9,10,11,12	${True}	${False}	
	${cell5}	动态创建Qcell-NR小区_多模	${GNODEB}	5	${VBP8}	${PRRUS3}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell6}	动态创建Qcell-NR小区_多模	${GNODEB}	6	${VBP8}	${PRRUS4}	100
	...	0	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.05W)@2.6G	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS4}	${freqDict}			
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	恢复环境2					
							
RAN-5044170 【Qcell】PB1125H双上联负荷分担场景功能模式13与通用功能模式255相互改配__RAN-5044170	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+302			
	${prru3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+311			
	${prru4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+312			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${vbp8}	OF6			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	13	
	同步规划区数据_多模	${ENODEB}					
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	sleep	4min					
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	${PRRUS}	create list	${prru}				
	${PRRUS2}	create list	${prru2}				
	${PRRUS3}	create list	${prru3}				
	${PRRUS4}	create list	${prru4}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP4}	${PRRUS2}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell5}	动态创建Qcell-NR小区_多模	${GNODEB}	5	${VBP8}	${PRRUS3}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell6}	动态创建Qcell-NR小区_多模	${GNODEB}	6	${VBP8}	${PRRUS4}	100
	...	0	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G			
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	255	
	同步规划区数据_多模	${ENODEB}					
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	Comment	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}
	...	20	100	9,10,11,12	${True}	${False}	
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	Comment	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP4}	${PRRUS2}
	...	20	100	9,10,11,12	${True}	${False}	
	${cell5}	动态创建Qcell-NR小区_多模	${GNODEB}	5	${VBP8}	${PRRUS3}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell6}	动态创建Qcell-NR小区_多模	${GNODEB}	6	${VBP8}	${PRRUS4}	100
	...	0	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.05W)@2.6G	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS4}	${freqDict}			
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#业务验证						
	Comment	验证NR小区	${cell2}	${CPE}	${PDN}		
	Comment	验证NR小区	${cell7}	${CPE2}	${PDN}		
	Comment	验证FDL小区	fddCell460-11_10882-02	${CPE3}	${PDN}		
	Comment	验证TDL小区业务	${tddCell4}	${CPE3}	${PDN}		
	[Teardown]	恢复环境					
							
RAN-5044970 【Qcell】PB1125H双上联负荷分担场景功能模式27与通用功能模式255相互改配__RAN-5044970	${almStart}	查询基站当前告警_多模	${ENODEB}				
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	${prru}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+301			
	${prru2}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+302			
	${prru3}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+311			
	${prru4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	R8159 *********+312			
	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${VBP4}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_4			
	${pbs}	获取VBP特定光口上的PB	${vbp8}	OF6			
	${PB30}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	PB1125H+PB_30			
	run keyword and continue on failure	修改PRRU功能模式_多模	${prru}	16;32;8192	1090519041		
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	255	
	同步规划区数据_多模	${ENODEB}					
	#修改光接口连线						
	修改VBP光口协议_多模	${VBP4}	OF1	1			
	修改VBP光口速率_多模	${VBP4}	OF1	25			
	创建无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	0	
	修改无线接口连线_多模	${VBP8}	OF1	${PB30}	OF1	${VBP4}	OF1
	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	1.4.4-5.1.1				
	sleep	4min					
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	${PRRUS}	create list	${prru}				
	${PRRUS2}	create list	${prru2}				
	${PRRUS3}	create list	${prru3}				
	${PRRUS4}	create list	${prru4}				
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP4}	${PRRUS2}	60
	...	100	9,10,11,12	${True}	${False}		
	${cell5}	动态创建Qcell-NR小区_多模	${GNODEB}	5	${VBP8}	${PRRUS3}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell6}	动态创建Qcell-NR小区_多模	${GNODEB}	6	${VBP8}	${PRRUS4}	100
	...	0	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS2}	${freqDict}			
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G			
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	#2告警验证						
	Wait Until Keyword Succeeds	20min	60sec	确认基站无新增告警_多模	${ENODEB}	${almStart}	
	删除并释放无线资源_多模	${ENODEB}					
	动态创建基带功能_多模	VBP_1_8-instance					
	动态创建基带功能_多模	VBP_1_4-instance					
	: FOR	${board}	IN	@{pbs}			
	\	Run Keyword And Return Status	修改PB功能模式_多模	${board}	0	27	
	同步规划区数据_多模	${ENODEB}					
	${cell1}	动态创建Qcell-NR小区_多模	${GNODEB}	1	${VBP4}	${PRRUS}	100
	...	0	9,10,11,12	${True}	${False}		
	Comment	${cell2}	动态创建Qcell-NR小区_多模	${GNODEB}	2	${VBP4}	${PRRUS}
	...	20	100	9,10,11,12	${True}	${False}	
	${cell3}	动态创建Qcell-NR小区_多模	${GNODEB}	3	${VBP4}	${PRRUS2}	100
	...	0	9,10,11,12	${True}	${False}		
	Comment	${cell4}	动态创建Qcell-NR小区_多模	${GNODEB}	4	${VBP4}	${PRRUS2}
	...	20	100	9,10,11,12	${True}	${False}	
	${cell5}	动态创建Qcell-NR小区_多模	${GNODEB}	5	${VBP8}	${PRRUS3}	100
	...	0	9,10,11,12	${True}	${False}		
	${cell6}	动态创建Qcell-NR小区_多模	${GNODEB}	6	${VBP8}	${PRRUS4}	100
	...	0	9,10,11,12	${True}	${False}		
	同步规划区数据_多模	${GNODEB}					
	: FOR	${radioString}	IN	(3T*2T2R*10M*0.05W)@2.6G	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G	
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS3}	${freqDict}			
	\	创建Qcell多模小区_多模	${PRRUS4}	${freqDict}			
	: FOR	${radioString}	IN	(1T*2T2R*20M*0.05W)@2.3G	(1F*2T2R*20M*0.2W)@1.8G		
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	${prru}		
	\	创建Qcell多模小区_多模	${PRRUS}	${freqDict}			
	同步规划区数据_多模	${GNODEB}					
	sleep	6min					
	Wait Until Keyword Succeeds	15min	30sec	验证NR小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证FDD小区_多模	${ENODEB}		
	Wait Until Keyword Succeeds	15min	30sec	验证TDD小区_多模	${ENODEB}		
	[Teardown]	恢复环境					
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${ENODEB}+${GNODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建UE对象	${CPE4}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名						
	创建光交换机_多模	${FIBERCONNECT}					
							
释放配置	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除UE对象	${CPE4}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
恢复环境	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	sleep	120					
							
恢复环境2	恢复光交换机配置						
	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	sleep	120					
							
VBP光口下配PB、PRRU	[Arguments]	${PORT}	${scaseNumb}	${prruNumb}	${VBP8}		
	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${pbAliasList}	创建VBP特定光口下的多级PB级联_多模	${ENODEB}	${VBP8}	${PORT}	PB1125H	${scaseNumb}
	...	${dictPb}					
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruList}	create list					
	: FOR	${pbAlias}	IN	@{pbAliasList}			
	\	${prruAliasList}	创建指定PB下的多个PRRU_多模	${ENODEB}	${pbAlias}	R8159 *********	${dictPrru}
	...	${prruNumb}					
	\	${prruList}	Combine Lists	${prruList}	${prruAliasList}		
	log	${prruList}					
	[Return]	${prruList}					
							
验证指定LTE小区	[Arguments]	${cellList}					
	${list_}	Create List					
	: FOR	${cell}	IN	@{cellList}			
	\	Append To List	${list_}	${cell}			
	重复执行_多模	30	确认EUtran小区状态正常_多模	@{cellList}			
							
修改rfAppMode	${TDDAlias}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cell}	IN	@{TDDAlias}			
	\	${attrDic}	create dictionary	rfAppMode=3			
	\	修改TD_LTE基带资源参数_多模	${cell}	${attrDic}			
	${attrDic1}	create dictionary	rfAppMode=3				
	${NRcellAlias}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{NRcellAlias}			
	\	修改NR扇区载波属性_多模	${cell}	${attrDic1}	${False}		
	同步规划区数据_多模	${ENODEB}					
							
修改LTE小区5M带宽	[Arguments]	${cellAlias}					
	修改LTE采样速率模式配置_多模	${cellAlias}	0				
	修改LTE小区带宽_多模	${cellAlias}	5				
	检查激活配置	${ENODEB}					
	sleep	120					
							
修改LTE小区10M带宽	[Arguments]	${moId}					
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moId}			
	${attrDict}	create dictionary	bandWidth=3	maxUeRbNumDl=50	maxUeRbNumUl=23	sampleRateCfg=0	
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改LTE小区15M带宽	[Arguments]	${moId}					
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moId}			
	${attrDict}	create dictionary	bandWidth=4	maxUeRbNumDl=75	maxUeRbNumUl=28	sampleRateCfg=2	
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改LTE小区20M带宽	[Arguments]	${moId}					
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moId}			
	${attrDict}	create dictionary	bandWidth=5	maxUeRbNumDl=100	maxUeRbNumUl=33	sampleRateCfg=2	
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改FDL小区10M带宽	[Arguments]	${moId}					
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${moId}			
	${attrDict}	create dictionary	bandWidthDl=3	bandWidthUl=3	maxUeRbNumDl=50	maxUeRbNumUl=23	sampleRateCfg=0
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改FDL小区15M带宽	[Arguments]	${moId}					
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${moId}			
	${attrDict}	create dictionary	bandWidthDl=4	bandWidthUl=4	maxUeRbNumDl=75	maxUeRbNumUl=28	sampleRateCfg=2
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改FDL小区20M带宽	[Arguments]	${moId}					
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${moId}			
	${attrDict}	create dictionary	bandWidthDl=5	bandWidthUl=5	maxUeRbNumDl=100	maxUeRbNumUl=33	sampleRateCfg=2
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改FDL小区频点	${lteCell}	获取FDD小区别名_多模	${ENODEB}				
	${freqDl}	获得LTE小区上下行频点_多模	@{lteCell}[0]	earfcnDl			
	${freqUl}	获得LTE小区上下行频点_多模	@{lteCell}[0]	earfcnUl			
	${newFreqDl}	evaluate	${freqDl}+5				
	${newFreqUl}	evaluate	${freqUl}+5				
	修改LTE小区上下行频点	@{lteCell}[0]	${newFreqDl}				
							
修改NR小区频点和带宽	[Arguments]	${newFreq}	${cell}				
	${attrDic}	create dictionary	CarrierDL_frequency	${newFreq}	CarrierUL_frequency	${newFreq}	
	模板修改NR小区参数_多模	@{cell}[0]	NRCarrier	${attrDic}			
	模板修改NR小区带宽_多模	${GNODEB}	@{cell}[0]	80	217		
	同步规划区数据_多模	${ENODEB}					
	sleep	400					
							
修改TDL小区频点	[Arguments]	${moid}	${TDDAlias}	${newFreq}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moid}			
	${attrDict}	create dictionary	earfcn	${newFreq}			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改LTE小区上下行频点	[Arguments]	${cellAlias}	${freqDl}	${isCurArea}=${False}	${isTemplate}=${False}		
	[Documentation]	【功能说明】：					
	...	修改LTE小区频点					
	...						
	...	【入参】：					
	...	1、cellAlias：小区对象名					
	...	2、freqDl: 小区频点					
	...	3、isCurrArea：现网区操作或规划区操作，默认True是现网区，False是规划区					
	...	4、isTemplate：规划区模板操作，默认True是模板操作，False是非模板操作					
	...						
	...	【作者】：					
	...	00103116					
	${result}	modify_lte_cell_dl_and_up_freq	${cellAlias}	${freqDl}	${isCurArea}	${isTemplate}	
							
获取MTS监控流量	[Arguments]	${ueAlias}	${cellAlias}	${dlLimit}	${ulLimit}		
	从Huc容器里获取cpfUeId_多模	${VSW}	${ueAlias}				
	${eventList}	create list	CELL_DL_BASE_INFO	CELL_UL_BASE_INFO			
	${taskid}	创建小区MTS任务_多模	${cellAlias}	${eventList}			
	sleep	25					
	${trafficId}	开始UDP收灌包	RANDOM	${PDN}	${CPE2}	BI	400m
	...	9999	1024	4			
	sleep	10					
	${thread}	run keyword and continue on failure	开始MTS监控数据_多模	${cellAlias}	${taskid}		
	sleep	80					
	${tableListDl}	create list	CELL_DL_BASE_INFO	CELL_DL_BASE_INFO			
	${elementListDl}	create list	PHYTput(Kbps)	Bler[CEU](%)			
	${tableListUl}	create list	CELL_UL_BASE_INFO	CELL_UL_BASE_INFO			
	${elementListUl}	create list	PHYTput(Kbps)	Bler[CEU](%)			
	${resultDl}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableListDl}
	...	${elementListDl}	${False}				
	${resultUl}	run keyword and continue on failure	获取MTS上UE监控数据_多模	${cellAlias}	${ueAlias}	${taskid}	${tableListUl}
	...	${elementListUl}	${False}				
	${dlPhyputValue}	获取UE指标_多模	${resultDl}	PHYTput(Kbps)			
	${dlBler}	获取UE指标_多模	${resultDl}	Bler[CEU](%)			
	${ulPhyputValue}	获取UE指标_多模	${resultUl}	PHYTput(Kbps)			
	${ulBler}	获取UE指标_多模	${resultUl}	Bler[CEU](%)			
	run keyword and continue on failure	结束UDP收灌包	${trafficId}	BI			
	run keyword and continue on failure	should be true	${dlPhyputValue} > ${dlLimit}				
	run keyword and continue on failure	should be true	${ulPhyputValue} > ${ulLimit}				
	run keyword and continue on failure	should be true	${dlBler} < 2				
	run keyword and continue on failure	should be true	${ulBler} < 2				
	run keyword and continue on failure	停止MTS监控数据_多模	${cellAlias}	${taskid}	${thread}		
	run keyword and continue on failure	删除小区MTS任务_多模	${cellAlias}	${taskid}			
							
获取VBP8特定光口上的PRRUS	[Arguments]	${port}					
	${prrus}	create list					
	${pbAliases}	获取VBP特定光口下的PB别名_多模	${ENODEB}	VBP_1_8-instance	${port}		
	: FOR	${pbAliase}	IN	@{pbAliases}			
	\	${tmp}	根据pb单板别名获取其上的prrus_多模	${pbAliase}			
	\	${prrus}	Combine Lists	${prrus}	${tmp}		
	[Return]	${prrus}					
							
创建NR小区	[Arguments]	${radioStrings}	${prrus}				
	: FOR	${radioString}	IN	@{radioStrings}			
	\	${freqDict}	根据Qcell字符串分配无线参数_多模	${radioString}	@{prrus}[0]		
	\	创建Qcell多模小区_多模	${prrus}	${freqDict}			
							
无线帧偏移	[Arguments]	${moId}	${nrBand41FreqAdjustValue}	${dFreqAdjustValue}	${nrBand40FreqAdjustValue}	${eFreqAdjustValue}	
	${filterDict}	create dictionary	mocName=FrequencyBandOffset	moId=${moId}			
	${attrDict}	create dictionary	nrBand41FreqAdjustValue=${nrBand41FreqAdjustValue}	dFreqAdjustValue=${dFreqAdjustValue}	nrBand40FreqAdjustValue=${nrBand40FreqAdjustValue}	eFreqAdjustValue=${eFreqAdjustValue}	
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
修改小区帧结构	[Arguments]	${frameType}	${TddConfig_frameType2Present}	${frameType1}			
	[Documentation]	2.5ms单1D3U帧结构，frameType1要配：1;2;0;0;0					
	...	2.5ms双周期帧结构，frameType1要配：1;1;1;1;1;1;1;2;0;0					
	...	5ms单周期帧结构，frameType1要配：1;1;1;1;1;1;1;2;0;0					
	${attrDic}	create dictionary	TddConfig_dlULTransmissionPeriodicity1	${frameType}	TddConfig_frameType2Present	${TddConfig_frameType2Present}	
	模板修改NR小区参数_多模	${cell}	NRPhysicalCellDU	${attrDic}			
	同步规划区数据_多模	${GNODEB}					
	${attr}	create Dictionary	frameType1	${frameType1}			
	修改NR物理DU小区属性_多模	${cell}	TddConfig	${attr}			
	检查激活配置	${GNODEB}					
							
修改小区为3+2ms双周期帧结构	[Arguments]	${cell}					
	${attr}	create Dictionary	userExpSwitch=2				
	修改NR物理DU小区属性_多模	${cell}	NRPhysicalCellDU	${attr}	1	${False}	
	${attr}	create Dictionary	dlULTransmissionPeriodicity1=ms3	frameType2Present=1	dlULTransmissionPeriodicity2=ms2	frameType1=1;1;1;2;0;0	frameType2=1;1;1;1;null
	修改NR物理DU小区属性_多模	${cell}	TddConfig	${attr}	1	${False}	
	同步规划区数据_多模	${GNODEB}					
							
修改小区为ms2p5双周期1D3U帧结构	[Arguments]	${cellAlias}					
	修改Tddconfig配置	${cellAlias}	dlULTransmissionPeriodicity1	ms2p5			
	修改Tddconfig配置	${cellAlias}	frameType2Present	0			
	修改Tddconfig配置	${cellAlias}	frameType1	1;2;0;0;0			
							
复位所有PB	${pbs}	create list					
	: FOR	${scase}	IN RANGE	4			
	\	${i}	evaluate	4-${scase}			
	\	${tmp}	根据级联数获取实例化单板别名_多模	${ENODEB}	${i}		
	\	${pbs}	Combine Lists	${pbs}	${tmp}		
	: FOR	${pb}	IN	@{pbs}			
	\	掉电复位PB_多模	${pb}				
							
获取VBP特定光口上的PB	[Arguments]	${VBP}	${port}				
	${pbAliases}	获取VBP特定光口下的PB别名_多模	${ENODEB}	${VBP}	${port}		
	[Return]	${pbAliases}					
							
prru定时下电-基站级	[Arguments]	${time}	${isWeekend}	${usedboards}	${delaytime}		
	[Documentation]	time: 时间					
	...						
	...	isweekend: 是否是周末					
	${almStart}	查询基站当前告警_多模	${ENODEB}				
	@{boards}	根据类型获取实例化单板别名_多模	${ENODEB}	128			
	${boards1}	create list					
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	append to list	${boards1}	${board}			
	${boards2}	create list					
	${boards2}	copy list	${boards1}				
	sleep	10					
	: FOR	${board}	IN	@{usedboards}			
	\	log	${board}				
	\	remove values from list	${boards2}	${board}			
	log	${boards2}					
	run keyword and continue on failure	创建PRRU定时节电参数_多模	${ENODEB}				
	设置不参与定时节电的PRRU_多模	${ENODEB}	${boards2}				
	sleep	10					
	#打开定时下电						
	修改PRRU定时节电开关_多模	${ENODEB}	1				
	修改PRRU定时节电策略_多模	${ENODEB}	0				
	修改PRRU定时节电温度门限_多模	${ENODEB}	30				
	#设置定时下电时间						
	${a}	set variable	${time}				
	${tmp}	split string	${a}	T			
	${tmp}	split string	${tmp[1]}	:			
	${time1}	set variable	${tmp[0]}:${tmp[1]}				
	${time2}	获取延时后的时间点	${delaytime}				
	修改PRRU工作日节电时间_多模	${ENODEB}	${time1}	${time2}			
	修改PRRU休息日节电时间_多模	${ENODEB}	${time1}	${time2}			
	同步规划区数据_多模	${ENODEB}					
	sleep	8min					
	#查询PRRU状态						
	: FOR	${board}	IN	@{usedboards}			
	\	${RRUSaveEnegyStatus}	查询单板信息_多模	${board}	availStatus		
	\	should be true	'${RRUSaveEnegyStatus}' == 'unavailable savingElectricity'				
	\	${resvalue}	查询单板信息_多模	${board}	operState		
	\	should be true	'${resvalue}' == 'Normal'				
	#查询告警状态						
	Comment	Wait Until Keyword Succeeds	10min	30sec	确认基站无新增告警_多模	${ENODEB}	${almStart}
	[Teardown]						
							
FDL打开通道关断开关	${attr}	create dictionary	sonFuncId	57	sonSwitch	1	
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	rfShutdownSwitch	1	rfShutdownDelayTime	1	weekdayChannelEsTime
	...	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendChannelEsTime	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1			
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=1			
	${attrDict}	create dictionary	energySavControl	1			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
FDL关闭通道关断开关	${attr}	create dictionary	sonFuncId	57	sonSwitch	1	
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	rfShutdownSwitch	0	rfShutdownDelayTime	1	weekdayChannelEsTime
	...	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendChannelEsTime	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1			
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=44			
	${attrDict}	create dictionary	energySavControl	1			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
FDL关闭深度休眠开关	[Arguments]	${moid}					
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1	
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch	0	weekdaySleepEsTime	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime
	...	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${moid}			
	${attrDict}	create dictionary	energySavControl	1			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
FDL打开深度休眠开关	[Arguments]	${moid}					
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1	
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch	1	weekdaySleepEsTime	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime
	...	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${moid}			
	${attrDict}	create dictionary	energySavControl	1			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
TDL打开深度休眠开关	[Arguments]	${moid}					
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1	
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch	1	weekdaySleepEsTime	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime
	...	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moid}			
	${attrDict}	create dictionary	energySavControl	1			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
TDL关闭深度休眠开关	[Arguments]	${moid}					
	${attr}	create dictionary	sonFuncId	57	sonSwitch	1	
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch	0	weekdaySleepEsTime	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime
	...	1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${moid}			
	${attrDict}	create dictionary	energySavControl	1			
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
TNR打开深度休眠开关	[Arguments]	${nrCarrierId}	${deepSleepNRSwitch}				
	${filterDict}	create dictionary	mocName=NRCarrierObj	nrCarrierId=${nrCarrierId}			
	${attributeNameList}	create list	moId				
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}		
	${moId}	Get From Dictionary	${result[0]}	moId			
	${filterDict}	create dictionary	mocName=CarrierESPolicy	moId=1			
	${attrDict}	create dictionary	deepSleepNRSwitch=${deepSleepNRSwitch}	deepSleepWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1		
	${keyMoPathDict}	create dictionary	ESPolicy=${moId}				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	sleep	30					
							
TNR关闭深度休眠开关	[Arguments]	${nrCarrierId}	${deepSleepNRSwitch}				
	${filterDict}	create dictionary	mocName=NRCarrierObj	nrCarrierId=${nrCarrierId}			
	${attributeNameList}	create list	moId				
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}		
	${moId}	Get From Dictionary	${result[0]}	moId			
	${filterDict}	create dictionary	mocName=CarrierESPolicy	moId=1			
	${attrDict}	create dictionary	deepSleepNRSwitch=${deepSleepNRSwitch}	deepSleepWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1		
	${keyMoPathDict}	create dictionary	ESPolicy=${moId}				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	sleep	30					
							
打开NR载波关断开关	[Arguments]	${nrCarrierId}					
	[Documentation]	功能					
	...	[入参]：					
	...	${nrCarrierId}:载波ID					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${filterDict}	create dictionary	mocName=NRCarrierObj	nrCarrierId=${nrCarrierId}			
	${attributeNameList}	create list	moId				
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}		
	${moId}	Get From Dictionary	${result[0]}	moId			
	${filterDict}	create dictionary	mocName=CarrierESPolicy	moId=1			
	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	
	${keyMoPathDict}	create dictionary	ESPolicy=${moId}				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭NR载波关断开关	[Arguments]	${nrCarrierId}					
	[Documentation]	功能					
	...	[入参]：					
	...	${nrCarrierId}:载波ID					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${filterDict}	create dictionary	mocName=NRCarrierObj	nrCarrierId=${nrCarrierId}			
	${attributeNameList}	create list	moId				
	${result}	__查询节点属性信息	${GNODEB}	${filterDict}	${attributeNameList}		
	${moId}	Get From Dictionary	${result[0]}	moId			
	${filterDict}	create dictionary	mocName=CarrierESPolicy	moId=1			
	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	
	${keyMoPathDict}	create dictionary	ESPolicy=${moId}				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
ITRAN-LTE-FDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-FDD-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
打开所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR符号关断开关	${cell}				
							
关闭所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR符号关断开关	${cell}				
							
打开ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
查询PRRU节能模式	[Arguments]	${gndAlias}	${moId}				
	${filterDict}	create dictionary	mocName	ReplaceableUnit	moId	${moId}	
	${attributeNameList}	create list	energySavingMode				
	${eSNDict}	__查询节点属性信息	${gndAlias}	${filterDict}	${attributeNameList}		
	[Return]	@{eSNDict}[0]					
							
查询NR小区节能模式	[Arguments]	${gndAlias}	${moId}				
	${filterDict}	create dictionary	mocName	NRCellDU	moId	${moId}	
	${attributeNameList}	create list	cellEsState				
	${eSNDict}	__查询节点属性信息	${gndAlias}	${filterDict}	${attributeNameList}		
	[Return]	@{eSNDict}[0]					
							
查询FDL小区节能模式	[Arguments]	${gndAlias}	${moId}				
	${filterDict}	create dictionary	mocName	CUEUtranCellFDDLTE	moId	${moId}	
	${attributeNameList}	create list	cellEsState				
	${eSNDict}	__查询节点属性信息	${gndAlias}	${filterDict}	${attributeNameList}		
	[Return]	@{eSNDict}[0]					
							
查询TDL小区节能模式	[Arguments]	${gndAlias}	${moId}				
	${filterDict}	create dictionary	mocName	CUEUtranCellTDDLTE	moId	${moId}	
	${attributeNameList}	create list	cellEsState				
	${eSNDict}	__查询节点属性信息	${gndAlias}	${filterDict}	${attributeNameList}		
	[Return]	@{eSNDict}[0]					
							
闭塞后验证NR小区	[Arguments]	${cellAlias}	${ueAlias}	${pdnAlias}			
	闭塞所有LTE小区						
	解闭塞NR小区_多模	${cellAlias}					
	闭塞同频NR小区_多模	${cellAlias}					
	设置UE模式_多模	${ueAlias}	SA				
	设置UE锁NR小区频点和pci_多模	${ueAlias}	${cellAlias}				
	UE去附着	${ueAlias}					
	sleep	2					
	UE同步并接入NR小区成功_多模	${ueAlias}	${cellAlias}				
	${direction}	Set Variable	UL				
	${packageSize}	Set Variable	32				
	${trafficId}	开始Ping	${direction}	${ueAlias}	${pdn}	${packageSize}	60
	...	1000	${True}				
	sleep	60					
	${avg}	${min}	${max}	查询时延	${trafficId}	${direction}	
	${lost_num}	查询丢包数	${trafficId}	${direction}			
	${lost_rate}	查询丢包率	${trafficId}	${direction}			
	结束Ping	${trafficId}	${direction}				
	UE去附着	${ueAlias}					
	Run Keyword If	${lost_rate}>10	Fail	丢包率超过10%			
	[Teardown]	run keywords	解闭塞所有LTE小区				
	...	AND	解闭塞同频NR小区_多模	${cellAlias}			
							
删建FDD小区	[Arguments]	${gnbAlias}					
	删除基站FDD制式MO_多模	${gnbAlias}					
	sleep	60					
	创建基站FDD制式MO_多模	${gnbAlias}					
	${attrs}	Create Dictionary	endcHOwithSnSwch	1	hoBasedEndcCapSwch	1	enDcAnchorHoSwch
	...	1					
	批量修改基站配置_多模	${ENODEB}	ENDCPolicyFDDLTE	${attrs}			
							
删建TDD小区	[Arguments]	${gnbAlias}					
	删除基站TDD制式MO_多模	${gnbAlias}					
	sleep	60					
	创建基站TDD制式MO_多模	${gnbAlias}					
	${attrs}	Create Dictionary	endcHOwithSnSwch	1	hoBasedEndcCapSwch	1	enDcAnchorHoSwch
	...	1					
	批量修改基站配置_多模	${ENODEB}	ENDCPolicyTDDLTE	${attrs}			
							
打开NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	sleep	30					
							
双上联修改光口速率	[Arguments]	${bitRateOnLine}					
	${filterDict}	create dictionary	mocName=RiPort	moId=OF2			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=VBP_1_8				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF9			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=VBP_1_8				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF1			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=PB_10				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF2			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=PB_10				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF3			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=PB_10				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF4			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=PB_10				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF1			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=PB_11				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	${filterDict}	create dictionary	mocName=RiPort	moId=OF2			
	${attrDict}	create dictionary	bitRateOnIrLine=${bitRateOnLine}				
	${keyMoPathDict}	create dictionary	ReplaceableUnit=PB_11				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
	检查激活配置	${GNODEB}					
	sleep	10					
							
STA获取NI值	[Arguments]	${cell}					
	${taskIdA}	创建小区MTS任务_多模	${cell}	CELL_AVG_NI			
	${thred}	开始MTS监控数据_多模	${cell}	${taskIdA}			
	sleep	120					
	${value}	查询MTS的平均NI值_多模	${cell}	${taskIdA}			
	停止小区MTS任务_多模	${cell}	${taskIdA}				
	删除小区MTS任务_多模	${cell}	${taskIdA}				
	[Return]	${value}					
							
修改NR小区频点	[Arguments]	${cellAlias}	${earfcn}				
	${NRPhysicalCellDUid}	获取小区的NRPhysicalCellDUid	${cellAlias}				
	${NRCarrier}	获取小区的NRCarrierid	${cellAlias}				
	修改CarrierDL中参数	${cellAlias}	frequency	${earfcn}	1	NRCarrier	${NRCarrier}
	修改CarrierUL中参数	${cellAlias}	frequency	${earfcn}	1	NRCarrier	${NRCarrier}
							
删建部分NR小区	[Arguments]	${gnbAlias}					
	@{moidList}	获取保存NR小区MOID_多模					
	: FOR	${moid}	IN	@{moidList}			
	\	删除指定NR小区_多模	${gnbAlias}	${moid}			
	sleep	60					
	: FOR	${moid}	IN	@{moidList}			
	\	创建指定NR小区_多模	${gnbAlias}	${moid}			
							
删建部分LTE小区	[Arguments]	${gnbAlias}					
	@{moidList}	获取保存FDD LTE小区的所有MOID_多模					
	: FOR	${moid}	IN	@{moidList}			
	\	删除指定FDD LTE小区_多模	${gnbAlias}	${moid}			
	sleep	60					
	: FOR	${moid}	IN	@{moidList}			
	\	创建指定FDD LTE小区_多模	${gnbAlias}	${moid}			
							
恢复光交换机配置	#修改光交换机接口						
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.1				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.2				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	2.6.6				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	1.4.3				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	1.4.4				
	删除某个端口的光交换连接_多模	${FIBERCONNECT}	5.1.8				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.1-5.1.1				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.2-5.1.2				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.3-5.1.5				
	创建指定的光交换连接_多模	${FIBERCONNECT}	2.6.6-5.1.6				
	sleep	2min					
							
VBP_8_OF2	${VBP8}	根据名称或moId获取实例化单板别名_多模	${ENODEB}	VBPe2+VBP_1_8			
	${dictPb}	create dictionary	hwWorkScence=0	functionMode=255	rate=25		
	${pbAliasList}	创建VBP特定光口下的多级PB级联_多模	${ENODEB}	${VBP8}	OF2	PB1125H	4
	...	${dictPb}					
	${dictPrru}	create dictionary	hwWorkScence=16;32;8192	functionMode=1090519041	rate=25		
	${prruList}	create list					
	${prruList1}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[0]	R8159 *********	${dictPrru}	7
	${prruList2}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[1]	R8159 *********	${dictPrru}	7
	${prruList3}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[2]	R8159 *********	${dictPrru}	8
	${prruList4}	创建指定PB下的多个PRRU_多模	${ENODEB}	@{pbAliasList}[3]	R8159 *********	${dictPrru}	8
	[Return]	${prruList1}	${prruList2}	${prruList3}	${prruList4}		
							
基于pb的位置创建双上联	[Arguments]	${pbPosition}	${VBP}				
	${up}	set variable	OF2				
	${down}	set variable	OF2				
	${pbToDes}	Get Slice From List	${vbpOfTopo}	${pbPosition}			
	Reverse List	${pbToDes}					
	: FOR	${i}	IN RANGE	${pbPosition}			
	\	${up}	set variable if	'@{vbpOfTopo}[${i}]' == '${VBP}'	OF2	OF3	
	\	run keyword and ignore error	创建双上联无线接口连线_多模	@{vbpOfTopo}[${i}]	${up}	@{pbAlias}[${i}]	${down}
	${up}	set variable	OF2				
	: FOR	${pb}	IN	@{pbToDes}			
	\	${up}	set variable if	'${pb}' == '${VBP8}'	OF2	OF3	
	\	run keyword and ignore error	删除无线接口连线_多模	${pb}	${up}		
							
修改PRRU定时节电温度门限_多模	[Arguments]	${enodebAlias}	${status}				
	[Documentation]	【功能说明】					
	...						
	...	修改PRRU定时节电温度门限					
	...						
	...						
	...	【入参】					
	...						
	...	enodebAlias：基站别名					
	...						
	...	status，PRRU定时节电温度门限					
	...						
	...						
	...	【返回】					
	...						
	...	无					
	${params}=	Create Dictionary	deltaTLimit	${status}			
	update_prru_power_saving_multi	${enodebAlias}	${params}				
