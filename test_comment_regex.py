#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

# 测试修复后的Comment注释正则表达式
pattern = re.compile(
    r'^([\s\t]*#.*|[\s\t]*Comment(\s.*|\t.*|$)|#.*[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]+.*)',
    re.UNICODE
)

test_lines = [
    # 应该匹配的注释行
    '# 这是#号注释',
    'Comment    这是Comment注释',
    '    Comment    缩进的Comment注释',
    'Comment',
    'Comment\t制表符分割',
    '    Comment',
    '        Comment    深度缩进',
    'Comment 空格分割的注释',
    '# 中文注释测试',
    'Comment    中文Comment注释',
    '\t# 制表符开头的注释',
    '\tComment\t制表符Comment',
    
    # 不应该匹配的非注释行
    'Log    这不是注释',
    'Set Variable    test_value',
    '    Log    缩进的关键字',
    'Should Be Equal    a    b',
    '${var}=    Set Variable    value',
]

print("测试Comment注释正则表达式修复:")
print("=" * 50)

for line in test_lines:
    match = pattern.match(line)
    status = "✓ 匹配" if match else "✗ 不匹配"
    print(f'{status:8} "{line}"')

print("\n" + "=" * 50)
print("修复验证:")

# 特别测试Comment相关的情况
comment_tests = [
    'Comment',                    # 单独的Comment
    'Comment ',                   # Comment后跟空格
    'Comment\t',                  # Comment后跟制表符
    'Comment    内容',            # Comment后跟空格和内容
    'Comment\t内容',              # Comment后跟制表符和内容
    '    Comment',                # 缩进的Comment
    '\tComment',                  # 制表符缩进的Comment
    '        Comment    深缩进',   # 深度缩进的Comment
]

for line in comment_tests:
    match = pattern.match(line)
    status = "✓" if match else "✗"
    print(f'{status} Comment测试: "{line}"')
