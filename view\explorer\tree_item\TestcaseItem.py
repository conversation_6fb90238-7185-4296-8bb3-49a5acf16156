# coding=utf-8
'''
Created on 2019年11月1日

@author: 10240349
'''
import os

from PyQt5.Qt import QIcon, Qt
from PyQt5.QtWidgets import QMenu

from controller.system_plugin.SignalDistributor import SignalDistributor
from resources.Loader import Loader
from settings.i18n.Loader import LanguageLoader
from utility.ProjectTreeRepository import ProjectTreeRepository
from utility.UIRepository import UIRepository
from view.common.MessageBox import message_box_dec
from view.explorer.tree_item.CheckBoxApi import <PERSON><PERSON><PERSON><PERSON><PERSON>
from view.explorer.tree_item.TreeItem import TreeItem


class TestcaseItem(TreeItem, CheckBoxApi):

    def __init__(self, parent, data_file=None, path=None):
        self.call_by_self = True
        self.data_file = data_file
        super(TestcaseItem, self).__init__(parent, data_file, path)

    def reload_children(self):
        pass

    def set_ui(self):
        self._name = self._data_file.name
        self.setText(0, self._name)
        self.setIcon(0, QIcon(Loader().get_path('TESTCASE')))
        self.setCheckState(0, Qt.Unchecked)

    def update_local_repository(self):
        pass

    # 重写TreeItem的自带的方法setDate
    def setData(self, *args, **kwargs):
        result = TreeItem.setData(self, *args, **kwargs)
        if self.is_check_box(args[1]) and self.call_by_self:
            self.parent().update_checked_status()
        return result

    def get_path(self):
        return self.parent().get_path() + os.path.sep + self._name

    def refresh_children(self, parent):
        pass

    def reload_children_once(self):
        pass

    def show_context_menu(self, parent, pos):
        self._tree = parent
        menu = self._add_action(QMenu(parent))
        menu.setToolTipsVisible(True)
        self._action = menu.exec_(parent.mapToGlobal(pos))
        self._execute_action()

    def _add_action(self, menu):
        self._set_home_page = menu.addAction(LanguageLoader().get('SET_HOME_PAGE'))
        self._copy_item = menu.addAction(LanguageLoader().get('COPY'))
        self._rename_item = menu.addAction(LanguageLoader().get('RENAME'))
        self._delete_item = menu.addAction(LanguageLoader().get('DELETE'))
        self._tick_item = menu.addAction(LanguageLoader().get('TICK'))
        self._untick_item = menu.addAction(LanguageLoader().get('UNTICK'))
        self._move_up_item = menu.addAction(LanguageLoader().get('MOVE_UP'))
        self._move_down_item = menu.addAction(LanguageLoader().get('MOVE_DOWN'))
        
        self._set_home_page.setToolTip('SET_HOME_PAGE_TOOLTIP')
        return menu

    def _execute_action(self):
        if self._action == self._delete_item:
            self._delete()
        elif self._action == self._rename_item:
            self._set_edit_status(self._tree.currentItem().text(0))
        elif self._action == self._copy_item:
            self._copy()
        elif self._action == self._tick_item:
            self._tick_items()
        elif self._action == self._untick_item:
            self._untick_items()
        elif self._action == self._move_up_item:
            self._move_up()
        elif self._action == self._move_down_item:
            self._move_down()
        elif self._action == self._set_home_page:
            print('_set_home_page')
            self._set_home_page_handler()

    @message_box_dec(LanguageLoader().get('DELETE_TIPS'))
    def _delete(self):
        for item in self._tree.selectedItems():
            if isinstance(item, TestcaseItem):
                parent = item.parent()
                index = parent.indexOfChild(item) - parent._variable_num
                parent._data_file.delete_testcase(index)
                parent._data_file.save()
                parent.removeChild(item)
                parent._testcase_num -= 1
        current = ProjectTreeRepository().find("PROJECT_TREE").currentItem()
        SignalDistributor().show(current)
        SignalDistributor().refresh_text_edit_content(current)

    def _copy(self):
        UIRepository().update('copyed_data_file', [item for item in self._tree.selectedItems()])

    def _tick(self):
        self.set_checked(self)

    def _untick(self):
        self.set_unchecked(self)

    def _tick_items(self):
        all_selectes_items = self._tree.selectedItems()
        for i in all_selectes_items:
            if isinstance(i, TestcaseItem):
                i._tick()

    def _untick_items(self):
        all_selectes_items = self._tree.selectedItems()
        for i in all_selectes_items:
            if isinstance(i, TestcaseItem):
                i._untick()

    def _move_up(self):
        parent = self.parent()
        variable_num = parent._variable_num
        if variable_num >= parent.indexOfChild(self):
            return
        parent._data_file.move_up(self._data_file)
        index = parent.indexOfChild(self) - 1
        parent.removeChild(self)
        parent.insertChild(index, self)
        self._refresh_content(self)

    def _move_down(self):
        parent = self.parent()
        variable_num = parent._variable_num
        testcase_num = parent._testcase_num
        if parent.indexOfChild(self) + 1 >= variable_num + testcase_num:
            return
        parent._data_file.move_down(self._data_file)
        index = parent.indexOfChild(self) + 1
        parent.removeChild(self)
        parent.insertChild(index, self)
        self._refresh_content(self)


