# RobotFramework 语法高亮实现说明

## 功能概述

成功实现了完整的 RobotFramework 语法高亮器，支持 Robot Framework 的所有主要语法元素，包括中文内容的完整支持。

## 实现特点

### 1. 完整的语法支持

#### 支持的语法元素
- **章节标题**: `*** Settings ***`, `*** Variables ***`, `*** Test Cases ***`, `*** Keywords ***`
- **测试用例名**: 不以空格或制表符开头的行
- **关键字**: 用户定义的关键字和内置关键字
- **变量**: `${var}`, `@{list}`, `&{dict}`, `%{env}`
- **设置项**: `[Documentation]`, `[Tags]`, `[Setup]`, `[Teardown]` 等
- **注释**: `#` 开头的注释和 `Comment` 关键字
- **字符串**: 引号包围的字符串
- **数字**: 数值常量
- **标签**: `[Tags]` 后的标签内容

#### 颜色方案
| 元素类型 | 颜色 | 字体样式 | 说明 |
|----------|------|----------|------|
| 默认文本 | 黑色 (#000000) | 普通 | 普通文本 |
| 章节标题 | 蓝色 (#0000FF) | 粗体 | *** Settings *** 等 |
| 测试用例名 | 深红色 (#8B0000) | 粗体 | 测试用例标题 |
| 用户关键字 | 紫色 (#800080) | 粗体 | 用户定义的关键字 |
| 内置关键字 | 中蓝色 (#0000CD) | 普通 | Robot Framework 内置关键字 |
| 变量 | 深橙色 (#FF8C00) | 普通 | ${var}, @{list} 等 |
| 设置项 | 靛蓝色 (#4B0082) | 普通 | [Documentation] 等 |
| 注释 | 绿色 (#008000) | 普通 | # 注释内容 |
| 字符串 | 深红色 (#DC143C) | 普通 | "字符串" |
| 数字 | 洋红色 (#FF00FF) | 普通 | 123, 45.67 |
| 标签 | 深绿色 (#006400) | 普通 | [Tags] 后的内容 |

### 2. 中文支持

#### 完整的中文处理
- **中文章节标题**: 支持包含中文的章节标题
- **中文测试用例名**: 完全支持中文测试用例名称
- **中文关键字**: 支持中文用户关键字
- **中文注释**: 支持中文注释内容
- **中文变量**: 支持包含中文的变量名
- **中文设置项**: 支持中文设置项如 `[标签]`

#### 中文正则表达式
```python
# 章节标题匹配（支持中文）
self.section_pattern = re.compile(r'^\*{3}\s*(\w+\s*?){1,3}\*{3}', re.IGNORECASE | re.UNICODE)

# 变量匹配（支持中文）
self.variable_pattern = re.compile(r'\$\{.*?\}|@\{.*?\}|&\{.*?\}|%\{.*?\}', re.UNICODE)

# 设置项匹配（支持中文）
self.setting_pattern = re.compile(r'^\s*\[([\u4e00-\u9fa5A-Za-z]+)\]', re.IGNORECASE | re.UNICODE)

# 中文注释匹配
self.chinese_comment_pattern = re.compile(
    r'^([\s\t]*#.*|Comment\s+.*|#.*[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]+.*)', 
    re.UNICODE
)
```

### 3. 内置关键字库

#### 包含的关键字类别
- **BuiltIn库关键字**: `Log`, `Sleep`, `Should Be Equal` 等基础关键字
- **控制流关键字**: `FOR`, `IF`, `ELSE`, `END`, `WHILE` 等
- **测试用例相关**: `Setup`, `Teardown`, `Test Setup` 等
- **变量操作**: `Set Variable`, `Get Variable Value` 等
- **字符串操作**: `Should Contain`, `Should Start With` 等
- **数值操作**: `Should Be Greater Than`, `Convert To Number` 等
- **文件操作**: `File Should Exist`, `Create File` 等
- **时间操作**: `Get Current Date`, `Add Time To Date` 等

#### 关键字总数
超过 **80个** 常用的 Robot Framework 内置关键字

### 4. 智能语法分析

#### 行级别分析
```python
def styleText(self, start, end):
    # 按行处理文本
    for line in lines:
        # 1. 检查章节标题
        if section_match:
            # 高亮整行为章节标题
        
        # 2. 检查注释行
        elif comment_match:
            # 高亮注释部分
        
        # 3. 检查测试用例名
        elif not line.startswith((' ', '\t')):
            # 高亮为测试用例名
        
        # 4. 处理其他行内容
        else:
            # 详细分析行内元素
```

#### 行内元素分析
```python
def _style_line_content(self, line, line_start):
    # 1. 设置项分析
    # 2. 变量分析
    # 3. 字符串分析
    # 4. 数字分析
    # 5. 内置关键字分析
    # 6. 用户关键字分析
```

### 5. 边界检查和冲突处理

#### 关键字边界检查
```python
def _is_keyword_boundary(self, line, start, end):
    # 检查关键字前后是否有合适的分隔符
    # 确保不会误匹配部分单词
```

#### 上下文检查
```python
def _is_in_variable_or_string(self, line, start, end):
    # 确保数字和关键字不在变量或字符串内被高亮
    # 避免语法冲突
```

## 集成方式

### 1. TextEditor 集成

在 `TextEditor.py` 中的集成代码：
```python
# 导入语法高亮器
from iplatform.highlight.RobotHighlighter import RobotHighlighter

# 根据文件类型选择高亮器
if path.endswith('.robot') or path.endswith('.tsv'):
    self.lexer = RobotHighlighter(self)
else:
    self.lexer = PythonHighlighter(self)

# 设置高亮器
self.setLexer(self.lexer)
```

### 2. 自动检测

- **文件扩展名检测**: 自动识别 `.robot` 和 `.tsv` 文件
- **动态切换**: 打开不同类型文件时自动切换语法高亮器
- **实时更新**: 文件内容变化时实时更新语法高亮

## 性能优化

### 1. 正则表达式预编译
```python
# 在初始化时预编译所有正则表达式
self.section_pattern = re.compile(...)
self.variable_pattern = re.compile(...)
self.setting_pattern = re.compile(...)
```

### 2. 智能处理策略
- **按行处理**: 避免处理整个文件，提高效率
- **早期返回**: 匹配到模式后立即处理，避免不必要的检查
- **边界优化**: 只在必要时进行复杂的边界检查

### 3. 内存优化
- **关键字列表**: 使用列表而不是字典，节省内存
- **局部变量**: 在方法内使用局部变量，及时释放内存

## 测试验证

### 1. 测试文件
创建了 `test_robot_syntax.robot` 包含：
- 所有主要语法元素
- 中文内容测试
- 复杂嵌套结构
- 边界情况测试

### 2. 验证要点
- ✅ 章节标题正确高亮
- ✅ 测试用例名正确高亮
- ✅ 关键字正确分类和高亮
- ✅ 变量正确识别
- ✅ 注释正确处理
- ✅ 中文内容完全支持
- ✅ 语法冲突正确处理

## 扩展性

### 1. 新关键字添加
```python
# 在 builtin_keywords 列表中添加新关键字
self.builtin_keywords.extend([
    'new keyword 1',
    'new keyword 2'
])
```

### 2. 新语法元素
- 可以轻松添加新的语法元素类型
- 支持自定义颜色和字体样式
- 灵活的正则表达式配置

### 3. 主题支持
- 可以通过修改颜色配置支持不同主题
- 支持用户自定义颜色方案

## 使用方法

### 1. 打开 Robot 文件
1. 在项目树中双击 `.robot` 或 `.tsv` 文件
2. 文本编辑器自动应用 RobotFramework 语法高亮
3. 享受彩色的语法高亮效果

### 2. 实时编辑
- 输入内容时实时应用语法高亮
- 支持复制粘贴后的语法高亮更新
- 文件保存时保持语法高亮状态

## 总结

RobotFramework 语法高亮器提供了：

1. **完整的语法支持** - 覆盖所有 Robot Framework 语法元素
2. **优秀的中文支持** - 完全支持中文内容的语法高亮
3. **丰富的关键字库** - 包含80+个内置关键字
4. **智能的语法分析** - 准确识别各种语法结构
5. **良好的性能** - 优化的算法确保流畅的编辑体验
6. **易于扩展** - 灵活的架构支持功能扩展

这个实现大大提升了 Robot Framework 文件的编辑体验，使代码更加易读和易于维护。
