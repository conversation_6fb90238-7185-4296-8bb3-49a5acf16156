# coding=utf-8
'''
Created on 2019年12月2日

@author: 10240349
'''
import os
from settings.SystemSettings import SystemSettings
from utility.PluginRepository import PluginRepository
from controller.system_plugin.SignalDistributor import SignalDistributor
from model.data_file.Repository import LocalKeyWordRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem
from view.explorer.tree_item.PyItem import PyItem
from view.explorer.tree_item.ResourceItem import ResourceItem
from view.explorer.tree_item.SuiteItem import SuiteItem
from view.explorer.tree_item.UserKeywordItem import UserKeywordItem


class SpecifiedKeywordJumper(object):

    def get_keyword_path_from_local_repository(self, name):
        _list = LocalKeyWordRepository().query(name)
        path = ''
        if _list:
            path = _list.get(name)[0].get("path")
        return path

    def get_keyword_item(self, path, keyword_name, _type=UserKeywordItem):
        project_tree = ProjectTreeRepository().find("PROJECT_TREE")
        item = self.query(path, keyword_name)
        if item:
            if isinstance(item, PyItem):
                self._handle_pyitem(project_tree, item, keyword_name)
                print('get_keyword_item')
                print(item.get_name())
                print(item.get_path())
                print(keyword_name)
                return item
            else:
                self._handle_other_item(project_tree, keyword_name, item, _type)
                return item

    def _handle_pyitem(self, project_tree, item, keyword_name=None):
        project_tree.setCurrentItem(item)
        PluginRepository().find('EDIT_TAB').setCurrentIndex(SystemSettings().get_value('TEXT_EDIT_SLOT'))
        PluginRepository().add('LAST_CLICK_TAB', 'TEXT_EDIT')  # 更新当前的tab标签页
        SignalDistributor().show(item)
        if keyword_name:
            SignalDistributor().highlight_selected_keyword(keyword_name)

    def _handle_other_item(self, project_tree, keyword_name, item, _type=UserKeywordItem):
        names = keyword_name.split('.', 1)
        if len(names) > 1:
            names[0] = keyword_name
        for i in range(item.childCount()):
            child = item.child(i)
            for name in names[::-1]:
                if child.get_name().strip() == name and isinstance(child, _type):
                    project_tree.setCurrentItem(child)
                    SignalDistributor().show(child)
                    break

    def query(self, path, keyword_name):
        if path:
            self._path_list = path.split(os.path.sep)
            root_item = ProjectTreeItemRepository().find('PROJECT_TREE_ITEM')
            name = root_item.get_name()
            self._clear_root_path(name)
            index = self._find_index(name)
            if self._path_list[-1] == '__init__.tsv':
                return self._query_init_file(root_item, name, index, keyword_name)
            else:
                return self._query(root_item, name, index)

    def _clear_root_path(self, root_name):
        _list = []
        index = self._path_list.index(root_name)
        _list = self._path_list[index:]
        self._path_list = _list

    def _query_init_file(self, item, name, index, keyword_name):
        for i in range(item.childCount()):
            child = item.child(i)
            if isinstance(child, UserKeywordItem):
                if child.get_name() == keyword_name:
                    return child.parent()
            elif isinstance(child, ProjectTreeItem):
                result = self._parse_project_tree_init_item(child, index, name, keyword_name)
                if result:
                    return result

    def _query(self, item, name, index):
        if not self._is_parse(item):
            self._parse_non_force_parse(item)
        for i in range(item.childCount()):
            child = item.child(i)
            if isinstance(child, ProjectTreeItem):
                result = self._parse_project_tree_item(child, index, name)
                if result:
                    return result
            elif isinstance(child, ResourceItem) or isinstance(child, SuiteItem) or isinstance(child, PyItem):
                result = self._parse_suite_item(child, index)
                if result:
                    return result

    def _parse_init_file_item(self, child, index, name):
        child_index = self._find_index(child.get_name())
        if child_index and child_index == (index + 1):
            if not self._is_parse(child):
                self._parse_non_force_parse(child)
            return self._query(child, name, child_index)
        else:
            return None

    def _parse_project_tree_item(self, child, index, name):
        child_index = self._find_index(child.get_name())
        if child_index and child_index == (index + 1):
            if not self._is_parse(child):
                self._parse_non_force_parse(child)
            return self._query(child, name, child_index)
        else:
            return None

    def _parse_project_tree_init_item(self, child, index, name, keyword_name):
        child_index = self._find_index(child.get_name())
        if child_index and child_index == (index + 1):
            if not self._is_parse(child):
                self._parse_non_force_parse(child)
            return self._query_init_file(child, name, child_index, keyword_name)
        else:
            return None

    def _parse_suite_item(self, child, index):
        child_index = self._find_index(child.get_name())
        if child_index and child_index == (index + 1):
            if not self._is_parse(child):
                self._parse_non_force_parse(child)
            return child
        else:
            return None

    def _is_parse(self, child):
        if child.childCount():
            return True
        else:
            return False

    def _parse_non_force_parse(self, item):
        item._data_file.parse(force_parse=False)
        item.reload_children()

    def _find_index(self, name):
        for n in self._path_list:
            if n == name:
                index = self._path_list.index(n)
                if index != 0 and len(self._path_list[index - 1]) == 0:  # 解决多个文件夹重名跳转失败的问题
                    self._path_list[index] = ''
                return index
