# TextEditor关键字跳转功能实现说明

## 功能概述

在TextEditor中实现了用户关键字跳转功能，当用户按住Ctrl键并且鼠标悬停到关键字时，如果关键字在仓库中存在，则为关键字添加蓝色下划线，用户点击后可以跳转到对应的关键字定义，并将目标关键字滚动到屏幕中间。

## 功能特性

### 1. 智能关键字识别
- **仓库查询**: 检查关键字是否在 `BuildInKeyWordRepository` 或 `LocalKeyWordRepository` 中存在
- **实时检测**: 鼠标悬停时实时检测关键字
- **精确匹配**: 只有在仓库中存在的关键字才会显示下划线

### 2. 视觉反馈
- **蓝色下划线**: 当Ctrl+悬停到可跳转关键字时显示蓝色下划线
- **即时响应**: 按下Ctrl键时立即检查当前鼠标位置的关键字
- **自动清除**: 释放Ctrl键或移开鼠标时自动清除下划线

### 3. 跳转功能
- **Ctrl+点击**: 按住Ctrl键并点击关键字进行跳转
- **精确定位**: 跳转到关键字定义的确切位置
- **居中显示**: 跳转后将目标关键字滚动到屏幕中间

## 技术实现

### 1. 初始化设置

#### 关键字跳转相关初始化
```python
def __init__(self):
    # ... 其他初始化代码
    
    # 关键字跳转相关初始化
    self._ctrl_pressed = False
    self._current_keyword = None
    self._keyword_jumper = SpecifiedKeywordJumper()
    self._underlined_indicators = []  # 存储下划线指示器
    
    # 设置鼠标跟踪
    self.setMouseTracking(True)
```

#### 必要的导入
```python
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QCursor
from model.data_file.Repository import LocalKeyWordRepository, BuildInKeyWordRepository
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper
```

### 2. 事件处理

#### 键盘事件处理
```python
def keyPressEvent(self, event):
    """处理键盘按下事件"""
    if event.key() == Qt.Key_Control:
        self._ctrl_pressed = True
        # 如果鼠标在关键字上，立即显示下划线
        self._check_keyword_under_cursor()
    super().keyPressEvent(event)

def keyReleaseEvent(self, event):
    """处理键盘释放事件"""
    if event.key() == Qt.Key_Control:
        self._ctrl_pressed = False
        # 清除下划线
        self._clear_keyword_underline()
    super().keyReleaseEvent(event)
```

#### 鼠标事件处理
```python
def mouseMoveEvent(self, event):
    """处理鼠标移动事件"""
    if self._ctrl_pressed:
        self._check_keyword_under_cursor()
    super().mouseMoveEvent(event)

def mousePressEvent(self, event):
    """处理鼠标点击事件"""
    if event.button() == Qt.LeftButton and self._ctrl_pressed:
        keyword = self._get_keyword_at_cursor()
        if keyword and self._is_keyword_in_repository(keyword):
            self._jump_to_keyword(keyword)
            return  # 阻止默认的点击行为
    super().mousePressEvent(event)
```

### 3. 关键字检测

#### 获取光标位置的关键字
```python
def _get_keyword_at_cursor(self):
    """获取光标位置的关键字"""
    try:
        # 获取鼠标位置
        cursor_pos = QCursor.pos()
        widget_pos = self.mapFromGlobal(cursor_pos)
        
        # 使用 SendScintilla 获取鼠标位置对应的字符位置
        char_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINTCLOSE, widget_pos.x(), widget_pos.y())
        if char_pos < 0:
            return None
        
        # 获取行号和列号
        line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, char_pos)
        col = char_pos - self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)
        
        # 获取该行的文本
        line_text = self.text(line)
        if not line_text or col >= len(line_text):
            return None
        
        # 查找关键字边界
        start = col
        end = col
        
        # 向前查找关键字开始位置
        while start > 0 and (line_text[start - 1].isalnum() or line_text[start - 1] in '_'):
            start -= 1
        
        # 向后查找关键字结束位置
        while end < len(line_text) and (line_text[end].isalnum() or line_text[end] in '_'):
            end += 1
        
        # 提取关键字
        keyword = line_text[start:end].strip()
        return keyword if keyword else None
        
    except Exception as e:
        print(f"获取光标位置关键字时出错: {e}")
        return None
```

#### 关键字仓库查询
```python
def _is_keyword_in_repository(self, keyword):
    """检查关键字是否在仓库中"""
    try:
        # 检查内置关键字
        builtin_result = BuildInKeyWordRepository().query(keyword)
        if builtin_result and builtin_result.get(keyword):
            return True
        
        # 检查本地关键字（包括用户关键字和库关键字）
        local_result = LocalKeyWordRepository().query(keyword)
        if local_result and local_result.get(keyword):
            return True
            
        return False
    except Exception as e:
        print(f"检查关键字时出错: {e}")
        return False
```

### 4. 视觉反馈

#### 添加下划线指示器
```python
def _add_keyword_underline(self, keyword):
    """为关键字添加下划线"""
    try:
        # 获取鼠标位置和关键字位置
        cursor_pos = QCursor.pos()
        widget_pos = self.mapFromGlobal(cursor_pos)
        
        # 使用 SendScintilla 获取精确位置
        char_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINTCLOSE, widget_pos.x(), widget_pos.y())
        if char_pos < 0:
            return
        
        # 获取行号和列号
        line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, char_pos)
        col = char_pos - self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)
        
        # 查找关键字边界并设置下划线
        # ... 边界查找逻辑
        
        # 设置蓝色下划线指示器
        indicator_id = 1
        self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
        self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, indicator_id, QsciScintilla.INDIC_PLAIN)
        self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, indicator_id, 0x0000FF)  # 蓝色
        
        # 添加下划线
        self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_pos, end_pos - start_pos)
        
    except Exception as e:
        print(f"添加关键字下划线时出错: {e}")
```

#### 清除下划线
```python
def _clear_keyword_underline(self):
    """清除关键字下划线"""
    try:
        indicator_id = 1
        self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
        
        # 清除所有下划线
        for start_pos, length in self._underlined_indicators:
            self.SendScintilla(QsciScintilla.SCI_INDICATORCLEARRANGE, start_pos, length)
        
        self._underlined_indicators.clear()
        
    except Exception as e:
        print(f"清除关键字下划线时出错: {e}")
```

### 5. 关键字跳转

#### 跳转实现
```python
def _jump_to_keyword(self, keyword):
    """跳转到关键字定义"""
    try:
        # 获取关键字路径
        path = self._keyword_jumper.get_keyword_path_from_local_repository(keyword)
        if path:
            # 使用SpecifiedKeywordJumper进行跳转
            self._keyword_jumper.get_keyword_item(path, keyword)
            print(f"跳转到关键字: {keyword} at {path}")
        else:
            print(f"未找到关键字定义: {keyword}")
            
    except Exception as e:
        print(f"跳转到关键字时出错: {e}")
```

#### 跳转流程
1. **获取关键字路径**: 通过 `LocalKeyWordRepository` 查询关键字定义的文件路径
2. **定位关键字项**: 使用 `SpecifiedKeywordJumper.get_keyword_item()` 定位到具体的关键字项
3. **切换视图**: 自动切换到对应的编辑页签或文本编辑页签
4. **滚动定位**: 将目标关键字滚动到屏幕中间

## 支持的关键字类型

### 1. 内置关键字
- Robot Framework 核心关键字
- 如: `Log`, `Set Variable`, `Should Be Equal`, `Run Keyword`

### 2. 库关键字
- 导入库中的关键字
- 如: `Open Browser` (SeleniumLibrary), `Create List` (Collections)

### 3. 用户关键字
- 项目中自定义的关键字
- 在 `*** Keywords ***` 部分定义的关键字

## 使用方法

### 1. 基本操作
1. **按住Ctrl键**: 激活关键字检测模式
2. **鼠标悬停**: 将鼠标悬停到关键字上
3. **查看下划线**: 如果关键字在仓库中存在，会显示蓝色下划线
4. **点击跳转**: 保持Ctrl键按下，点击关键字进行跳转

### 2. 视觉提示
- **蓝色下划线**: 表示关键字可以跳转
- **无下划线**: 表示关键字不在仓库中或不是有效关键字
- **即时反馈**: 按下/释放Ctrl键时立即更新显示

### 3. 跳转效果
- **自动切换页签**: 跳转到对应的编辑页签
- **精确定位**: 定位到关键字定义的确切位置
- **居中显示**: 目标关键字显示在屏幕中间

## 技术要点

### 1. 位置计算
- 使用 `SendScintilla` API 进行精确的位置计算
- `SCI_POSITIONFROMPOINTCLOSE`: 获取鼠标位置对应的字符位置
- `SCI_LINEFROMPOSITION`: 获取字符位置对应的行号

### 2. 指示器管理
- 使用 QsciScintilla 的指示器系统显示下划线
- 指示器ID 1 专门用于关键字下划线
- 自动管理指示器的创建和清除

### 3. 事件协调
- 键盘事件和鼠标事件的协调处理
- 状态管理确保下划线的正确显示和清除
- 防止事件冲突和重复处理

### 4. 性能优化
- 只在Ctrl键按下时进行关键字检测
- 缓存当前关键字状态，避免重复处理
- 异常处理确保功能的稳定性

## 错误处理

### 1. 位置计算错误
- 鼠标位置超出文本范围时的处理
- 无效字符位置的检查和处理

### 2. 仓库查询错误
- 仓库不可用时的降级处理
- 查询异常的捕获和日志记录

### 3. 跳转错误
- 关键字路径不存在时的处理
- 跳转失败时的用户提示

## 总结

TextEditor关键字跳转功能提供了完整的关键字导航体验：

1. **智能识别**: 只有在仓库中存在的关键字才会显示跳转提示
2. **视觉反馈**: 蓝色下划线清晰标识可跳转的关键字
3. **精确跳转**: 准确定位到关键字定义并居中显示
4. **用户友好**: 符合现代IDE的交互习惯
5. **性能优化**: 高效的事件处理和状态管理

这个功能大大提升了Robot Framework代码的导航效率，让用户能够快速在关键字定义之间跳转！🎉
