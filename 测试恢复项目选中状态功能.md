# 测试恢复项目选中状态功能

## 功能说明

在 `refresh_children` 和 `refresh_children_without_parse` 方法中增加了恢复子项目的**选中状态**（selection state）功能。

**注意**: 这里的"选中状态"指的是鼠标单击项目后在树形控件中高亮显示的状态，不是勾选框的勾选状态。

## 测试步骤

### 1. 准备测试环境
1. 打开一个包含多个子项的测试套件文件
2. 确保测试套件中有多个子项（测试用例、关键字、变量等）

### 2. 设置初始选中状态
1. 在项目树中单击选择某个子项（例如某个测试用例）
2. 观察该项目在树形控件中高亮显示
3. 记录当前选中的项目名称

### 3. 测试 refresh_children 功能
1. 右键点击测试套件项
2. 选择"刷新"或触发 `refresh_children` 方法
3. 观察刷新后的项目选中状态
4. **验证**: 之前选中的项目应该仍然保持选中状态（高亮显示）

### 4. 测试 refresh_children_without_parse 功能
1. 触发 `refresh_children_without_parse` 方法
2. 观察刷新后的项目选中状态
3. **验证**: 之前选中的项目应该仍然保持选中状态

### 5. 测试不同类型的子项
1. **测试用例项**: 选中某个测试用例，刷新后验证
2. **关键字项**: 选中某个用户关键字，刷新后验证
3. **变量项**: 选中某个变量，刷新后验证

### 6. 测试边界情况
1. **无选中项**: 刷新前没有选中任何子项
2. **选中项被删除**: 刷新前后子项数量发生变化
3. **选中项重命名**: 子项名称发生变化的情况

## 预期结果

### ✅ 正常情况
- 刷新后，之前选中的子项保持选中状态（高亮显示）
- 文本编辑器显示选中项的内容
- 右侧面板显示选中项的详细信息

### ✅ 边界情况
- 如果之前没有选中项，刷新后也不会有选中项
- 如果选中的项目被删除，不会出现错误
- 如果找不到匹配的项目，会有相应的日志信息

### ✅ 错误处理
- 如果恢复过程中出现异常，不会影响整个刷新流程
- 错误信息会被正确记录和显示

## 技术实现验证

### 1. 选中状态获取机制
```python
def _get_selected_item_name(self):
    """获取当前选中的子项名称"""
    try:
        tree = ProjectTreeRepository().find("PROJECT_TREE")
        if tree:
            current_item = tree.currentItem()
            if current_item and current_item.parent() == self:
                # 当前选中的项是这个节点的子项
                return current_item.get_name()
    except Exception as e:
        print(f"获取选中项目名称时出错: {e}")
    return None
```

**验证要点**:
- 只获取当前节点的直接子项的选中状态
- 如果选中的不是子项，返回 None
- 异常处理确保获取过程的稳定性

### 2. 选中状态恢复机制
```python
def _restore_selected_item(self, selected_item_name):
    """恢复选中的子项"""
    if not selected_item_name:
        return
    
    try:
        tree = ProjectTreeRepository().find("PROJECT_TREE")
        if tree:
            # 查找匹配名称的子项
            for i in range(self.childCount()):
                child = self.child(i)
                if child.get_name() == selected_item_name:
                    # 设置为当前选中项
                    tree.setCurrentItem(child)
                    # 触发显示更新
                    SignalDistributor().show(child)
                    print(f"已恢复选中项目: {selected_item_name}")
                    return
            print(f"未找到要恢复的选中项目: {selected_item_name}")
    except Exception as e:
        print(f"恢复选中项目时出错: {e}")
```

**验证要点**:
- 按名称匹配查找要恢复的子项
- 使用 `tree.setCurrentItem()` 设置选中状态
- 使用 `SignalDistributor().show()` 触发界面更新
- 如果找不到匹配项，会记录日志但不会出错

### 3. 调用时机验证
```python
def refresh_children(self, force_parse=True):
    checked_states = self._get_checked_states()        # 1. 保存勾选状态
    selected_item_name = self._get_selected_item_name() # 2. 保存选中状态
    self.remove_children()                             # 3. 移除子项
    self._data_file.parse(force_parse=True)            # 4. 重新解析
    self.reload_children()                             # 5. 重新加载子项
    SignalDistributor().show(self)                     # 6. 显示更新
    self._remove_exective_testcases()                  # 7. 清理执行用例
    self.set_name_without_star()                       # 8. 更新名称
    self._restore_checked_states(checked_states)       # 9. 恢复勾选状态
    self._restore_selected_item(selected_item_name)    # 10. 恢复选中状态
```

**验证要点**:
- 选中状态保存在子项移除之前进行
- 选中状态恢复在所有其他操作完成之后进行
- 恢复是整个刷新流程的最后一步

## 支持的子项类型

### ✅ 支持选中状态恢复
- **TestcaseItem**: 测试用例项
- **UserKeywordItem**: 用户关键字项
- **VariableItem**: 变量项
- **ResourceItem**: 资源文件项
- **PyItem**: Python文件项
- **SuiteItem**: 测试套件项
- **ProjectTreeItem**: 项目树项

**说明**: 所有继承自 `TreeItem` 的子项都支持选中状态恢复，因为选中状态是 QTreeWidget 的基本功能。

## 用户体验改进

### 改进前
1. 用户选中某个测试用例
2. 刷新测试套件
3. 选中状态丢失，需要重新选择
4. 文本编辑器可能显示错误的内容

### 改进后
1. 用户选中某个测试用例
2. 刷新测试套件
3. 选中状态自动恢复
4. 文本编辑器继续显示正确的内容

## 测试用例

### 用例1: 基本功能测试
1. 选中测试用例A
2. 刷新测试套件
3. 验证测试用例A仍然选中

### 用例2: 不同类型子项测试
1. 分别选中测试用例、关键字、变量
2. 刷新测试套件
3. 验证各类型子项的选中状态都能正确恢复

### 用例3: 无选中项测试
1. 不选中任何子项（或选中父项）
2. 刷新测试套件
3. 验证刷新后没有子项被选中

### 用例4: 选中项不存在测试
1. 选中某个子项
2. 手动删除该子项的数据
3. 刷新测试套件
4. 验证不会出现错误，日志中有相应信息

### 用例5: 异常处理测试
1. 在恢复过程中模拟异常情况
2. 验证错误被正确处理和记录
3. 验证刷新流程能够继续完成

## 日志信息

### 成功恢复
```
已恢复选中项目: test_case_1
```

### 未找到匹配项
```
未找到要恢复的选中项目: deleted_test_case
```

### 异常情况
```
获取选中项目名称时出错: [具体错误信息]
恢复选中项目时出错: [具体错误信息]
```

## 与勾选状态的区别

| 特性 | 选中状态 (Selection) | 勾选状态 (Checked) |
|------|---------------------|-------------------|
| 表现形式 | 高亮显示 | 勾选框 |
| 数量限制 | 单选 | 多选 |
| 触发方式 | 鼠标单击 | 点击勾选框 |
| 用途 | 查看/编辑内容 | 批量操作 |
| 支持类型 | 所有子项 | 仅支持勾选的子项 |

## 总结

通过这个功能，用户在刷新项目树时能够保持当前的工作状态，避免了重新选择项目的麻烦，显著提升了用户体验。

这个功能对于以下场景特别有用：
- 正在编辑某个测试用例时需要刷新
- 在查看某个关键字时项目结构发生变化
- 频繁刷新项目树的开发工作流程
