# Python函数跳转功能说明

## 功能概述

已为TextEditor添加了Python函数按Ctrl+左键点击的跳转功能，用户可以像在现代IDE中一样快速导航到函数定义。

## 修改的文件

- `controller/system_plugin/text_edit/TextEditor.py`

## 新增功能

### 1. 鼠标事件处理
- **方法**: `mousePressEvent(self, event)`
- **功能**: 检测Ctrl+左键点击组合，触发跳转功能

### 2. 点击位置文本提取
- **方法**: `_get_function_name_at_position(self, pos)`
- **功能**:
  - 使用QsciScintilla的SendScintilla API获取鼠标位置
  - 智能提取点击位置的函数名
  - 验证函数名的有效性

### 3. 函数定义查找
- **方法**: `_find_function_definition_in_current_file(self, function_name)`
- **功能**:
  - 使用正则表达式在当前文件中查找函数定义
  - 支持各种函数定义格式（带空格、缩进等）

### 4. 跳转功能
- **方法**: `_jump_to_function_definition(self, function_name)`
- **功能**:
  - 优先在当前文件中查找函数定义
  - 如果找到，直接跳转并高亮显示
  - 如果没找到，使用现有的关键字跳转机制

### 5. 集成现有系统
- **方法**: `_try_keyword_jump(self, function_name)`
- **功能**:
  - 利用项目中已有的SpecifiedKeywordJumper类
  - 支持跨文件的函数跳转

## 使用方法

1. 在TextEditor中打开Python文件
2. 按住Ctrl键，鼠标悬停在函数名上会显示蓝色下划线
3. 鼠标光标会变成手型指针
4. 左键点击函数名，系统会自动跳转到函数定义位置
5. 跳转后光标定位到函数定义行，不会选中文字

## 支持的功能

- ✅ 当前文件内的函数跳转
- ✅ 类方法跳转
- ✅ 全局函数跳转
- ✅ 跨文件函数跳转（通过现有的关键字跳转机制）
- ✅ 智能函数名识别
- ✅ 错误处理和异常捕获
- ✅ Ctrl键按下时显示蓝色下划线提示
- ✅ 鼠标光标变化（手型指针）
- ✅ 跳转后不选中文字
- ✅ 支持中文关键字和函数名
- ✅ 正确处理中文字符的下划线长度

## 技术实现细节

### API修复
原始实现中使用了错误的API调用：
```python
# 错误的方式
line, index = self.lineIndexFromPosition(pos)  # pos是QPoint对象
```

修复后使用正确的QsciScintilla API：
```python
# 正确的方式
position = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINT, pos.x(), pos.y())
line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, position)
column = self.SendScintilla(QsciScintilla.SCI_GETCOLUMN, position)
```

### 函数名识别算法
1. 从点击位置开始，向前和向后扩展
2. 支持字母、数字、下划线和中文字符（ASCII码 > 127）
3. 验证函数名必须以字母、下划线或中文字符开头

### 中文字符支持
新增了专门的中文字符处理：
```python
def _is_identifier_char(self, char):
    """检查字符是否是标识符字符（支持中文）"""
    return char.isalnum() or char == '_' or ord(char) > 127  # 支持中文等非ASCII字符

def _char_index_to_byte_position(self, line, char_index):
    """将字符索引转换为字节位置，正确处理中文字符"""
    text_part = line_text[:char_index]
    byte_length = len(text_part.encode('utf-8'))
    return line_start_pos + byte_length
```

### 函数定义查找
使用正则表达式匹配函数定义：
```python
pattern = r'^\s*def\s+' + re.escape(function_name) + r'\s*\('
```

### 下划线显示功能
使用QsciScintilla的指示器（Indicator）功能：
```python
# 设置指示器样式
indicator_id = 8
self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, indicator_id, QsciScintilla.INDIC_PLAIN)
self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, indicator_id, 0x0000FF)  # 蓝色

# 添加下划线
self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_pos, length)
```

### 键盘和鼠标事件处理
- `keyPressEvent`: 检测Ctrl键按下
- `keyReleaseEvent`: 检测Ctrl键释放，清除下划线
- `mouseMoveEvent`: 检测鼠标移动，显示/隐藏下划线
- `mousePressEvent`: 处理Ctrl+点击跳转

### 跳转优化
移除了跳转后的文字选中：
```python
# 原来的代码（会选中文字）
self.setSelection(line_number, 0, line_number, len(self.text(line_number)))

# 新的代码（只定位光标）
self.setCursorPosition(line_number, 0)
```

## 测试文件

创建了测试文件：

### `test_python_jump.py`
包含英文函数测试：
- 全局函数
- 类方法
- 函数调用链
- 各种函数定义格式

### `test_chinese_keywords.py`
包含中文关键字测试：
- 纯中文函数名：`测试函数()`
- 中英文混合：`中英文混合函数()`
- 带下划线：`带下划线的中文函数_test()`
- 中文类和方法：`测试类.中文方法()`

## 错误处理

所有方法都包含完整的异常处理，确保：
- 不会因为错误的点击位置而崩溃
- 提供有用的错误信息用于调试
- 优雅地处理边界情况

## 兼容性

- 完全兼容现有的TextEditor功能
- 不影响其他鼠标事件处理
- 集成现有的关键字跳转系统
