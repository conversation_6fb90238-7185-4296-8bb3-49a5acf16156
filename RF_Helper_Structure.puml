@startuml RF_Helper系统架构图
left to right direction
title RF Helper系统架构

skinparam component {
  BackgroundColor #F5F5F5
  BorderColor #333333
  ArrowColor #666666
  FontName "Microsoft YaHei"
}

package "RF Helper系统" {
  [核心控制器] #FFD700
  [API服务模块] #87CEEB {
    [AI服务接口]
    [数据库接口]
    [版本控制接口]
  }
  [线程管理模块] #98FB98 {
    [AI处理线程]
    [配置加载线程]
    [数据库线程]
  }
  [用户界面模块] #FFA07A {
    [登录界面]
    [设置界面]
    [主操作界面]
  }
  [资源管理模块] #DDA0DD
}

[核心控制器] --> [API服务模块]
[核心控制器] --> [线程管理模块] 
[核心控制器] --> [用户界面模块]
[核心控制器] --> [资源管理模块]

[用户界面模块] --> [线程管理模块]
[线程管理模块] --> [API服务模块]

note right of [核心控制器]
  **系统说明**
  RF Helper是一个自动化测试辅助工具
  主要功能:
  - 通过API与各种服务交互
  - 多线程处理测试任务
  - 提供友好的用户界面
  - 管理测试资源和配置
end note

@enduml
