*** Settings ***
Resource	D:/script_v2/5GNR/testlib5g/infrastructure/resource/resource.tsv
Resource	D:/script_v2/5GNR/Test/userkeywords/basic_multi/resource.tsv
Resource	D:/script_v2/5GNR/Test/userkeywords/basic/utility/excelHandle.tsv
Library	Test.py
Library	VersionDownloadService.py



*** Keywords ***
ITRAN版本升级
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	ITRAN基站开站	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	重复执行_多模	30	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

SDR版本升级
	[Arguments]	${enodebAlias}	${pkgVersion}	${configDataVersion}	${artList}
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级PKG包_SDR_多模	${enodebAlias}	${pkgVersion}
	run keyword if	'model_nfc_info_is_null' in '${reason}'	从失败信息中匹配模型号并入库模型包	${enodebAlias}	${reason}
	${reason}	run keyword if	'model_nfc_info_is_null' in '${reason}'	重新升级PKG包_SDR_多模	${enodebAlias}	${pkgVersion}	ELSE
	...	set variable	${reason}
	Run Keyword If	'upgrade sequence' in '${reason}'	SDR开站升级	${enodebAlias}	${pkgVersion}	${configDataVersion}
	Sleep	1100
	${version}	重复执行_多模	30	查询基站运行版本号_SDR_多模	${enodebAlias}
	${version}	Run Keyword If	'V' in '${version}'	evaluate	'${version}'[1:]	ELSE	set variable
	...	${version}
	${pkgVersion}	Run Keyword If	'V' in '${pkgVersion}'	evaluate	'${pkgVersion}'[1:]	ELSE	set variable
	...	${pkgVersion}
	Should Be True	'${pkgVersion}'=='${version}'	升级失败，期望版本${pkgVersion}，实际版本${version}
	Run Keyword And Ignore Error	备份SDR开站配置xml	${enodebAlias}

ITRAN基站开站
	[Arguments]	${enodebAlias}	${tarName}
	run keyword and ignore error	拆分NR超级小区	${enodebAlias}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	保存基站MO_多模	${enodebAlias}	GlobalConfigLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	保存基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE
	保存基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE
	保存基站MO_多模	${enodebAlias}	SsbMeasInfo
	保存基站MO_多模	${enodebAlias}	NRCellRelation
	保存基站MO_多模	${enodebAlias}	ExternalNRCellCU
	保存基站MO_多模	${enodebAlias}	NRFreq
	保存基站MO_多模	${enodebAlias}	FrequencyBandList
	保存基站MO_多模	${enodebAlias}	NRFreqRelation
	保存基站MO_多模	${enodebAlias}	InterFHoA1A2
	保存基站MO_多模	${enodebAlias}	CoverMobilityCtrl
	保存基站MO_多模	${enodebAlias}	NRRadioInfrastructure
	保存基站MO_多模	${enodebAlias}	X2Ap
	保存基站MO_多模	${enodebAlias}	ENDCX2Ap
	保存基站MO_多模	${enodebAlias}	Ip
	保存基站MO_多模	${enodebAlias}	Sctp
	保存基站MO_多模	${enodebAlias}	FrequencyBandOffset
	保存基站MO_多模	${enodebAlias}	PrruPowerSupplyConfig
	保存基站MO_多模	${enodebAlias}	GNBCUUPFunction
	保存基站MO_多模	${enodebAlias}	GNBCUCPFunction
	保存基站MO_多模	${enodebAlias}	GNBDUFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdListUP
	保存基站MO_多模	${enodebAlias}	PlmnIdListCU
	保存基站MO_多模	${enodebAlias}	PlmnIdList
	保存基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	保存基站MO_多模	${enodebAlias}	SliceProfile
	保存基站MO_多模	${enodebAlias}	NSSAI
	保存基站MO_多模	${enodebAlias}	NgAp
	保存基站MO_多模	${enodebAlias}	Clk
	保存基站MO_多模	${enodebAlias}	ClockSyncConfig
	保存基站MO_多模	${enodebAlias}	XnAp
	保存基站MO_多模	${enodebAlias}	NRFreqObj
	保存基站MO_多模	${enodebAlias}	ExternalNrCell
	保存基站MO_多模	${enodebAlias}	EnDCCtrl
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}
	修改开站模板文件_多模	${filePath}	${tarName}
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	创建IPv4操作维护通道_多模	${enodebAlias}
	恢复基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	恢复基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	恢复基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	恢复基站MO_多模	${enodebAlias}	GlobalConfigLTE
	恢复基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	恢复基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	恢复基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	恢复基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	恢复基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE
	恢复基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE
	恢复基站MO_多模	${enodebAlias}	NRFreq
	恢复基站MO_多模	${enodebAlias}	FrequencyBandList
	恢复基站MO_多模	${enodebAlias}	ExternalNRCellCU
	恢复基站MO_多模	${enodebAlias}	SsbMeasInfo
	恢复基站MO_多模	${enodebAlias}	NRCellRelation
	恢复基站MO_多模	${enodebAlias}	NRFreqRelation
	恢复基站MO_多模	${enodebAlias}	InterFHoA1A2
	恢复基站MO_多模	${enodebAlias}	CoverMobilityCtrl
	恢复基站MO_多模	${enodebAlias}	FrequencyBandOffset
	恢复基站MO_多模	${enodebAlias}	XnAp
	恢复基站MO_多模	${enodebAlias}	NRFreqObj
	恢复基站MO_多模	${enodebAlias}	ExternalNrCell
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	EnDCCtrl
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NRRadioInfrastructure
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	X2Ap
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	ENDCX2Ap
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	Ip
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	Sctp
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	PrruPowerSupplyConfig
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	GNBCUUPFunction
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	GNBCUCPFunction
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	GNBDUFunction
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	PlmnIdListUP
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	PlmnIdListCU
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	PlmnIdList
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	SliceProfile
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NSSAI
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NgAp
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	Clk
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	ClockSyncConfig
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE

9920版本升级
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	9920基站开站	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

9920基站开站
	[Arguments]	${enodebAlias}	${tarName}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	保存基站MO_多模	${enodebAlias}	GlobalConfigLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	保存基站MO_多模	${enodebAlias}	GNBCUUPFunction
	保存基站MO_多模	${enodebAlias}	GNBCUCPFunction
	保存基站MO_多模	${enodebAlias}	GNBDUFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdListUP
	保存基站MO_多模	${enodebAlias}	PlmnIdListCU
	保存基站MO_多模	${enodebAlias}	PlmnIdList
	保存基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	保存基站MO_多模	${enodebAlias}	SliceProfile
	保存基站MO_多模	${enodebAlias}	NSSAI
	保存基站MO_多模	${enodebAlias}	NgAp
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}	V9920
	修改开站模板文件_多模	${filePath}	${tarName}
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	创建IPv4操作维护通道_多模	${enodebAlias}
	创建基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	创建基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	创建基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	创建基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	创建基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	修改基站MO_多模	${enodebAlias}	GlobalConfigLTE
	修改基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	修改基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	修改基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	修改基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBCUUPFunction
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBCUCPFunction
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBDUFunction
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdListUP
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdListCU
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdList
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	SliceProfile
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NSSAI
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NgAp

获取IP
	[Return]	${value}
	${value}	get_ip_multi

拆分NR超级小区
	[Arguments]	${enodebAlias}
	@{ids}	查询NR超级小区主小区IDS_多模	${enodebAlias}
	FOR	${id}	IN	@{ids}
	拆分NR超级小区_多模	${enodebAlias}	${id}

ITRAN定位环境版本升级
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	ITRAN定位环境基站开站	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

ITRAN定位环境基站开站
	[Arguments]	${enodebAlias}	${tarName}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	PositionAssistance
	保存基站MO_多模	${enodebAlias}	DRXCfg
	保存基站MO_多模	${enodebAlias}	NRMecsAp
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}
	修改开站模板文件_多模	${filePath}	${tarName}
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4
	写入XLSX单元格	${filePath}	Sctp	5101	7	9
	写入XLSX单元格	${filePath}	Sctp	460-01	7	10
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	创建IPv4操作维护通道_多模	${enodebAlias}
	修改基站MO_多模	${enodebAlias}	PositionAssistance
	修改基站MO_多模	${enodebAlias}	DRXCfg
	Comment	删除基站MO_多模	${enodebAlias}	NgAp	GNBCUCPFunction=460-01_5101,EPNgC=1,NgAp=1
	创建基站MO_多模	${enodebAlias}	NRMecsAp

补丁版本升级
	[Arguments]	${enodebAlias}	${version}	${waitTime}=60
	下载补丁包_SDR_多模	${enodebAlias}	${version}.pkg
	sleep	${waitTime}

ITRAN定位环境基站开站NRPPA新协议
	[Arguments]	${enodebAlias}	${tarName}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	PositionAssistance
	保存基站MO_多模	${enodebAlias}	DRXCfg
	保存基站MO_多模	${enodebAlias}	NRMecsAp
	保存基站MO_多模	${enodebAlias}	RANIntelligentFunction
	保存基站MO_多模	${enodebAlias}	GnbObject
	保存基站MO_多模	${enodebAlias}	EPE2C
	保存基站MO_多模	${enodebAlias}	NetworkInterfaceFunction
	保存基站MO_多模	${enodebAlias}	VIPFunction
	保存基站MO_多模	${enodebAlias}	KPMFunction
	保存基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	保存基站MO_多模	${enodebAlias}	GlobalConfigLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	保存基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE
	保存基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreq
	保存基站MO_多模	${enodebAlias}	FrequencyBandList
	保存基站MO_多模	${enodebAlias}	ExternalNRCellCU
	保存基站MO_多模	${enodebAlias}	SsbMeasInfo
	保存基站MO_多模	${enodebAlias}	NRCellRelation
	保存基站MO_多模	${enodebAlias}	NRFreqRelation
	保存基站MO_多模	${enodebAlias}	InterFHoA1A2
	保存基站MO_多模	${enodebAlias}	CoverMobilityCtrl
	保存基站MO_多模	${enodebAlias}	GNBCUCPFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdListCU
	保存基站MO_多模	${enodebAlias}	EPNgC
	保存基站MO_多模	${enodebAlias}	NgAp
	保存基站MO_多模	${enodebAlias}	GNBCUUPFunction
	保存基站MO_多模	${enodebAlias}	GNBDUFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdList
	保存基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	保存基站MO_多模	${enodebAlias}	SliceProfile
	保存基站MO_多模	${enodebAlias}	NSSAI
	保存基站MO_多模	${enodebAlias}	NRRadioInfrastructure
	保存基站MO_多模	${enodebAlias}	GlobalSwitchInformation
	保存基站MO_多模	${enodebAlias}	Sctp
	保存基站MO_多模	${enodebAlias}	X2Ap
	保存基站MO_多模	${enodebAlias}	PrruTxRxGroup
	保存基站MO_多模	${enodebAlias}	SRSConfig
	保存基站MO_多模	${enodebAlias}	GlobalSwitchInformation
	保存基站MO_多模	${enodebAlias}	Clk
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}
	修改开站模板文件_多模	${filePath}	${tarName}
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	创建IPv4操作维护通道_多模	${enodebAlias}
	恢复基站MO_多模	${enodebAlias}	PositionAssistance
	恢复基站MO_多模	${enodebAlias}	DRXCfg
	恢复基站MO_多模	${enodebAlias}	NRMecsAp
	恢复基站MO_多模	${enodebAlias}	RANIntelligentFunction
	恢复基站MO_多模	${enodebAlias}	GnbObject
	恢复基站MO_多模	${enodebAlias}	EPE2C
	恢复基站MO_多模	${enodebAlias}	NetworkInterfaceFunction
	恢复基站MO_多模	${enodebAlias}	VIPFunction
	恢复基站MO_多模	${enodebAlias}	KPMFunction
	恢复基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	恢复基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	恢复基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	恢复基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	恢复基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	恢复基站MO_多模	${enodebAlias}	GlobalConfigLTE
	恢复基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	恢复基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	恢复基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	恢复基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	恢复基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE
	恢复基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE
	恢复基站MO_多模	${enodebAlias}	NRFreq
	恢复基站MO_多模	${enodebAlias}	FrequencyBandList
	恢复基站MO_多模	${enodebAlias}	ExternalNRCellCU
	恢复基站MO_多模	${enodebAlias}	SsbMeasInfo
	恢复基站MO_多模	${enodebAlias}	NRCellRelation
	恢复基站MO_多模	${enodebAlias}	NRFreqRelation
	恢复基站MO_多模	${enodebAlias}	InterFHoA1A2
	恢复基站MO_多模	${enodebAlias}	CoverMobilityCtrl
	恢复基站MO_多模	${enodebAlias}	GNBCUCPFunction
	恢复基站MO_多模	${enodebAlias}	PlmnIdListCU
	恢复基站MO_多模	${enodebAlias}	EPNgC
	恢复基站MO_多模	${enodebAlias}	NgAp
	恢复基站MO_多模	${enodebAlias}	GNBCUUPFunction
	恢复基站MO_多模	${enodebAlias}	GNBDUFunction
	恢复基站MO_多模	${enodebAlias}	PlmnIdList
	恢复基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	恢复基站MO_多模	${enodebAlias}	SliceProfile
	恢复基站MO_多模	${enodebAlias}	NSSAI
	恢复基站MO_多模	${enodebAlias}	NRRadioInfrastructure
	恢复基站MO_多模	${enodebAlias}	GlobalSwitchInformation
	恢复基站MO_多模	${enodebAlias}	Sctp
	恢复基站MO_多模	${enodebAlias}	X2Ap
	恢复基站MO_多模	${enodebAlias}	SRSConfig
	恢复基站MO_多模	${enodebAlias}	Clk
	恢复基站MO_多模	${enodebAlias}	PrruTxRxGroup

ITRAN定位环境版本升级NRPPA新协议
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	ITRAN定位环境基站开站NRPPA新协议	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

导入基站备份XML
	[Arguments]	${gnbAlias}
	[Documentation]	导入基站xml，文件保存在D:/0基站配置备份 目录下。
	...	如果文件不存在则忽略错误
	${version}	查询基站运行版本号_多模	${gnbAlias}	SOFTWARE
	${meid}	查询环境设备属性值	${gnbAlias}	meId
	${xmlPath}	set variable	D:/0基站配置备份/CfgBackup_${meid}_${version}.xml
	${isFile}	判断文件是否存在	${xmlPath}
	return from keyword if	('${isFile}' == 'False')	${xmlPath}
	导入基站数据_多模	${gnbAlias}	${xmlPath}

导出基站XML并备份
	[Arguments]	${gnbAlias}
	[Documentation]	导出基站xml，并保存在D:/0基站配置备份 目录下，返回文件目录。如果该版本的xml存在，则不会覆盖。
	...	该文件用来在测试套结束时恢复环境用。
	[Return]	${xmlPath}
	${version}	查询基站运行版本号_多模	${gnbAlias}	SOFTWARE
	${meid}	查询环境设备属性值	${gnbAlias}	meId
	${xmlPath}	set variable	D:/0基站配置备份/CfgBackup_${meid}_${version}.xml
	${xml}	导出基站数据_多模	${gnbAlias}
	${xmlPath}	复制文件并重命名_多模	${xml}	D:/0基站配置备份	CfgBackup_${meid}_${version}.xml

从制品库下载SDR规格包_多模
	[Arguments]	${version}	${artList}=[]	${pkgType}=""
	[Documentation]	【功能说明】
	...	
	...	从制品库下载SDR规格包到本地
	...	
	...	
	...	【入参】
	...	
	...	
	...	version - 版本号，如V5.50.10.20F30
	...	artList - 制品库列表。[unibts-gulnv-alpha-generic,unibts_gult-alpha-generic,unibts-gulnv-snapshot-generic]
	...	
	...	
	...	【返回】
	...	
	...	verPath：下载到本地的版本目录
	...	作者：10124054
	[Return]	${verPath}
	${verPath}	VersionDownloadService.download_version_from_art_multi	${version}	${artList}	${pkgType}

入库SDR规格包到UME网管
	[Arguments]	${enbAlias}	${verPathDir}	${pkgVersion}
	[Documentation]	【功能说明】
	...	
	...	从本地入库SDR规格包到UME网管
	...	
	...	
	...	【入参】
	...	
	...	
	...	enbAlias - 基站别名
	...	verPathDir - 版本包所在路径
	...	pkgVersion：版本号
	...	
	...	
	...	【返回】
	...	无
	...	作者：10124054
	入库TAR包_多模	${enbAlias}	${verPathDir}/SW_GSM_${pkgVersion}.pkg
	入库TAR包_多模	${enbAlias}	${verPathDir}/SW_LTE-FDD_${pkgVersion}.pkg
	入库TAR包_多模	${enbAlias}	${verPathDir}/SW_NB-IOT_${pkgVersion}.pkg
	入库TAR包_多模	${enbAlias}	${verPathDir}/SW_PLAT_${pkgVersion}.pkg
	入库TAR包_多模	${enbAlias}	${verPathDir}/SW_UMTS_${pkgVersion}.pkg
	Run Keyword And Ignore Error	删除目录_多模	${verPathDir}

检查入库SDR模型包
	[Arguments]	${enbAlias}	${mimVersion}
	[Documentation]	【功能说明】
	...	
	...	检查UME网管上是否存在指定版本的模型包，如果不存在，则导入
	...	
	...	
	...	【入参】
	...	
	...	
	...	enbAlias - 基站别名
	...	mimiVersion - 模型包名称，如GULN_D_V5.50.10.00
	...	
	...	
	...	【返回】
	...	
	...	作者：10124054
	${result}	查询模型包是否入库_多模	${enbAlias}	${mimVersion}	SDR
	${result2}	Run Keyword If	'${result}' != '${True}'	从预置模型包入库模型包_SDR_多模	${enbAlias}	${mimVersion}	ELSE
	...	set variable	${True}
	${modelFilePath}	Run Keyword If	'${result2}' != '${True}' and '${result}' != '${True}'	从FTP服务器上获取SDR模型包_多模	${mimVersion}	ELSE	set variable
	...	${True}
	Run Keyword If	'${modelFilePath}' != '${True}'	入库模型包_SDR_多模	${enbAlias}	${modelFilePath}

从FTP服务器上获取SDR模型包_多模
	[Arguments]	${mimVersion}
	[Documentation]	【功能说明】
	...	
	...	从部门服务器10.2.71.211上获取指定版本的模型包
	...	
	...	
	...	【入参】
	...	
	...	mimiVersion - 模型包名称，如GULN_D_V5.50.10.00
	...	
	...	
	...	【返回】
	...	modelFilePath：模型包文件路径
	...	作者：10124054
	[Return]	${modelFilePath}
	${modelFilePath}	VersionDownloadService.get_nf_model_from_ftp_multi	${mimVersion}

SDR开站升级
	[Arguments]	${enbAlias}	${pkgVersion}	${configDataVersion}
	[Documentation]	【功能说明】
	...	
	...	从备份配置目录找到合适的xml，开站到低分支版本，然后再升级到目标版本。
	...	
	...	
	...	【入参】
	...	
	...	
	...	enbAlias - 基站别名
	...	pkgVersion - 版本号，如V5.50.10.00F30
	...	configDataVersion - 配置数据版本号 如V5.50.10.00
	...	artList - 制品库列表
	...	【返回】
	...	
	...	作者：10124054
	${excludeXmlList}	create list
	${filePath}	从指定文件夹获取最接近的SDR开站文件_多模	D:\\0基站配置备份\\SDR直连UME	${configDataVersion}	${excludeXmlList}	${pkgVersion}
	创建并激活开站任务_SDR_多模	${enbAlias}	${filePath}	${pkgVersion}
	Sleep	600

备份SDR开站配置xml
	[Arguments]	${enbAlias}
	[Documentation]	【功能说明】
	...	
	...	从网管备份SDR配置文件到本地，并按指定格式重命名。
	...	D:\0基站配置备份\SDR直连UME\Cfgbakup-1802-V5.50.10.20P31-GULN_D_V5.50.10.00.xml
	...	
	...	【入参】
	...	
	...	
	...	enbAlias - 基站别名
	...	
	...	【返回】
	...	
	...	作者：10124054
	${filePath}	备份基站数据_SDR_多模	${enbAlias}
	解压缩本地zip文件	${filePath}	D:/000
	${result}	获取指定文件夹中最新的文件_多模	D:/000	zip
	解压缩本地zip文件	${result}	D:/000
	${result}	获取指定文件夹中最新的文件_多模	D:/000	xml
	${mimVersion}	网管查询mimVersion信息_SDR_多模	${enbAlias}
	${version}	查询基站运行版本号_SDR_多模	${enbAlias}
	${meId}	获取网元ID	${enbAlias}
	${filePath}	复制文件并重命名_多模	${result}	D:/0基站配置备份/SDR直连UME	Cfgbakup-${meId}-${version}-${mimVersion}.xml

CI环境SDR版本升级
	[Arguments]	${enodebAlias}	${pkgVersion}	${configDataVersion}	${artList}
	[Documentation]	【功能说明】
	...	
	...	CI环境SDR直连UME环境升级
	...	
	...	【入参】
	...	
	...	
	...	enbAlias - 基站别名
	...	pkgVersion - 版本号
	...	configDataVersion -配置数据版本号
	...	artList - 制品库列表
	...	
	...	【返回】
	...	
	...	作者：10124054
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级PKG包_SDR_多模	${enodebAlias}	${pkgVersion}
	Run Keyword If	'upgrade sequence' in '${reason}'	SDR开站升级	${enodebAlias}	${pkgVersion}	${configDataVersion}
	Sleep	1100
	${version}	查询基站运行版本号_SDR_多模	${enodebAlias}
	${pkgVersion}	Run Keyword If	'V' in '${pkgVersion}'	evaluate	'${pkgVersion}'[1:]	ELSE	set variable
	...	${pkgVersion}
	Should Be True	'${pkgVersion}'=='${version}'	升级失败，期望版本${pkgVersion}，实际版本${version}
	Run Keyword And Ignore Error	备份SDR开站配置xml	${enodebAlias}

ITRAN汇聚调度定位环境版本升级
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	ITRAN汇聚调度定位环境版本开站	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

ITRAN汇聚调度定位环境版本开站
	[Arguments]	${enodebAlias}	${tarName}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	PositionAssistance
	保存基站MO_多模	${enodebAlias}	DRXCfg
	保存基站MO_多模	${enodebAlias}	NRMecsAp
	保存基站MO_多模	${enodebAlias}	RANIntelligentFunction
	保存基站MO_多模	${enodebAlias}	GnbObject
	保存基站MO_多模	${enodebAlias}	EPE2C
	保存基站MO_多模	${enodebAlias}	NetworkInterfaceFunction
	保存基站MO_多模	${enodebAlias}	VIPFunction
	保存基站MO_多模	${enodebAlias}	KPMFunction
	保存基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	保存基站MO_多模	${enodebAlias}	GlobalConfigLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	保存基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE
	保存基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreq
	保存基站MO_多模	${enodebAlias}	FrequencyBandList
	保存基站MO_多模	${enodebAlias}	ExternalNRCellCU
	保存基站MO_多模	${enodebAlias}	SsbMeasInfo
	保存基站MO_多模	${enodebAlias}	NRCellRelation
	保存基站MO_多模	${enodebAlias}	NRFreqRelation
	保存基站MO_多模	${enodebAlias}	InterFHoA1A2
	保存基站MO_多模	${enodebAlias}	CoverMobilityCtrl
	保存基站MO_多模	${enodebAlias}	GNBCUCPFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdListCU
	保存基站MO_多模	${enodebAlias}	EPNgC
	保存基站MO_多模	${enodebAlias}	NgAp
	保存基站MO_多模	${enodebAlias}	GNBCUUPFunction
	保存基站MO_多模	${enodebAlias}	GNBDUFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdList
	保存基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	保存基站MO_多模	${enodebAlias}	SliceProfile
	保存基站MO_多模	${enodebAlias}	NSSAI
	保存基站MO_多模	${enodebAlias}	NRRadioInfrastructure
	保存基站MO_多模	${enodebAlias}	GlobalSwitchInformation
	保存基站MO_多模	${enodebAlias}	Sctp
	保存基站MO_多模	${enodebAlias}	X2Ap
	保存基站MO_多模	${enodebAlias}	PrruTxRxGroup
	保存基站MO_多模	${enodebAlias}	SRSConfig
	保存基站MO_多模	${enodebAlias}	GlobalSwitchInformation
	保存基站MO_多模	${enodebAlias}	SchAggESPolicy
	保存基站MO_多模	${enodebAlias}	NRCarrierObj
	保存基站MO_多模	${enodebAlias}	NRPhysicalCellObj
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}
	修改开站模板文件_多模	${filePath}	${tarName}
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	创建IPv4操作维护通道_多模	${enodebAlias}
	恢复基站MO_多模	${enodebAlias}	PositionAssistance
	恢复基站MO_多模	${enodebAlias}	DRXCfg
	恢复基站MO_多模	${enodebAlias}	NRMecsAp
	恢复基站MO_多模	${enodebAlias}	RANIntelligentFunction
	恢复基站MO_多模	${enodebAlias}	GnbObject
	恢复基站MO_多模	${enodebAlias}	EPE2C
	恢复基站MO_多模	${enodebAlias}	NetworkInterfaceFunction
	恢复基站MO_多模	${enodebAlias}	VIPFunction
	恢复基站MO_多模	${enodebAlias}	KPMFunction
	恢复基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	恢复基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	恢复基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	恢复基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	恢复基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	恢复基站MO_多模	${enodebAlias}	GlobalConfigLTE
	恢复基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	恢复基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	恢复基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	恢复基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	恢复基站MO_多模	${enodebAlias}	ExternalEUtranCellFDDLTE
	恢复基站MO_多模	${enodebAlias}	EUtranRelationFDDLTE
	恢复基站MO_多模	${enodebAlias}	NRFreq
	恢复基站MO_多模	${enodebAlias}	FrequencyBandList
	恢复基站MO_多模	${enodebAlias}	ExternalNRCellCU
	恢复基站MO_多模	${enodebAlias}	SsbMeasInfo
	恢复基站MO_多模	${enodebAlias}	NRCellRelation
	恢复基站MO_多模	${enodebAlias}	NRFreqRelation
	恢复基站MO_多模	${enodebAlias}	InterFHoA1A2
	恢复基站MO_多模	${enodebAlias}	CoverMobilityCtrl
	恢复基站MO_多模	${enodebAlias}	GNBCUCPFunction
	恢复基站MO_多模	${enodebAlias}	PlmnIdListCU
	恢复基站MO_多模	${enodebAlias}	EPNgC
	${attrDict}	create dictionary	mocName	NgAp	moId	1
	run keyword and ignore error	__删除节点	${enodebAlias}	${attrDict}
	${attrDict}	create dictionary	mocName	NgAp	moId	2
	run keyword and ignore error	__删除节点	${enodebAlias}	${attrDict}
	run keyword and ignore error	同步规划区数据_多模	${enodebAlias}
	${keyMoPathDict}	create dictionary	NgAp	1
	${attrDict}	create dictionary	sctpInfo	1	moId	1
	run keyword and ignore error	__创建节点	${enodebAlias}	${attrDict}	planarea	NgAp	${keyMoPathDict}
	run keyword and ignore error	同步规划区数据_多模	${enodebAlias}
	恢复基站MO_多模	${enodebAlias}	GNBCUUPFunction
	恢复基站MO_多模	${enodebAlias}	GNBDUFunction
	恢复基站MO_多模	${enodebAlias}	PlmnIdList
	恢复基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	恢复基站MO_多模	${enodebAlias}	SliceProfile
	恢复基站MO_多模	${enodebAlias}	NSSAI
	恢复基站MO_多模	${enodebAlias}	NRRadioInfrastructure
	恢复基站MO_多模	${enodebAlias}	GlobalSwitchInformation
	恢复基站MO_多模	${enodebAlias}	Sctp
	恢复基站MO_多模	${enodebAlias}	X2Ap
	恢复基站MO_多模	${enodebAlias}	SRSConfig
	恢复基站MO_多模	${enodebAlias}	PrruTxRxGroup
	run keyword and ignore error	删除基站MO_多模	${enodebAlias}	NRCarrierObj
	run keyword and ignore error	删除基站MO_多模	${enodebAlias}	NRPhysicalCellObj
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NRCarrierObj
	run keyword and ignore error	恢复基站MO_多模	${enodebAlias}	NRPhysicalCellObj
	run keyword and ignore error	修改节能配置_5125
	恢复基站MO_多模	${enodebAlias}	SchAggESPolicy
	${attr}	create dictionary	phyResAdaptConfigSwitch=1
	${version}	查询基站运行版本号_多模	${enodebAlias}
	run keyword if	'V5.75.10' not in '${version}'	批量修改基站配置_多模	${enodebAlias}	NRPhysicalCellDU	${attr}
	run keyword if	'V5.75.10' not in '${version}'	同步规划区数据_多模	${enodebAlias}

入库SDR补丁包到UME网管
	[Arguments]	${enbAlias}	${verPath}
	[Documentation]	【功能说明】
	...	
	...	从本地入库SDR规格包到UME网管
	...	
	...	
	...	【入参】
	...	
	...	
	...	enbAlias - 基站别名
	...	verPathDir - 版本包所在路径
	...	pkgVersion：版本号
	...	
	...	
	...	【返回】
	...	无
	...	作者：10124054
	入库TAR包_多模	${enbAlias}	${verPath}

SDR直连UME环境父版本升级
	[Arguments]	${pkgVersion}	${configDataVersion}
	${artList}	create list	lte-release-generic	unibts-gulnv-alpha-generic	unibts_gult-alpha-generic	unibts-gulnv-snapshot-generic	unibts_gult-snapshot-generic
	...	unibts_gult-release-generic	unibts-gulnv-release-generic
	${result}	${verPathDir}	run keyword and ignore error	从制品库下载SDR规格包_多模	${pkgVersion}	${artList}
	run keyword and ignore error	入库SDR规格包到UME网管	${ENODEB}	${verPathDir}	${pkgVersion}
	run keyword and ignore error	删除基站所有升级任务_多模	${ENODEB}
	${currentMimVersion}	网管查询mimVersion信息_SDR_多模	${ENODEB}
	${mimVersion}	evaluate	'${currentMimVersion}'.split('V')[0]+'${configDataVersion}'
	检查入库SDR模型包	${ENODEB}	${mimVersion}
	${version}	重复执行_多模	30	查询基站运行版本号_SDR_多模	${ENODEB}
	Run keyword If	'V${version}'=='${pkgVersion}'	sleep	450	ELSE	CI环境SDR版本升级	${ENODEB}
	...	${pkgVersion}	${configDataVersion}	${artList}

SDR直连UME环境补丁版本升级
	[Arguments]	${pkgVersionColdPatch}
	${artList}	create list	lte-release-generic	unibts-gulnv-alpha-generic	unibts_gult-alpha-generic	unibts-gulnv-snapshot-generic	unibts_gult-snapshot-generic
	...	unibts_gult-release-generic	unibts-gulnv-release-generic
	${pkgVersionColdPatchList}	evaluate	'${pkgVersionColdPatch}'.split(',')
	FOR	${pkg}	IN	@{pkgVersionColdPatchList}
	${verPath}	从制品库下载SDR规格包_多模	${pkg}	${artList}	CP
	入库SDR补丁包到UME网管	${ENODEB}	${verPath}
	run keyword and ignore error	删除基站所有升级任务_多模	${ENODEB}
	${pkgName}	evaluate	'${verPath}'.split('/')[-1]
	下载补丁包_SDR_多模	${ENODEB}	${pkgName}

FMM版本升级
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	FMM基站开站	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

FMM基站开站
	[Arguments]	${enodebAlias}	${tarName}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	保存基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	保存基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	保存基站MO_多模	${enodebAlias}	GlobalConfigLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	保存基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	保存基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	保存基站MO_多模	${enodebAlias}	GNBCUUPFunction
	保存基站MO_多模	${enodebAlias}	GNBCUCPFunction
	保存基站MO_多模	${enodebAlias}	GNBDUFunction
	保存基站MO_多模	${enodebAlias}	PlmnIdListUP
	保存基站MO_多模	${enodebAlias}	PlmnIdListCU
	保存基站MO_多模	${enodebAlias}	PlmnIdList
	保存基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	保存基站MO_多模	${enodebAlias}	SliceProfile
	保存基站MO_多模	${enodebAlias}	NSSAI
	保存基站MO_多模	${enodebAlias}	NgAp
	${sceneList}	create list	1588	IPSec	MAU	DryContactCable	Cabinet
	...	BWP	TMA	BFD	FMM
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}	V9200	${sceneList}
	修改开站模板文件_多模	${filePath}	${tarName}
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	创建IPv4操作维护通道_多模	${enodebAlias}
	创建基站MO_多模	${enodebAlias}	ExternalNrCellLTE
	创建基站MO_多模	${enodebAlias}	NRFreqParaFDDLTE
	创建基站MO_多模	${enodebAlias}	NRFreqParaTDDLTE
	创建基站MO_多模	${enodebAlias}	NrNiborRelaFDDLTE
	创建基站MO_多模	${enodebAlias}	NrNiborRelaTDDLTE
	修改基站MO_多模	${enodebAlias}	GlobalConfigLTE
	修改基站MO_多模	${enodebAlias}	EndcPlmnFDDLTE
	修改基站MO_多模	${enodebAlias}	ENDCPolicyFDDLTE
	修改基站MO_多模	${enodebAlias}	EndcPlmnTDDLTE
	修改基站MO_多模	${enodebAlias}	ENDCPolicyTDDLTE
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBCUUPFunction
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBCUCPFunction
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	GNBDUFunction
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdListUP
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdListCU
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	PlmnIdList
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NetworkSliceSubnet
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	SliceProfile
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NSSAI
	run keyword and ignore error	创建基站MO_多模	${enodebAlias}	NgAp

ITRAN JTCJT环境版本升级
	[Arguments]	${enodebAlias}	${tarName}	${tarVersion}	${pkgType}=SOFTWARE
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级TAR包_多模	${enodebAlias}	${tarName}
	run keyword if	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	ITRAN JTCJT环境基站开站	${enodebAlias}	${tarName}
	run keyword unless	'Failed to find ' in '${reason}' and 'upgrade sequence' in '${reason}'	sleep	1200
	${pkgVersion}	查询基站运行版本号_多模	${enodebAlias}	${pkgType}
	Should be true	'${pkgVersion}'=='${tarVersion}'	升级失败，期望版本${tarVersion}，实际版本${pkgVersion}
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}

ITRAN JTCJT环境基站开站
	[Arguments]	${enodebAlias}	${tarName}
	删除IPv4操作维护通道_多模	${enodebAlias}
	保存基站MO_多模	${enodebAlias}	NRCellRelation
	Comment	保存基站MO_多模	${enodebAlias}	CompCfg
	Comment	保存基站MO_多模	${enodebAlias}	NRDlCompMeasCfg
	${filePath}	导出开站模板文件_多模	${enodebAlias}	${tarName}
	修改开站模板文件_多模	${filePath}	${tarName}
	写入XLSX单元格	${filePath}	site	basic_loopback_template	7	4
	Comment	写入XLSX单元格	${filePath}	Sctp	5101	7	9
	Comment	写入XLSX单元格	${filePath}	Sctp	460-01	7	10
	${dataPlanJobName}	导入开站模板文件_多模	${enodebAlias}	${filePath}
	${jobId}	PNP带数据开站_多模	${enodebAlias}	${tarName}	${dataPlanJobName}
	sleep	1200
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}	NR
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}	NR
	${state}	${file}	Run Keyword And Ignore Error	匹配License_多模	${enodebAlias}
	Run Keyword And Ignore Error	加载License_多模	${enodebAlias}	${file}
	Comment	修改基站MO_多模	${enodebAlias}	PositionAssistance
	Comment	修改基站MO_多模	${enodebAlias}	DRXCfg
	Comment	Comment	删除基站MO_多模	${enodebAlias}	NgAp	GNBCUCPFunction=460-01_5101,EPNgC=1,NgAp=1
	Comment	创建基站MO_多模	${enodebAlias}	NRMecsAp
	创建基站MO_多模	${enodebAlias}	NRCellRelation
	Comment	修改基站MO_多模	${enodebAlias}	CompCfg
	Comment	修改基站MO_多模	${enodebAlias}	NRDlCompMeasCfg
	创建IPv4操作维护通道_多模	${enodebAlias}

修改NR载波son载波对象
	[Arguments]	${gnbAlias}	${moId}	${nrCarrierId}
	${filterDict}	create dictionary	mocName=NRCarrierObj	moId=${moId}
	${attrDict}	create dictionary	nrCarrierId=${nrCarrierId}
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}

修改节能配置_5125
	修改NR载波son载波对象	${GNODEB}	1	51
	修改NR载波son载波对象	${GNODEB}	2	52
	修改NR载波son载波对象	${GNODEB}	3	53
	修改NR载波son载波对象	${GNODEB}	4	54
	修改NR载波son载波对象	${GNODEB}	5	55
	修改NR载波son载波对象	${GNODEB}	6	56
	修改NR载波son载波对象	${GNODEB}	7	61
	修改NR载波son载波对象	${GNODEB}	8	62
	修改son物理小区对象	${GNODEB}	1	1
	修改son物理小区对象	${GNODEB}	2	2
	修改son物理小区对象	${GNODEB}	3	3
	修改son物理小区对象	${GNODEB}	4	4
	修改son物理小区对象	${GNODEB}	5	5
	修改son物理小区对象	${GNODEB}	6	6
	修改son物理小区对象	${GNODEB}	7	7
	修改son物理小区对象	${GNODEB}	8	8
	修改NR载波节能策略	${GNODEB}	1	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=1
	修改NR载波节能策略	${GNODEB}	2	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=2
	修改NR载波节能策略	${GNODEB}	3	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=3
	修改NR载波节能策略	${GNODEB}	4	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=4
	修改NR载波节能策略	${GNODEB}	5	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=5
	修改NR载波节能策略	${GNODEB}	6	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=6
	修改NR载波节能策略	${GNODEB}	7	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=7
	修改NR载波节能策略	${GNODEB}	8	NRSelfOrganizingNetwork=1,NRSONObj=1,NRCarrierObj=8
	同步规划区数据_多模	${GNODEB}

修改son物理小区对象
	[Arguments]	${gnbAlias}	${moId}	${nrPhysicalCellDUId}
	${filterDict}	create dictionary	mocName=NRPhysicalCellObj	moId=${moId}
	${attrDict}	create dictionary	nrPhysicalCellDUId=${nrPhysicalCellDUId}
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}

修改NR载波节能策略
	[Arguments]	${gnbAlias}	${moId}	${refNRCarrierObj}
	${filterDict}	create dictionary	mocName=ESPolicy	moId=${moId}
	${attrDict}	create dictionary	refNRCarrierObj=${refNRCarrierObj}
	__修改节点属性	${gnbAlias}	${filterDict}	${attrDict}

从失败信息中匹配模型号并入库模型包
	[Arguments]	${enbAlias}	${failInfo}
	${mimVersion}	evaluate	'${failInfo}'.split()[-1]
	run keyword and ignore error	检查入库SDR模型包	${enbAlias}	${mimVersion}

重新升级PKG包_SDR_多模
	[Arguments]	${enodebAlias}	${pkgVersion}
	[Return]	${reason}
	删除基站所有升级任务_多模	${enodebAlias}
	${state}	${reason}	Run Keyword And Ignore Error	升级PKG包_SDR_多模	${enodebAlias}	${pkgVersion}


