/* 深色主题样式文件 */

/* 全局样式 */
QWidget {
    color: #E0E0E0;
    background-color: #2B2B2B;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
}

/* 主窗口 */
QMainWindow {
    background-color: #2B2B2B;
    color: #E0E0E0;
}

/* 菜单栏 */
QMenuBar {
    background-color: #3C3C3C;
    color: #E0E0E0;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #4A90E2;
    color: #FFFFFF;
}

QMenuBar::item:pressed {
    background-color: #357ABD;
    color: #FFFFFF;
}

/* 菜单 */
QMenu {
    background-color: #3C3C3C;
    color: #E0E0E0;
    border: 1px solid #555555;
}

QMenu::item {
    padding: 6px 20px;
    background: transparent;
}

QMenu::item:selected {
    background-color: #4A90E2;
    color: #FFFFFF;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 2px 0;
}

/* 工具栏 */
QToolBar {
    background-color: #3C3C3C;
    border: none;
    spacing: 2px;
}

QToolBar::handle {
    background-color: #555555;
    width: 2px;
    margin: 2px;
}

/* 按钮 */
QPushButton {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 60px;
}

QPushButton:hover {
    background-color: #4A90E2;
    border-color: #357ABD;
    color: #FFFFFF;
}

QPushButton:pressed {
    background-color: #357ABD;
    border-color: #2E6DA4;
}

QPushButton:disabled {
    background-color: #333333;
    color: #666666;
    border-color: #444444;
}

/* 特殊按钮样式 */
QPushButton[name='primary'] {
    background-color: #4A90E2;
    color: #FFFFFF;
    border-color: #357ABD;
}

QPushButton[name='primary']:hover {
    background-color: #357ABD;
    border-color: #2E6DA4;
}

QPushButton[name='succ'] {
    background-color: #5CB85C;
    color: #FFFFFF;
    border-color: #4CAE4C;
}

QPushButton[name='succ']:hover {
    background-color: #4CAE4C;
    border-color: #449D44;
}

QPushButton[name='warn'] {
    background-color: #F0AD4E;
    color: #FFFFFF;
    border-color: #EEA236;
}

QPushButton[name='warn']:hover {
    background-color: #EEA236;
    border-color: #E6982A;
}

QPushButton[name='danger'] {
    background-color: #D9534F;
    color: #FFFFFF;
    border-color: #D43F3A;
}

QPushButton[name='danger']:hover {
    background-color: #D43F3A;
    border-color: #C9302C;
}

/* 文本输入框 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 4px;
    selection-background-color: #4A90E2;
    selection-color: #FFFFFF;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #4A90E2;
}

/* 代码编辑器 QsciScintilla */
QsciScintilla {
    background-color: #2B2B2B;
    color: #E0E0E0;
    border: 1px solid #555555;
    selection-background-color: #4A90E2;
    selection-color: #FFFFFF;
}

/* 代码编辑器滚动条 */
QsciScintilla QScrollBar:vertical {
    background-color: #3C3C3C;
    border: none;
    width: 12px;
}

QsciScintilla QScrollBar::handle:vertical {
    background-color: #666666;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QsciScintilla QScrollBar::handle:vertical:hover {
    background-color: #777777;
}

QsciScintilla QScrollBar::add-line:vertical,
QsciScintilla QScrollBar::sub-line:vertical {
    height: 0px;
}

QsciScintilla QScrollBar:horizontal {
    background-color: #3C3C3C;
    border: none;
    height: 12px;
}

QsciScintilla QScrollBar::handle:horizontal {
    background-color: #666666;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QsciScintilla QScrollBar::handle:horizontal:hover {
    background-color: #777777;
}

QsciScintilla QScrollBar::add-line:horizontal,
QsciScintilla QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 组合框 */
QComboBox {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 4px 8px;
    min-width: 60px;
}

QComboBox:hover {
    border-color: #4A90E2;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #555555;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
    background: #E0E0E0;
}

QComboBox QAbstractItemView {
    background-color: #3C3C3C;
    color: #E0E0E0;
    border: 1px solid #555555;
    selection-background-color: #4A90E2;
    selection-color: #FFFFFF;
}

/* 列表和树形视图 */
QListView, QTreeView {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #555555;
    alternate-background-color: #454545;
    selection-background-color: #4A90E2;
    selection-color: #FFFFFF;
}

QListView::item, QTreeView::item {
    padding: 4px;
    border: none;
}

QListView::item:hover, QTreeView::item:hover {
    background-color: #505050;
}

QListView::item:selected, QTreeView::item:selected {
    background-color: #4A90E2;
    color: #FFFFFF;
}

/* 表格视图 */
QTableView {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #555555;
    gridline-color: #555555;
    selection-background-color: #4A90E2;
    selection-color: #FFFFFF;
}

QTableView::item {
    padding: 4px;
    border: none;
}

QTableView::item:hover {
    background-color: #505050;
}

QTableView::item:selected {
    background-color: #4A90E2;
    color: #FFFFFF;
}

/* 表头 */
QHeaderView::section {
    background-color: #3C3C3C;
    color: #E0E0E0;
    border: 1px solid #555555;
    padding: 6px;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #505050;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #3C3C3C;
    width: 12px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #666666;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #777777;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #3C3C3C;
    height: 12px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #666666;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #777777;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 选项卡 */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #404040;
}

QTabBar::tab {
    background-color: #3C3C3C;
    color: #E0E0E0;
    border: 1px solid #555555;
    border-bottom: none;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #404040;
    border-bottom: 1px solid #404040;
}

QTabBar::tab:hover:!selected {
    background-color: #505050;
}

/* 状态栏 */
QStatusBar {
    background-color: #3C3C3C;
    color: #E0E0E0;
    border-top: 1px solid #555555;
}

/* 停靠窗口 */
QDockWidget {
    color: #E0E0E0;
    titlebar-close-icon: none;
    titlebar-normal-icon: none;
}

QDockWidget::title {
    background-color: #3C3C3C;
    color: #E0E0E0;
    padding: 6px;
    border-bottom: 1px solid #555555;
}

/* 分割器 */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 复选框和单选按钮 */
QCheckBox, QRadioButton {
    color: #E0E0E0;
    spacing: 8px;
}

QCheckBox::indicator, QRadioButton::indicator {
    width: 16px;
    height: 16px;
    background-color: #404040;
    border: 1px solid #555555;
}

QCheckBox::indicator:hover, QRadioButton::indicator:hover {
    border-color: #4A90E2;
}

QCheckBox::indicator:checked {
    background-color: #4A90E2;
    border-color: #357ABD;
}

QRadioButton::indicator {
    border-radius: 8px;
}

QRadioButton::indicator:checked {
    background-color: #4A90E2;
    border-color: #357ABD;
}

/* 进度条 */
QProgressBar {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #555555;
    border-radius: 3px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #4A90E2;
    border-radius: 2px;
}

/* 分组框 */
QGroupBox {
    color: #E0E0E0;
    border: 1px solid #555555;
    border-radius: 3px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}
