*** Settings ***
Documentation    测试TextEditor关键字跳转功能
Library          SeleniumLibrary
Library          Collections
Library          String

*** Variables ***
${URL}           https://www.example.com
${USERNAME}      testuser
${PASSWORD}      testpass

*** Test Cases ***
测试关键字跳转功能
    [Documentation]    测试Ctrl+点击关键字跳转功能
    [Tags]    关键字跳转    TextEditor
    
    # 内置关键字（应该能跳转）
    Log    这是内置Log关键字
    Set Variable    test_value
    Should Be Equal    value1    value1
    Should Contain    Hello World    World
    Run Keyword    Log    运行关键字测试
    
    # 库关键字（应该能跳转）
    Open Browser    about:blank    chrome
    Get Title
    Maximize Browser Window
    Create List    item1    item2    item3
    Get Length    ${list}
    Convert To Uppercase    hello world
    Close Browser
    
    # 用户关键字（应该能跳转）
    自定义关键字示例    参数1    参数2
    Another Custom Keyword
    复杂参数关键字    ${USERNAME}    ${PASSWORD}

测试复杂关键字跳转
    [Documentation]    测试复杂场景下的关键字跳转
    [Tags]    复杂跳转
    
    # 嵌套关键字调用
    Run Keyword If    True    Log    条件为真
    Run Keyword And Return Status    Should Be Equal    1    1
    Run Keyword And Continue On Failure    Should Be Equal    wrong    right
    
    # 循环中的关键字
    FOR    ${i}    IN RANGE    1    4
        Log    循环次数: ${i}
        自定义处理关键字    ${i}
        Should Be True    ${i} > 0
    END
    
    # 条件中的关键字
    IF    True
        Log    条件为真
        自定义条件处理
    ELSE
        Log    条件为假
        自定义错误处理
    END

测试关键字识别
    [Documentation]    测试关键字识别的准确性
    [Tags]    关键字识别
    
    # 这些应该被识别为关键字并可跳转
    Log    正常的Log关键字
    Set Variable    正常的Set Variable关键字
    Should Be Equal    正常的Should Be Equal关键字
    
    # 用户关键字
    自定义关键字示例    正常的用户关键字
    Another Custom Keyword
    
    # 库关键字
    Create List    正常的库关键字
    Get Length    ${list}
    
    # 这些不应该被识别为关键字
    ${variable_name}=    Set Variable    这是变量赋值
    "Log"    这是字符串中的Log，不应该跳转
    # Log    这是注释中的Log，不应该跳转

*** Keywords ***
自定义关键字示例
    [Arguments]    ${param1}    ${param2}
    [Documentation]    用户自定义关键字，测试跳转到此处
    
    Log    执行自定义关键字: ${param1}, ${param2}
    ${result}=    Set Variable    ${param1}_${param2}
    Should Be True    len('${result}') > 0
    RETURN    ${result}

Another Custom Keyword
    [Documentation]    另一个用户关键字，测试英文关键字跳转
    
    Log    执行另一个用户关键字
    ${time}=    Get Time
    Should Contain    ${time}    :
    RETURN    ${time}

复杂参数关键字
    [Arguments]    ${username}    ${password}    ${optional}=default
    [Documentation]    带复杂参数的用户关键字
    
    Log    用户名: ${username}
    Log    密码: ****
    Log    可选参数: ${optional}
    
    # 在用户关键字中调用其他关键字
    Run Keyword If    '${optional}' != 'default'    Log    使用了自定义可选参数
    
    ${result}=    Create Dictionary    user=${username}    status=processed
    RETURN    ${result}

自定义处理关键字
    [Arguments]    ${value}
    [Documentation]    处理单个值的关键字
    
    Log    处理值: ${value}
    ${processed}=    Set Variable    processed_${value}
    Should Start With    ${processed}    processed_
    RETURN    ${processed}

自定义条件处理
    [Documentation]    条件为真时的处理
    
    Log    执行条件为真的处理逻辑
    ${result}=    Set Variable    condition_true
    Should Be Equal    ${result}    condition_true

自定义错误处理
    [Documentation]    条件为假时的处理
    
    Log    执行条件为假的处理逻辑
    ${result}=    Set Variable    condition_false
    Should Be Equal    ${result}    condition_false

带循环的关键字
    [Documentation]    包含循环的用户关键字
    
    ${results}=    Create List
    
    FOR    ${i}    IN RANGE    1    6
        ${item}=    Set Variable    item_${i}
        Append To List    ${results}    ${item}
        Log    添加项目: ${item}
        
        # 在循环中调用其他关键字
        自定义处理关键字    ${item}
    END
    
    ${count}=    Get Length    ${results}
    Should Be Equal As Numbers    ${count}    5
    RETURN    ${results}

嵌套关键字调用
    [Documentation]    测试嵌套的关键字调用
    
    # 多层嵌套调用
    Run Keyword    Run Keyword    Log    深度嵌套调用
    
    # 条件嵌套
    Run Keyword If    True    自定义关键字示例    nested    call
    
    # 循环嵌套
    FOR    ${item}    IN    a    b    c
        Run Keyword    Log    嵌套循环项目: ${item}
    END
