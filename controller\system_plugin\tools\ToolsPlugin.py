# coding=utf-8
'''
Created on 2019年12月17日

@author: 10240349
'''

from _functools import partial

from PyQt5.Qt import QIcon
from PyQt5.QtWidgets import QAction

from controller.system_plugin.tools.item.Preferences.Preferences import Preferences
from controller.system_plugin.tools.search_dialog.Keywords import Keywords
from controller.system_plugin.tools.search_dialog.Testcases import Testcases
from resources.Loader import Loader
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader


class ToolsPlugin(object):

    def __init__(self, parent):
        self._parent_window = parent

    def load(self):
        file_menu = self._parent_window.addMenu('&' + LanguageLoader().get('TOOLS'))
        self._add_item_search_keywords(file_menu)
        self._add_item_search_testcases(file_menu)
        self._add_item_preferences(file_menu)

    def _add_item_search_keywords(self, file_menu):
        search_keywords_action = QAction(QIcon(''), LanguageLoader().get('SEARCH_KEYWORDS'), self._parent_window)
        search_keywords_action.setShortcut(self._get_shortcut_key('SEARCH_KEYWORDS'))
        search_keywords_action.triggered.connect(partial(self._open_search_keywords_dialog, self))
        file_menu.addAction(search_keywords_action)

    def _add_item_search_testcases(self, file_menu):
        search_testcases_action = QAction(QIcon(''), LanguageLoader().get('SEARCH_TESTCASES'), self._parent_window)
        search_testcases_action.setShortcut(self._get_shortcut_key('SEARCH_TESTCASES'))
        search_testcases_action.triggered.connect(partial(self._open_search_testcases_dialog, self))
        file_menu.addAction(search_testcases_action)

    def _add_item_preferences(self, file_menu):
        preferences_action = QAction(QIcon(''), LanguageLoader().get('PREFERENCES'), self._parent_window)
        preferences_action.triggered.connect(self._open_preferences_dialog)
        file_menu.addAction(preferences_action)

    def _get_shortcut_key(self, key):
        return SystemSettings().get_value(key)

    @staticmethod
    def _open_search_keywords_dialog(this):
        this.dialog = Keywords(LanguageLoader().get('SEARCH_KEYWORDS'), Loader().get_path('SEARCH'))
        this.dialog.show()

    @staticmethod
    def _open_search_testcases_dialog(this):
        this.dialog = Testcases(LanguageLoader().get('SEARCH_TESTCASES'), Loader().get_path('SEARCH'))
        this.dialog.show()

    def _open_preferences_dialog(self):
        self.preferences_dialog = Preferences(LanguageLoader().get('PREFERENCES'))
        self.preferences_dialog.show()
