#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 RobotFramework 语法高亮器集成
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtGui import QColor
from PyQt5.Qsci import QsciScintilla

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from iplatform.highlight.RobotHighlighter import RobotHighlighter


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('RobotFramework 语法高亮测试')
        self.setGeometry(100, 100, 1000, 700)

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 创建编辑器
        self.editor = QsciScintilla()
        self.editor.setUtf8(True)
        self.editor.setTabWidth(4)

        # 设置 Robot 语法高亮器
        self.highlighter = RobotHighlighter(self.editor)
        self.editor.setLexer(self.highlighter)

        # 设置行号
        self.editor.setMarginType(0, QsciScintilla.NumberMargin)
        self.editor.setMarginWidth(0, "0000")
        self.editor.setMarginLineNumbers(0, True)
        self.editor.setMarginsBackgroundColor(QColor("#f0f0f0"))

        # 加载测试内容
        self.load_test_content()

        layout.addWidget(self.editor)

    def load_test_content(self):
        """加载测试内容"""
        test_content = '''*** Settings ***
Documentation    这是一个测试RobotFramework语法高亮的示例文件
Library          SeleniumLibrary
Library          Collections
Resource         common.robot

*** Variables ***
${URL}           https://www.example.com
${BROWSER}       chrome
@{USERS}         admin    user1    user2
&{CONFIG}        host=localhost    port=8080

*** Test Cases ***
登录测试
    [Documentation]    测试用户登录功能
    [Tags]    smoke    login    critical

    打开登录页面
    输入用户名    ${USERNAME}
    输入密码      ${PASSWORD}
    点击登录按钮
    验证登录成功

搜索功能测试
    [Documentation]    测试搜索功能
    [Tags]    search    功能测试

    打开搜索页面
    输入搜索关键字    robot framework
    点击搜索按钮
    验证搜索结果    robot framework

循环测试示例
    FOR    ${user}    IN    @{USERS}
        Log    当前用户: ${user}
        验证用户权限    ${user}
    END

    FOR    ${i}    IN RANGE    1    6
        Log    循环次数: ${i}
        IF    ${i} == 3
            Log    到达中间值
        ELSE
            Log    其他值
        END
    END

*** Keywords ***
打开登录页面
    [Documentation]    打开登录页面的关键字
    [Arguments]    ${url}=${URL}

    Open Browser    ${url}    ${BROWSER}
    Maximize Browser Window
    Wait Until Page Contains    登录

输入用户名
    [Arguments]    ${username}
    Input Text    id=username    ${username}
    Sleep    0.5s

验证登录成功
    Wait Until Page Contains    欢迎
    Page Should Contain    用户中心
    Should Be True    True

# 这是单行注释
# 支持中文注释：这是中文注释测试
'''

        self.editor.setText(test_content)


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 创建测试窗口
    window = TestWindow()
    window.show()

    print("RobotFramework 语法高亮测试窗口已打开")
    print("请检查以下语法高亮效果：")
    print("1. 章节标题（*** Settings *** 等）应该是蓝色粗体")
    print("2. 测试用例名（登录测试等）应该是深红色粗体")
    print("3. 关键字（打开登录页面等）应该是紫色粗体")
    print("4. 内置关键字（Log, Sleep等）应该是中蓝色")
    print("5. 变量（${URL}等）应该是深橙色")
    print("6. 设置项（[Documentation]等）应该是靛蓝色")
    print("7. 注释（# 开头）应该是绿色")
    print("8. 字符串应该是深红色")
    print("9. 中文内容应该正确显示和高亮")

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
