# coding=utf-8
'''
Created on 2019年11月25日

@author: 10240349
'''
# coding=utf-8
'''
Created on 2019年10月23日

@author: 10240349
'''

from _functools import partial
import codecs
import os

from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QAction

from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository


SYSTEM_ROOT_DIR = "{}/..".format(os.path.abspath(os.path.dirname(__file__)))


class StylePlugin(object):

    def __init__(self, parent):
        self._parent_window = parent

    def load(self):
        file_menu = self._parent_window.addMenu('&' + LanguageLoader().get('STYLE'))
        self._add_item_style(file_menu, '深色', 'dark_orange')
        self._add_item_style(file_menu, '素雅', 'default')

    def _add_item_style(self, file_menu, alias, fileName = 'default'):
        name = SYSTEM_ROOT_DIR + '/resources/qss/' + fileName + '.qss'
        default_style_action = QAction(QIcon(''), '&' + fileName, self._parent_window)
        default_style_action.triggered.connect(partial(self._set_style, name))
        file_menu.addAction(default_style_action)

    @staticmethod
    def _set_style(self, style_file):
        main_frame = PluginRepository().find("MAIN_FRAME")
        with codecs.open(style_file, "r") as f:
            style = f.read()
        main_frame.setStyleSheet(style)
