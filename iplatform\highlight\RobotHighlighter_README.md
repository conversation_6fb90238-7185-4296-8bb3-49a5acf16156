# RobotHighlighter 使用说明

## 概述

RobotHighlighter 是一个基于 PyQt5 和 QsciScintilla 的 RobotFramework 语法高亮器，专门为 RobotFramework 测试脚本提供语法高亮功能。

## 主要特性

### 1. 支持的语法元素

- **部分标题** (Section Headers)：`*** Settings ***`, `*** Variables ***`, `*** Test Cases ***`, `*** Keywords ***`
- **变量** (Variables)：`${变量}`, `@{列表}`, `&{字典}`, `%{环境变量}`
- **测试用例名称** (Test Case Names)：在 Test Cases 部分的用例名称
- **关键字名称** (Keyword Names)：在 Keywords 部分的关键字名称
- **设置项** (Settings)：`[Tags]`, `[Documentation]`, `[Setup]`, `[Teardown]`, `[Arguments]`, `[Return]` 等
- **注释** (Comments)：以 `#` 开头的行或使用 `Comment` 关键字
- **内置关键字** (Built-in Keywords)：如 `log`, `sleep`, `should be equal` 等
- **数字** (Numbers)：整数和浮点数
- **字符串** (Strings)：单引号或双引号包围的文本
- **标签** (Tags)：`[Tags]` 后面的标签内容

### 2. 颜色方案

- 默认文本：黑色 (#000000)
- 部分标题：蓝色 (#0000FF) + 粗体
- 关键字：紫色 (#800080) + 粗体
- 变量：深橙色 (#FF8C00)
- 注释：绿色 (#008000)
- 测试用例名：深红色 (#8B0000) + 粗体
- 设置项：靛蓝色 (#4B0082)
- 数字：洋红色 (#FF00FF)
- 字符串：深红色 (#DC143C)
- 内置关键字：中蓝色 (#0000CD)
- 标签：深绿色 (#006400)

### 3. 中文支持

RobotHighlighter 完全支持中文字符，包括：
- 中文测试用例名称
- 中文关键字名称
- 中文变量名
- 中文注释
- 中文标签

## 使用方法

### 基本使用

```python
from PyQt5.Qsci import QsciScintilla
from iplatform.highlight.RobotHighlighter import RobotHighlighter

# 创建编辑器
editor = QsciScintilla()
editor.setUtf8(True)  # 启用UTF-8支持

# 创建并设置高亮器
highlighter = RobotHighlighter(editor)
editor.setLexer(highlighter)
```

### 在 TextEditor 中的集成

TextEditor 会自动根据文件扩展名选择合适的高亮器：

```python
# 根据文件类型设置高亮器
if path.endswith('.robot') or path.endswith('.tsv'):
    self.lexer = RobotHighlighter(self)
else:
    self.lexer = PythonHighlighter(self)
```

## 技术实现

### 1. 继承关系

RobotHighlighter 继承自 `QsciLexerCustom`，这允许我们完全自定义词法分析逻辑。

### 2. 词法分析策略

- 按行处理文本
- 使用正则表达式识别各种语法元素
- 维护当前所在部分的状态（Test Cases、Keywords 等）
- 优先级处理，避免样式冲突

### 3. 性能优化

- 预编译正则表达式
- 避免重复的字符串操作
- 只处理可见区域的文本

## 扩展和自定义

### 添加新的内置关键字

在 `__init__` 方法中的 `self.builtin_keywords` 列表中添加新的关键字：

```python
self.builtin_keywords = [
    'log', 'sleep', 'should be true',
    # 添加新的关键字
    'your_new_keyword'
]
```

### 修改颜色方案

在 `__init__` 方法中修改颜色设置：

```python
self.setColor(QColor("#新颜色代码"), self.样式ID)
```

### 添加新的语法元素

1. 在类中定义新的样式ID
2. 在 `__init__` 中设置颜色和字体
3. 在 `styleText` 或 `_style_line_content` 中添加识别逻辑

## 注意事项

1. 确保编辑器启用了 UTF-8 编码支持
2. RobotFramework 使用制表符或多个空格作为分隔符
3. 大小写不敏感的关键字匹配
4. 正确处理嵌套的语法元素（如变量中的数字）

## 测试

使用 `test_robot_highlighter.py` 文件可以测试高亮器的功能：

```bash
python iplatform/highlight/test_robot_highlighter.py
```

这将打开一个包含示例 RobotFramework 代码的窗口，展示各种语法元素的高亮效果。
