@startuml AI生成RF脚本流程
left to right direction
title AI生成RobotFramework脚本系统框架

skinparam activity {
  BackgroundColor #F5F5F5
  BorderColor #333333
  FontName "Microsoft YaHei"
}

start

:输入测试用例信息\n(名称/步骤/路径);
#LightBlue

:获取测试设置\n(Suite Setup/Teardown);
#LightGreen

:查询相似测试用例\n从数据库;
#LightSalmon

if (找到相似用例?) then (是)
  :构建few-shot prompt\n包含示例代码;
  #Plum
else (否)
  :构建zero-shot prompt\n仅含用例步骤;
  #Plum
endif

:调用AI模型生成脚本;
#LightCoral

:处理AI返回结果;
#LightSteelBlue

:输出最终RF脚本;
#LightBlue

stop

note right
  **系统说明**
  1. 支持两种模式:
    - 新手模式:直接复用相似用例
    - 专家模式:基于示例生成新脚本
  2. 确保使用已有关键字
  3. 符合RF语法规范
end note

@enduml
