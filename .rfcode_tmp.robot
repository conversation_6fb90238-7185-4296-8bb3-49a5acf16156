*** Settings ***
Resource	../variable/resource.tsv
Variables	VMRequest.py
Resource	../../../../userkeywords_multi/resource.tsv


*** Test Cases ***
从本地文件夹升级版本
	#[Tags]	Smoke_ZDY	Smoke_TX	Smoke_TX_cpu	Smoke_TX_soft
	#执行本地命令_多模	D://version//GetVersion.bat
	sleep	10
	${pkgversion}	从本地文件夹获取版本号_多模	D://version
	从本地文件夹入库版本_多模	${ENODEB}	D://version	'PU'
	Comment	下载激活查询基站版本_多模	${ENODEB}	${pkgversion}	PU	false	${False}
	Comment	sleep	600
	log	${pkgversion}
	UMTS下载不激活相同基站版本_多模	${ENODEB}	${pkgversion}	'PU'
	sleep	600

Trace关键字示例
	${tracelogpath}	打开Trace工具_多模	E:/ZDY/JTcitrace_master	BPN	**********	**********	*********
	...	"0;1;162;163;224"
	#Comment	log	${tracelogpath}
	CComment	Comment	确保RF打开trace有效,需要清空trace所有选项,窗口,单板IP,CC IP ,log保存 路径等。	此处注意路径是反斜杠	${EMPTY}	${EMPTY}	${EMPTY}	${EMPTY}	窗口号用分号隔开，全部用引号括起来。
	Comment	sleep	10
	Comment	关闭Trace工具_多模	E:/ZDY/JTcitrace_master	BPN	************
	Comment	sleep	10
	sleep	10
	Comment	${result}	按关键字查找Trace打印值_多模	${tracelogpath}	BPN_224	CpuLoad
	Comment	log	${result}
	Comment	Comment	${EMPTY}	${EMPTY}	trace \ log文件路径	需要查找的文件名称
	Comment	log	${result}
	${keylist}	create list	CpuLoad	Error	Core22
	${result}	查询是否存在指定Trace打印_多模	${tracelogpath}	BPN_224	${keylist}
	log	${result}
	${result}	匹配Trace打印特定行数值_多模	${tracelogpath}	BPN_224	Core10	CpuLoad is
	log	${result}

通道自检示例
	创建基带板	${ENODEB}	${VBP1}
	${rst}	umts通道自检之上行chip活性实例检测	${VBP1}	1
	should be true	'${rst}' == 'True'
	${rst}	umts通道自检之上行bit活性实例检测	${VBP1}	1
	should be true	'${rst}' == 'True'
	umts通道自检之下行bit活性实例检测	${VBP1}	1
	${rst}	umts通道自检之看门狗复位检测	${VBP1}	1
	should be true	'${rst}' == 'True'



