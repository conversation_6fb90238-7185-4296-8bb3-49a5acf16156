*Settings*							
Suite Setup	加载配置	umtsRequest	${dataset}				
Suite Teardown	删除配置						
Variables	Request.py						
Resource	../variable/resource.tsv						
Resource	../../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../../template.tsv						
Resource	../5818template.tsv						
							
*Variables*							
							
*Test Cases*							
RAN-6092610 【四频机型】深度休眠与工具：所有频段NR LTE进入深度休眠和退出深度休眠后，操作NI扫描__RAN-6092610	关闭所有频段深度休眠开关						
	sleep	30					
	打开所有频段深度休眠开关						
	sleep	60					
	: FOR	${i}	IN RANGE	8			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	${cellId}
	: FOR	${i}	IN RANGE	7			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100
	...	${cellId}					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell}	2
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;4;0;0
	打开所有频段深度休眠开关						
	sleep	30					
	关闭所有频段深度休眠开关						
	sleep	60					
	: FOR	${i}	IN RANGE	8			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	${cellId}
	: FOR	${i}	IN RANGE	7			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30
	...	${cellId}					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell}	0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;0;0;0
	sleep	300					
	${subscriptionId2}	创建小区频谱扫描任务_多模	${cell2}				
	sleep	90					
	${filePath2}	导出频谱扫描数据_多模	${GNODEB}	${subscriptionId2}			
	验证频谱扫描结果CSV文件	${filePath2}	2				
	删除小区频谱扫描任务_多模	${cell2}	${subscriptionId2}				
	[Teardown]	恢复环境					
							
RAN-6092208 【四频机型】深度休眠与性能测量：所有频段NR LTE进入深度休眠和退出深度休眠后，验证性能测量数据正常上报__RAN-6092208	关闭所有频段深度休眠开关						
	sleep	30					
	打开所有频段深度休眠开关						
	sleep	60					
	: FOR	${i}	IN RANGE	8			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	${cellId}
	: FOR	${i}	IN RANGE	7			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100
	...	${cellId}					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell}	2
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;4;0;0
	#计数器C37058						
	${prruIds}	create list	101	102	111	112	301
	...	302	311	312			
	: FOR	${prruId}	IN	@{prruIds}			
	\	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Deep Sleep Duration(ms)	Replaceable unit ID	${prruId}
	\	should be true	${result}				
	\	exit for loop					
	打开所有频段深度休眠开关						
	sleep	30					
	关闭所有频段深度休眠开关						
	sleep	60					
	: FOR	${i}	IN RANGE	8			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	${cellId}
	: FOR	${i}	IN RANGE	7			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30
	...	${cellId}					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell}	0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;0;0;0
	#计数器C37058						
	: FOR	${prruId}	IN	@{prruIds}			
	\	${result}	获取C37058计数器数值上报	RRU Energy-Saving Duration(ms)	RRU Deep Sleep Duration(ms)	Replaceable unit ID	${prruId}
	\	should not be true	${result}				
	\	exit for loop					
	[Teardown]	恢复环境					
							
RAN-6092711 【四频机型】深度休眠与诊断：所有频段NR LTE进入深度休眠和退出深度休眠后，小区功率查询、查询prru实际发射功率__RAN-6092711	关闭所有频段深度休眠开关						
	sleep	30					
	打开所有频段深度休眠开关						
	sleep	60					
	: FOR	${i}	IN RANGE	8			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	${cellId}
	: FOR	${i}	IN RANGE	7			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100
	...	${cellId}					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell}	2
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;4;0;0
	Comment	查询并确认PRRU无发射功率_5818	101-instance	4			
	Comment	sleep	180				
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	${cellPower1}	查询NR小区功率_多模	${cell}			
	\	should be true	${cellPower1} < -15				
	打开所有频段深度休眠开关						
	sleep	30					
	关闭所有频段深度休眠开关						
	sleep	60					
	: FOR	${i}	IN RANGE	8			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	${cellId}
	: FOR	${i}	IN RANGE	7			
	\	${cellId}	Evaluate	${i} + 1			
	\	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30
	...	${cellId}					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell}	0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	查询并确认PRRU有发射功率_5818	101-instance	4	
	sleep	180					
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cell}	IN	@{cellList}			
	\	${cellPower1}	查询NR小区功率_多模	${cell}			
	\	should be true	${cellPower1} > -15				
	[Teardown]	恢复环境					
							
RAN-6096297 【四频机型】通道上多载波配置，只有部分载波进入深度休眠时，PA不关闭。全部载波进入关断时PA才关闭（其中2.3 TDL多载波+2.6G NR双载波+4.9G NR双载波配置）__RAN-6096297	关闭所有频段深度休眠开关						
	sleep	30					
	打开ITRAN-LTE-TDD深度休眠开关	${tddCell4}					
	打开NR深度休眠开关	${cell2}					
	打开NR深度休眠开关	${cell6}					
	同步规划区数据_多模	${GNODEB}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	4	
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	2
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	6
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;4;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell3}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell6}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	0	
	${energySavingMode}	set variable					
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	${energySavingMode}	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	sleep	300					
	验证TDL小区	${tddCell7}	${CPE3}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	sleep	60					
	关闭所有频段深度休眠开关						
	sleep	30					
	打开所有频段深度休眠开关						
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	2	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	4	
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	2
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	6
	Wait Until Keyword Succeeds	15min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;4;0;0
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfDeepSleep	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	rfDeepSleep	
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Close					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Close					
	打开所有频段深度休眠开关						
	sleep	30					
	关闭所有频段深度休眠开关						
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	2	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	3	
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	7
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	${energySavingMode}	set variable					
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	${energySavingMode}	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	301-instance	${energySavingMode}	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区	${tddCell3}	${CPE3}	${PDN}			
	sleep	60					
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
RAN-6092625 【四频机型】闭塞小区：FDL单载波配置，进入载波关断后闭塞解闭塞小区（闭塞小区时PA变为打开态）__RAN-6092625	关闭ITRAN-LTE-FDD深度休眠开关	${fddCell1}					
	关闭ITRAN-LTE-FDD深度休眠开关	${fddCell2}					
	sleep	30					
	打开ITRAN-LTE-FDD深度休眠开关	${fddCell1}					
	打开ITRAN-LTE-FDD深度休眠开关	${fddCell2}					
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	7,8	Close
	@{fddCellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cell}	IN	@{fddCellList}			
	\	关断EUtran小区_多模	${cell}				
	sleep	30					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	${energySavingMode}	set variable					
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	${energySavingMode}	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	7,8	Open
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094461	
	: FOR	${cell}	IN	@{fddCellList}			
	\	解关断EUtran小区_多模	${cell}				
	sleep	100					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	7,8	Close
	[Teardown]	恢复环境					
							
RAN-6093814 【四频机型】闭塞小区：TDL多载波配置，进入载波关断后闭塞解闭塞1个小区和全部小区（闭塞小区时PA仍为关闭态）__RAN-6093814	关闭ITRAN-LTE-TDD深度休眠开关	${tddCell3}					
	关闭ITRAN-LTE-TDD深度休眠开关	${tddCell7}					
	sleep	30					
	打开ITRAN-LTE-TDD深度休眠开关	${tddCell3}					
	打开ITRAN-LTE-TDD深度休眠开关	${tddCell7}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	关断EUtran小区_多模	${tddCell3}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	3	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094858	
	解关断EUtran小区_多模	${tddCell3}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	3	
	Wait Until Keyword Succeeds	30min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	30min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	30min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	#闭塞全部的两个双载波TDL小区						
	关闭ITRAN-LTE-TDD深度休眠开关	${tddCell3}					
	关闭ITRAN-LTE-TDD深度休眠开关	${tddCell7}					
	sleep	30					
	打开ITRAN-LTE-TDD深度休眠开关	${tddCell3}					
	打开ITRAN-LTE-TDD深度休眠开关	${tddCell7}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	关断EUtran小区_多模	${tddCell3}					
	关断EUtran小区_多模	${tddCell7}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	${energySavingMode}	set variable					
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	${energySavingMode}	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	Wait Until Keyword Succeeds	5min	15s	检查预期告警是否上报	${ENODEB}	198094858	
	解关断EUtran小区_多模	${tddCell3}					
	解关断EUtran小区_多模	${tddCell7}					
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	7	
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2	Close
	[Teardown]	恢复环境					
							
RAN-6100314 【四频机型】闭塞小区：TNR双载波配置，进入深度休眠后闭塞解闭塞1个小区和全部小区（闭塞小区时PA仍为关闭态）__RAN-6100314	关闭NR深度休眠开关	${cell4}					
	关闭NR深度休眠开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	打开NR深度休眠开关	${cell4}					
	打开NR深度休眠开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	45					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	5
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	闭塞NR小区_多模	${cell4}					
	sleep	30					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	100	4
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	2	
	Wait Until Keyword Succeeds	10min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	解闭塞NR小区_多模	${cell4}					
	sleep	30					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	4
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	#闭塞4.9G全部的两个小区						
	关闭NR深度休眠开关	${cell4}					
	关闭NR深度休眠开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	240					
	打开NR深度休眠开关	${cell4}					
	打开NR深度休眠开关	${cell5}					
	同步规划区数据_多模	${GNODEB}					
	sleep	45					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	5
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	4
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	闭塞NR小区_多模	${cell4}					
	闭塞NR小区_多模	${cell5}					
	sleep	45					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	100	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	100	5
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	0	
	${energySavingMode}	set variable					
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	${energySavingMode}	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=4	Cellshutdown
	Wait Until Keyword Succeeds	10min	60sec	确认告警在当前告警中_多模	${GNODEB}	GNBDUFunction=460-11_11142,NRCellDU=5	Cellshutdown
	解闭塞NR小区_多模	${cell4}					
	解闭塞NR小区_多模	${cell5}					
	sleep	45					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	150	5
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认PRRU节能状态	101-instance	rfCarrierShutdown	
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	9,10,11,12	Close
	[Teardown]	恢复环境					
							
test	#打开NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	deepSleepNRSwitch=1	deepSleepWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	deepSleepLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	1
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	2
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	3
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	5
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	6
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	7
	sleep	600					
	#关闭NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	deepSleepNRSwitch=0	deepSleepWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	deepSleepWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10
	...	deepSleepLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	1
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	2
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	3
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	5
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	6
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	7
	sleep	120					
	[Teardown]	恢复环境					
							
test2	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=1	switchoffTimeWindow=3	weekdaySleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	同步规划区数据_多模	${GNODEB}					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	2	
	[Teardown]	恢复环境					
							
test3	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=1	switchoffTimeWindow=3	weekdaySleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	同步规划区数据_多模	${GNODEB}					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	4	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	5	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	6	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	7	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	8	
	[Teardown]	恢复环境					
							
test4	关闭所有频段深度休眠开关						
	sleep	30					
	打开所有频段深度休眠开关						
	sleep	90					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	2	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	4	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	5	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	6	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	7	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Start	150	8	
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	1
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	2
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	3
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	5
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	6
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Start ES	100	7
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell3}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell6}	2	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	2	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;4;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;4;0;0
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	102-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	111-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	112-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	302-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	311-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	312-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	Comment	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12
	...	Open					
	打开所有频段深度休眠开关						
	sleep	30					
	关闭所有频段深度休眠开关						
	sleep	45					
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	1	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	2	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	3	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	4	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	5	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	6	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	7	
	确认LTE sonm节能上报成功判断	${ENODEB}	Deep Sleep	ES Stop	150	8	
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	1
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	2
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	3
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	4
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	5
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	6
	确认sonm节能上报成功	${GNODEB}	Carrier Shutdown ES	Deep Sleep	Stop ES	30	7
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell1}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell2}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell3}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell4}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell5}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell6}	0	
	Wait Until Keyword Succeeds	15min	60sec	确认小区节能状态	${cell7}	0	
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	1	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认FDL小区节能状态	${ENODEB}	2	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	3	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	4	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	5	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	6	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	7	0;0;0;0;0;0
	Wait Until Keyword Succeeds	10min	60sec	确认TDL小区节能状态	${ENODEB}	8	0;0;0;0;0;0
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	101-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	102-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	111-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	112-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	301-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	302-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	311-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	Wait Until Keyword Succeeds	15min	30sec	确认PRRU的PA通道状态	312-instance	1,2,3,4,5,6,7,8,9,10,11,12	Open
	验证FDL小区	${fddCell1}	${CPE3}	${PDN}			
	验证TDL小区业务	${tddCell3}	${CPE3}	${PDN}			
	验证NR小区	${cell1}	${CPE}	${PDN}			
	验证NR小区	${cell7}	${CPE2}	${PDN}			
	[Teardown]	恢复环境					
							
*Keywords*							
加载配置	[Arguments]	${scene}	${dataset}				
	${params}	获取资源	${scene}	${dataset}			
	创建基站_多模	${NODEB}	${UME}	${FDDFUNCTION}	${TDDFUNCTION}		
	${XML_PATH}	导出基站数据_多模	${ENODEB}				
	Set Global Variable	${XML_PATH}					
	创建UE对象	${CPE}					
	创建UE对象	${CPE2}					
	创建UE对象	${CPE3}					
	创建PDN	${PDN}					
	实例化单板_多模	${ENODEB}	${XML_PATH}				
	实例化无线配置_多模	${ENODEB}	${XML_PATH}	FT			
	实例化无线配置_多模	${GNODEB}	${XML_PATH}	V			
	保存NR小区信息_多模	${GNODEB}					
	保存LTE-FDD小区信息_多模	${ENODEB}					
	保存LTE-TDD小区信息_多模	${ENODEB}					
	导出基站XML并备份	${ENODEB}	${UME}				
	创建VSW_多模	${ENODEB}	${VSW}				
	关闭告警防抖_多模	${ENODEB}					
	获取所有小区别名_5824						
	同步基站时间_多模	${GNODEB}					
	同步测试机时间_多模	${GNODEB}					
							
删除配置	导入基站数据_多模	${GNODEB}	${XML_PATH}				
	删除UE对象	${CPE}					
	删除UE对象	${CPE2}					
	删除UE对象	${CPE3}					
	删除PDN	${PDN}					
	删除VSW_多模	${VSW}					
	释放实例化无线配置_多模	${GNODEB}					
	释放实例化无线配置_多模	${ENODEB}					
	释放实例化单板_多模	${GNODEB}					
	删除基站_多模	${NODEB}					
							
打开NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR符号关断开关	[Arguments]	${cellAlias}	${dtxFunction}=0				
	[Documentation]	功能：打开					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${dtxFunction}:节能类型，0是符号关断，1是增强型符号关断					
	...	[备注]：					
	...	已实现					
	...						
	...	EnergySavingService enableEnergySavingService					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=DTXESPolicy				
	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=${dtxFunction}	
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
打开NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR载波关断开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${carrierShutdownNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10	carrierShutdownLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
ITRAN-LTE-FDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
ITRAN-LTE-FDD-TDD符号关断开关	[Arguments]	${esDTXSwitch}					
	[Documentation]	${esDTXSwitch}：0是关闭，1是打开					
	...	${notifyBBFlag}}:节能类型，0是增强型符号关断，1是符号关断					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	同步规划区数据_多模	${GNODEB}					
	sleep	20					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=${esDTXSwitch}	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	同步规划区数据_多模	${GNODEB}					
							
打开所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR符号关断开关	${cell}				
							
关闭所有NR小区符号关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR符号关断开关	${cell}				
							
打开ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-FDD载波关断开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-TDD载波关断开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	打开NR载波关断开关	${cell}				
							
关闭所有NR小区载波关断开关	[Arguments]	${gnodeb}					
	@{cellList}	获取NR小区别名_多模	${gnodeb}				
	: FOR	${cell}	IN	@{cellList}			
	\	关闭NR载波关断开关	${cell}				
							
恢复环境	导入基站数据_多模	${ENODEB}	${XML_PATH}				
	sleep	240					
							
修改LTE小区15M带宽	[Arguments]	${cellAlias}					
	修改LTE采样速率模式配置_多模	${cellAlias}	2				
	修改LTE小区带宽_多模	${cellAlias}	15				
							
修改LTE小区20M带宽	[Arguments]	${cellAlias}					
	修改LTE采样速率模式配置_多模	${cellAlias}	2				
	修改LTE小区带宽_多模	${cellAlias}	20				
							
关闭所有LTE载波关断开关	关闭ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	关闭ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	关闭ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
打开所有LTE载波关断开关	打开ITRAN-LTE-FDD载波关断开关	${fddCell1}					
	打开ITRAN-LTE-FDD载波关断开关	${fddCell2}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell3}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell4}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell5}					
	打开ITRAN-LTE-TDD载波关断开关	${tddCell6}					
							
prru定时下电预下电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
	sleep	${delayTime}					
							
prru定时下电真实下电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
	sleep	${delayTime}					
							
prru上电阶段	[Arguments]	${gnbAlias}	${delayTime}				
	修改PRRU定时节电开关_多模	${gnbAlias}	0				
	sleep	${delayTime}					
							
PB诊断测试_下电	[Arguments]	${pbAlias}					
	#资源利用率信息查询						
	${result}	查询PB资源使用率_多模	${VSW}				
	should be true	0< ${result[0]} <80					
	${total}	evaluate	${result[4]}+${result[5]}				
	should be true	${total}==100					
	#PB单板运行时间						
	${result}	查询PB工作时长_多模	${pbAlias}				
	should be true	0 < ${result}					
	#PB光/电模块诊断						
	${result}	PB光电模块诊断_多模	${pbAlias}				
	should be true	'${result[1]}'=='Success'					
	should be true	'${result[3]}'=='SFP Transceiver'					
	#PB以太网口状态检测						
	${result}	PB以太网口状态检测_多模	${pbAlias}				
	Should contain	${result}	HalfDuplex				
	#PB以太网口误码率诊断						
	${result}	PB以太网口误码率诊断_多模	${pbAlias}				
	Should contain	${result}	No Light/Electricity in Optical/Electric Port				
	#PB以太网口通信检测						
	${result}	PB以太网口SNR诊断_多模	${pbAlias}				
	Should contain	${result}	No Light/Electricity in Optical/Electric Port				
	#光/电误码率诊断						
	${result}	PB光口误码率诊断_多模	${pbAlias}				
	should be true	'${result}'=='0'					
	#PB光口状态检测						
	${result}	PB光口状态诊断_多模	${pbAlias}				
	should be true	'${result[1]}'=='In Position'					
	#PB单板测试						
	${result}	PB诊断测试_多模	${pbAlias}				
	should be true	0< ${result} <90					
	#POE供电状态检测						
	${result}	PB供电状态检测_多模	${pbAlias}				
	should be true	'${result[1]}'=='Unknown'					
	#PB光纤测距						
	${result}	PB光纤测距_多模	${pbAlias}				
	should be true	'${result[2]}'=='Shorter Than 200 Meters'					
							
查询PRRU资产信息	[Arguments]	${gnbAlias}					
	重复执行_多模	3	同步基站资产信息_多模	${gnbAlias}			
	获取基站硬件资产信息_多模	${gnbAlias}					
	@{boards}	根据类型获取实例化单板别名_多模	${gnbAlias}	256			
	: FOR	${board}	IN	@{boards}			
	\	${result}	获取单板硬件资产信息_多模	${board}			
	\	确认硬件资产上报正常_多模	${board}	${result}			
							
PRRU电源功率测量C37016	[Arguments]	${gnbAlias}					
	同步基站时间_多模	${gnbAlias}					
	${nameList}	create list	PrruPwr				
	${queryModleDict}	create dictionary	5818_prrupower_query=me,Equipment,ReplaceableUnit				
	${resPathList}	性能测量测试模板	${nameList}	${queryModleDict}			
	${colList}	create list	13				
	读取csv数据平均值，判断有值	@{resPathList}[0]	${colList}				
							
性能测量测试模板	[Arguments]	${nameList}	${queryModleDict}	${filterlayer}=me	${filterlayer2}=me		
	${time}	查询基站时间_多模	${GNODEB}				
	${startTime}	${endTime}	获取基站历史数据采集开始和结束时间	${time}	30		
	: FOR	${name}	IN	@{nameList}			
	\	创建测量任务_多模	${GNODEB}	${name}			
	sleep	35min					
	${filePathList}	create list					
	: FOR	${queryModle}	IN	@{queryModleDict}			
	\	${groupLayer}	get from dictionary	${queryModleDict}	${queryModle}		
	\	${filePath}	按模板查询并导出测量数据_多模	${GNODEB}	${queryModle}	${groupLayer}	${startTime}
	...	${endTime}	${filterlayer2}	900			
	\	append to list	${filePathList}	${filePath}			
	[Teardown]	清除测量任务	@{nameList}				
	[Return]	${filePathList}					
							
清除测量任务	[Arguments]	@{nameList}					
	: FOR	${name}	IN	@{nameList}			
	\	run keyword and continue on failure	删除测量任务_多模	${GNODEB}	${name}		
							
读取csv数据平均值，判断有值	[Arguments]	${filePath}	${colList}				
	${resList}	create list					
	: FOR	${col}	IN	@{colList}			
	\	${ave}	读取csv列数据平均值_多模	${filePath}	${col}		
	\	should be true	${ave}				
	\	append to list	${resList}	${ave}			
	[Return]	${resList}					
							
创建并设置PRRU节能下电持续时间	[Arguments]	${gnbAlias}	${delay}				
	创建PRRU定时节电参数_多模	${gnbAlias}					
	${time}	查询基站时间_多模	${GNODEB}				
	Comment	${a}	set variable	${time}			
	${tmp}	split string	${time}	T			
	${tmp}	split string	${tmp[1]}	:			
	${timeStart}	set variable	${tmp[0]}:${tmp[1]}				
	${timeEnd}	获取延时后的时间点	${delay}				
	修改PRRU工作日节电时间_多模	${gnbAlias}	${timeStart}	${timeEnd}			
	修改PRRU休息日节电时间_多模	${gnbAlias}	${timeStart}	${timeEnd}			
	修改定时下电温差	30					
	同步基站时间_多模	${gnbAlias}					
	同步测试机时间_多模	${gnbAlias}					
	修改PRRU定时节电开关_多模	${gnbAlias}	1				
							
关闭所有频段符号关断开关	#关闭FDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	#关闭TDD制式SON开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	#关闭SON节能配置						
	${attr}	create dictionary	esDTXSwitch=0	notifyBBFlag=1	weekdayDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendDTXEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=0	esWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段符号关断开关	#FDD和TDD制式符号关断开关打开						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开NR制式符号关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
关闭所有频段载波关断开关	#LTE制式载波关断总开关关闭						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=0	switchoffTimeWindow=3	weekdayCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendCarrierEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=0	carrierShutdownWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	carrierShutdownWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段载波关断开关	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esSwitch=1	switchoffTimeWindow=3	weekdayCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendCarrierEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	rfShutdownSwitch=0
	...	carrierESLBEvaluateMod=0					
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
合并开站文件并开站	[Arguments]	${enbAlias}	${filePath}				
	${version}	查询基站运行版本号_多模	${enbAlias}	SOFTWARE			
	${tarName}	查询TAR包名称_多模	${enbAlias}	${version}			
	${tarName}	run keyword if	'${tarName}'==''	set variable	UNI_${version}.tar		
	...	ELSE	set variable	${tarName}			
	${filePathNew}	导出开站模板文件_多模	${enbAlias}	${tarName}			
	${filePath}	合并开站模板文件_多模	${filePathNew}	${filePath}			
	按开站模板开站_多模	${enbAlias}	${filePath}	${version}			
	sleep	600					
							
PRRU诊断测试	@{boards}	根据类型获取实例化单板别名_多模	${GNODEB}	128			
	: FOR	${board}	IN	@{boards}			
	\	${status}	查询单板信息_多模	${board}	operState		
	\	Run Keyword If	'${status}' == 'Normal'	PRRU诊断测试_多模	${board}		
	\	${result}	Run Keyword If	'${status}' == 'Normal'	查询PRRU温度_多模	${board}	
	\	should be true	0< ${result} <90				
	\	Run Keyword If	'${status}' == 'Normal'	确认PRRU发射功率正常_多模	${board}		
	\	Run Keyword If	'${status}' == 'Normal'	确认PRRURssi正常_多模	${board}		
							
打开所有NR制式符号关断开关	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=DTXESPolicy			
	\	${attrDict}	create dictionary	dtxEsSwitch=1	esWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	dtxFunction=0
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
							
打开所有NR制式载波关断开关	#打开NR制式载波关断开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	carrierShutdownNRSwitch=1	carrierShutdownWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	carrierShutdownWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	carrierShutdownLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
							
打开TDL小区级符号关断开关	[Arguments]	${moId}					
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=1	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=SoneNBPolicyLTE	moId=ES-TDD			
	__删除节点	${GNODEB}	${filterDict}	planarea			
	${attrDict}	create dictionary	sonFuncId=57	moId=1	refSonPolicyEsLTE=ENBCUCPFunction=460-11_10882,CULTE=1,SONPolicy=1,SonPolicyEsLTE=ES		
	${keyMoPathDict}	create dictionary	CUEUtranCellTDDLTE=${moId}				
	__创建节点	${GNODEB}	${attrDict}	${None}	SonCellPolicyTDDLTE	${keyMoPathDict}	
							
关闭TDL小区级符号关断开关	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	esDTXSwitch=0	notifyBBFlag=1	weekdayDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendDTXEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
							
关闭所有频段深度休眠开关	#LTE制式深度休眠总开关关闭						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=0	switchoffTimeWindow=3	weekdaySleepEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendSleepEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#关闭FDD制式深度休眠开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭TDD制式深度休眠开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=0			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#关闭NR制式深度休眠开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	deepSleepNRSwitch=0	deepSleepWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	deepSleepWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10
	...	deepSleepLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开所有频段深度休眠开关	#打开LTE制式载波关断总开关						
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=1	switchoffTimeWindow=3	weekdaySleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	#打开FDD制式载波关断开关						
	@{cellList}	获取FDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	Comment	${duID}	evaluate	'${cellAlias}'.split('-')[-1]		
	\	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开TDD制式载波关断开关						
	@{cellList}	获取TDD小区别名_多模	${ENODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'[-1]			
	\	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}		
	\	${attrDict}	create dictionary	energySavControl=1			
	\	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea	
	#打开NR制式深度休眠开关						
	@{cellList}	获取NR小区别名_多模	${GNODEB}				
	: FOR	${cellAlias}	IN	@{cellList}			
	\	${duID}	evaluate	'${cellAlias}'.split('-')[-1]			
	\	${filterDict}	create dictionary				
	\	set to dictionary	${filterDict}	mocName=CarrierESPolicy			
	\	${attrDict}	create dictionary	deepSleepNRSwitch=1	deepSleepWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10
	...	deepSleepLoadEvaluateSwitch=0					
	\	${keyMoPathDict}	create dictionary	ESPolicy=${duID}			
	\	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}
	...	${keyMoPathDict}					
	同步规划区数据_多模	${GNODEB}					
							
打开NR深度休眠开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${deepSleepNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	deepSleepNRSwitch=1	deepSleepWorkdayTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepWeekendTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	esIntervalTimeLen=10	deepSleepLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
关闭NR深度休眠开关	[Arguments]	${cellAlias}					
	[Documentation]	功能					
	...	[入参]：					
	...	${cellAlias}:小区别名					
	...	${deepSleepNRSwitch}:0是关闭，1是打开					
	...						
	...	[备注]：					
	...	已实现					
	${duID}	evaluate	'${cellAlias}'.split('-')[-1]				
	${filterDict}	create dictionary					
	set to dictionary	${filterDict}	mocName=CarrierESPolicy				
	${attrDict}	create dictionary	deepSleepNRSwitch=0	deepSleepWorkdayTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	deepSleepWeekendTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	esIntervalTimeLen=10	deepSleepLoadEvaluateSwitch=0
	${keyMoPathDict}	create dictionary	ESPolicy=${duID}				
	__修改节点属性	${cellAlias}	${filterDict}	${attrDict}	${None}	${None}	${keyMoPathDict}
							
打开ITRAN-LTE-TDD深度休眠开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=1	switchoffTimeWindow=3	weekdaySleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-TDD深度休眠开关	[Arguments]	${cellAlias}					
	${duID}	evaluate	'${cellAlias}'[-1]				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-TDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=0	switchoffTimeWindow=3	weekdaySleepEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendSleepEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellTDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
打开ITRAN-LTE-FDD深度休眠开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=1			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=1	switchoffTimeWindow=3	weekdaySleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	weekendSleepEsTime=1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=1				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
							
关闭ITRAN-LTE-FDD深度休眠开关	[Arguments]	${cellAlias}					
	Comment	${duID}	evaluate	'${cellAlias}'[-1]			
	${duID}	evaluate	str(int('${cellAlias}'.split('-')[-1]))				
	${attr}	create dictionary	sonFuncId=57	sonSwitch=0			
	修改SonControlLTE属性值_多模	${GNODEB}	ES-FDD	${attr}			
	${attr}	create dictionary	sleepEsSwitch=0	switchoffTimeWindow=3	weekdaySleepEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	weekendSleepEsTime=0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0	deepSleepLBEvaluateMod=0
	修改SonPolicyEsLTE属性值_多模	${GNODEB}	ES	${attr}			
	${filterDict}	create dictionary	mocName=CUEUtranCellFDDLTE	moId=${duID}			
	${attrDict}	create dictionary	energySavControl=0				
	__修改节点属性	${GNODEB}	${filterDict}	${attrDict}	planarea		
	同步规划区数据_多模	${GNODEB}					
	sleep	10					
