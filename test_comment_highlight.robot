*** Settings ***
Documentation    测试注释行的浅灰色高亮效果
Library          SeleniumLibrary

# 这是设置部分的注释，整行应该显示为浅灰色
# Comment 这是使用Comment关键字的注释
# 中文注释：这里包含中文字符，也应该是浅灰色

*** Variables ***
${URL}           https://www.example.com
# 这是变量部分的注释
${USERNAME}      testuser    # 行尾注释也应该是浅灰色
${PASSWORD}      testpass
# Comment 变量部分的Comment注释

*** Test Cases ***
测试注释高亮效果
    [Documentation]    测试各种注释的高亮效果
    [Tags]    注释    高亮    浅灰色
    
    # 这是测试用例中的注释，应该显示为浅灰色
    Log    正常的Log关键字
    # Log    这是注释中的Log关键字，整行应该是浅灰色
    Set Variable    test_value
    # Set Variable    注释中的Set Variable，整行浅灰色
    
    # 多行注释测试
    # 第一行注释
    # 第二行注释
    # 第三行注释
    
    Should Be Equal    value1    value1
    # Should Be Equal    注释中的关键字    不应该高亮
    
    # Comment 使用Comment关键字的注释
    Comment    这是Comment关键字，应该显示为浅灰色
    # Comment    注释中的Comment关键字

中文注释测试
    [Documentation]    测试中文注释的显示效果
    [Tags]    中文    注释
    
    # 纯中文注释：这是一个中文注释行
    Log    中文日志消息
    # 混合注释：This is mixed Chinese English comment
    Set Variable    中文变量值
    # 包含特殊字符的注释：！@#￥%……&*（）
    
    # Comment 中文Comment注释
    Comment    中文Comment关键字注释

行尾注释测试
    [Documentation]    测试行尾注释（注意：Robot Framework中#后的内容是注释）
    [Tags]    行尾注释
    
    Log    正常消息    # 这是行尾注释，应该是浅灰色
    Set Variable    value    # 行尾注释测试
    Should Be Equal    test    test    # 比较操作的行尾注释
    
    # 注意：在Robot Framework中，#后面的所有内容都被视为注释
    Log    消息内容    # 从这里开始到行尾都是注释

复杂注释场景
    [Documentation]    测试复杂的注释场景
    [Tags]    复杂注释
    
    # 包含关键字名称的注释：Log, Set Variable, Should Be Equal
    Log    实际的关键字执行
    # 包含变量语法的注释：${variable}, @{list}, &{dict}
    ${var}=    Set Variable    actual_value
    # 包含字符串的注释："string", 'another string'
    Should Contain    actual_text    expected
    
    # 多级缩进的注释
        # 缩进的注释
            # 更深缩进的注释
    
    # Comment 复杂的Comment注释，包含各种内容
    Comment    实际的Comment关键字执行

*** Keywords ***
带注释的用户关键字
    [Documentation]    包含注释的用户关键字
    [Arguments]    ${param}
    
    # 用户关键字中的注释
    Log    参数值: ${param}
    # 这里是关键字内部的注释
    ${result}=    Set Variable    processed_${param}
    # Comment 用户关键字中的Comment注释
    
    # 返回值注释
    RETURN    ${result}    # 返回处理后的结果

另一个带注释的关键字
    [Documentation]    另一个包含各种注释的关键字
    
    # 开始执行关键字
    Log    开始执行用户关键字
    
    # 循环注释
    FOR    ${i}    IN RANGE    1    4
        # 循环内部的注释
        Log    循环次数: ${i}
        # Comment 循环中的Comment注释
    END
    
    # 条件判断注释
    IF    True
        # 条件为真的注释
        Log    条件为真
    ELSE
        # 条件为假的注释
        Log    条件为假
    END
    
    # 关键字结束注释
    Log    关键字执行完成

# 文件末尾的注释
# 这些注释应该都显示为浅灰色
# Comment 文件末尾的Comment注释
# 包含中文的文件末尾注释：测试结束
