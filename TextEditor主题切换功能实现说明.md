# TextEditor主题切换功能实现说明

## 功能概述

为TextEditor添加了右键菜单主题切换功能，用户可以在"白色背景"和"黑色背景"两种主题之间切换，每种主题都有对应的配色方案。

## 功能特性

### 1. 右键菜单
- **主题子菜单**: 右键点击编辑器显示主题选项
- **单选模式**: 两个主题选项互斥，只能选择一个
- **状态显示**: 当前选中的主题会显示勾选标记

### 2. 白色主题（默认）
- **背景色**: 白色 (#FFFFFF)
- **文字色**: 黑色 (#000000)
- **配色方案**: 使用当前的语法高亮方案
- **适合**: 日间使用，传统编辑器风格

### 3. 黑色主题
- **背景色**: 深灰色 (#2B2B2B)
- **文字色**: 浅灰色 (#E0E0E0)
- **配色方案**: 专门为黑色背景优化的配色
- **适合**: 夜间使用，护眼模式

## 技术实现

### 1. 初始化设置

#### 导入必要的模块
```python
from PyQt5.QtWidgets import QApplication, QVBoxLayout, QMenu, QAction
```

#### 初始化主题相关属性
```python
def __init__(self):
    # ... 其他初始化代码
    
    # 主题相关初始化
    self._current_theme = "white"  # 默认白色主题
    
    # 设置右键菜单
    self.setContextMenuPolicy(Qt.CustomContextMenu)
    self.customContextMenuRequested.connect(self._show_context_menu)
```

### 2. 右键菜单实现

#### 菜单创建和显示
```python
def _show_context_menu(self, position):
    """显示右键菜单"""
    try:
        menu = QMenu(self)
        
        # 添加主题选项
        theme_menu = menu.addMenu("主题")
        
        # 白色背景选项
        white_action = QAction("白色背景", self)
        white_action.setCheckable(True)
        white_action.setChecked(self._current_theme == "white")
        white_action.triggered.connect(lambda: self._set_theme("white"))
        theme_menu.addAction(white_action)
        
        # 黑色背景选项
        dark_action = QAction("黑色背景", self)
        dark_action.setCheckable(True)
        dark_action.setChecked(self._current_theme == "dark")
        dark_action.triggered.connect(lambda: self._set_theme("dark"))
        theme_menu.addAction(dark_action)
        
        # 显示菜单
        menu.exec_(self.mapToGlobal(position))
        
    except Exception as e:
        print(f"显示右键菜单时出错: {e}")
```

#### 主题切换逻辑
```python
def _set_theme(self, theme):
    """设置主题"""
    try:
        if theme == self._current_theme:
            return
            
        self._current_theme = theme
        
        if theme == "white":
            self._apply_white_theme()
        elif theme == "dark":
            self._apply_dark_theme()
            
        print(f"已切换到{theme}主题")
        
    except Exception as e:
        print(f"设置主题时出错: {e}")
```

### 3. 白色主题实现

#### 白色主题配色
```python
def _apply_white_theme(self):
    """应用白色主题"""
    try:
        # 设置白色背景
        self.setColor(QColor("#000000"))  # 黑色文字
        self.setPaper(QColor("#FFFFFF"))  # 白色背景
        
        # 设置选中文本的颜色
        self.setSelectionBackgroundColor(QColor("#316AC5"))  # 蓝色选中背景
        self.setSelectionForegroundColor(QColor("#FFFFFF"))  # 白色选中文字
        
        # 设置光标颜色
        self.setCaretForegroundColor(QColor("#000000"))  # 黑色光标
        
        # 设置行号区域
        self.setMarginsBackgroundColor(QColor("#F0F0F0"))  # 浅灰色行号背景
        self.setMarginsForegroundColor(QColor("#666666"))  # 深灰色行号文字
        
        # 设置当前行高亮
        self.setCaretLineBackgroundColor(QColor("#F5F5F5"))  # 浅灰色当前行背景
        self.setCaretLineVisible(True)
        
        # 重新应用语法高亮
        if hasattr(self, 'lexer') and self.lexer:
            self.setLexer(self.lexer)
            
    except Exception as e:
        print(f"应用白色主题时出错: {e}")
```

#### 白色主题特点
- **背景**: 纯白色，符合传统编辑器习惯
- **文字**: 黑色，对比度高，易于阅读
- **行号**: 浅灰色背景，深灰色文字
- **当前行**: 浅灰色高亮
- **选中**: 蓝色背景，白色文字
- **语法高亮**: 使用原有的配色方案

### 4. 黑色主题实现

#### 黑色主题配色
```python
def _apply_dark_theme(self):
    """应用黑色主题"""
    try:
        # 设置黑色背景
        self.setColor(QColor("#E0E0E0"))  # 浅灰色文字
        self.setPaper(QColor("#2B2B2B"))  # 深灰色背景
        
        # 设置选中文本的颜色
        self.setSelectionBackgroundColor(QColor("#4A90E2"))  # 蓝色选中背景
        self.setSelectionForegroundColor(QColor("#FFFFFF"))  # 白色选中文字
        
        # 设置光标颜色
        self.setCaretForegroundColor(QColor("#FFFFFF"))  # 白色光标
        
        # 设置行号区域
        self.setMarginsBackgroundColor(QColor("#3C3C3C"))  # 深灰色行号背景
        self.setMarginsForegroundColor(QColor("#A0A0A0"))  # 浅灰色行号文字
        
        # 设置当前行高亮
        self.setCaretLineBackgroundColor(QColor("#404040"))  # 深灰色当前行背景
        self.setCaretLineVisible(True)
        
        # 应用黑色主题的语法高亮
        self._apply_dark_syntax_highlighting()
            
    except Exception as e:
        print(f"应用黑色主题时出错: {e}")
```

#### 黑色主题语法高亮
```python
def _apply_dark_syntax_highlighting(self):
    """应用黑色主题的语法高亮"""
    try:
        if hasattr(self, 'lexer') and self.lexer:
            # 为黑色主题设置特殊的语法高亮颜色
            if hasattr(self.lexer, 'setColor'):
                # Python语法高亮颜色（黑色主题）
                self.lexer.setColor(QColor("#E0E0E0"), 0)  # 默认文字 - 浅灰色
                self.lexer.setColor(QColor("#75715E"), 1)  # 注释 - 灰色
                self.lexer.setColor(QColor("#66D9EF"), 2)  # 关键字 - 青色
                self.lexer.setColor(QColor("#A6E22E"), 3)  # 字符串 - 绿色
                self.lexer.setColor(QColor("#AE81FF"), 4)  # 数字 - 紫色
                self.lexer.setColor(QColor("#F92672"), 5)  # 操作符 - 红色
                self.lexer.setColor(QColor("#FD971F"), 6)  # 函数名 - 橙色
                self.lexer.setColor(QColor("#E6DB74"), 7)  # 类名 - 黄色
                
            # 重新设置词法分析器
            self.setLexer(self.lexer)
            
    except Exception as e:
        print(f"应用黑色主题语法高亮时出错: {e}")
```

#### 黑色主题特点
- **背景**: 深灰色 (#2B2B2B)，护眼效果好
- **文字**: 浅灰色 (#E0E0E0)，在深色背景上清晰可见
- **行号**: 深灰色背景，浅灰色文字
- **当前行**: 深灰色高亮
- **选中**: 蓝色背景，白色文字
- **语法高亮**: 专门为黑色背景优化的配色

## 配色方案对比

### 白色主题配色
| 元素 | 颜色 | 说明 |
|------|------|------|
| 背景 | #FFFFFF | 纯白色 |
| 文字 | #000000 | 纯黑色 |
| 光标 | #000000 | 黑色光标 |
| 选中背景 | #316AC5 | 蓝色 |
| 选中文字 | #FFFFFF | 白色 |
| 行号背景 | #F0F0F0 | 浅灰色 |
| 行号文字 | #666666 | 深灰色 |
| 当前行 | #F5F5F5 | 浅灰色高亮 |

### 黑色主题配色
| 元素 | 颜色 | 说明 |
|------|------|------|
| 背景 | #2B2B2B | 深灰色 |
| 文字 | #E0E0E0 | 浅灰色 |
| 光标 | #FFFFFF | 白色光标 |
| 选中背景 | #4A90E2 | 蓝色 |
| 选中文字 | #FFFFFF | 白色 |
| 行号背景 | #3C3C3C | 深灰色 |
| 行号文字 | #A0A0A0 | 浅灰色 |
| 当前行 | #404040 | 深灰色高亮 |

### 黑色主题语法高亮
| 语法元素 | 颜色 | 说明 |
|----------|------|------|
| 默认文字 | #E0E0E0 | 浅灰色 |
| 注释 | #75715E | 灰色 |
| 关键字 | #66D9EF | 青色 |
| 字符串 | #A6E22E | 绿色 |
| 数字 | #AE81FF | 紫色 |
| 操作符 | #F92672 | 红色 |
| 函数名 | #FD971F | 橙色 |
| 类名 | #E6DB74 | 黄色 |

## 用户体验

### 1. 操作流程
1. **右键点击**: 在TextEditor中右键点击
2. **选择主题**: 点击"主题"子菜单
3. **切换主题**: 选择"白色背景"或"黑色背景"
4. **即时生效**: 主题立即应用到编辑器

### 2. 视觉反馈
- ✅ **菜单标记**: 当前主题显示勾选标记
- ✅ **即时切换**: 主题切换立即生效
- ✅ **完整配色**: 包括背景、文字、行号、语法高亮等

### 3. 使用场景
- **白色主题**: 适合日间使用，传统编辑器风格
- **黑色主题**: 适合夜间使用，减少眼部疲劳

## 技术要点

### 1. 右键菜单设置
```python
# 设置自定义右键菜单
self.setContextMenuPolicy(Qt.CustomContextMenu)
self.customContextMenuRequested.connect(self._show_context_menu)
```

### 2. 主题状态管理
```python
# 主题状态跟踪
self._current_theme = "white"  # 默认白色主题

# 避免重复设置
if theme == self._current_theme:
    return
```

### 3. 语法高亮重新应用
```python
# 白色主题：使用原有配色
if hasattr(self, 'lexer') and self.lexer:
    self.setLexer(self.lexer)

# 黑色主题：自定义配色
self._apply_dark_syntax_highlighting()
```

### 4. 错误处理
- 所有主题相关方法都有异常处理
- 详细的错误日志输出
- 不影响编辑器的其他功能

## 扩展性

### 1. 添加新主题
```python
# 在 _set_theme 方法中添加新主题
elif theme == "new_theme":
    self._apply_new_theme()

# 在右键菜单中添加新选项
new_action = QAction("新主题", self)
new_action.setCheckable(True)
new_action.setChecked(self._current_theme == "new_theme")
new_action.triggered.connect(lambda: self._set_theme("new_theme"))
theme_menu.addAction(new_action)
```

### 2. 主题配置持久化
```python
# 可以添加配置保存功能
def _save_theme_preference(self, theme):
    # 保存到配置文件
    pass

def _load_theme_preference(self):
    # 从配置文件加载
    return "white"  # 默认值
```

## 总结

TextEditor主题切换功能提供了完整的主题体验：

1. **用户友好**: 简单的右键菜单操作
2. **视觉完整**: 包括背景、文字、行号、语法高亮等所有元素
3. **即时生效**: 主题切换立即应用
4. **护眼模式**: 黑色主题适合长时间编程
5. **扩展性好**: 易于添加新主题

这大大提升了用户的编辑体验，特别是在不同光线环境下的使用舒适度！🎨
