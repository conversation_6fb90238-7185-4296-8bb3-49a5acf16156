# 中文关键字下划线修复说明

## 问题描述

**问题**: 关键字是中文时，下划线的长度只有一半，没有覆盖整个中文关键字

**原因**: 中文字符在编辑器中的显示宽度通常是英文字符的两倍，但在计算字符位置时没有正确处理这种宽度差异，导致下划线长度不正确。

## 解决方案

### 1. 字符识别改进

**原来的代码**:
```python
# 只支持英文字符
while start > 0 and (line_text[start - 1].isalnum() or line_text[start - 1] == '_'):
    start -= 1
```

**修复后的代码**:
```python
# 支持中文字符
while start > 0 and self._is_identifier_char(line_text[start - 1]):
    start -= 1

def _is_identifier_char(self, char):
    """检查字符是否是标识符字符（支持中文）"""
    return char.isalnum() or char == '_' or ord(char) > 127  # 支持中文等非ASCII字符
```

### 2. 字节位置计算

**核心问题**: QsciScintilla内部使用UTF-8编码，中文字符占用3个字节，而英文字符只占用1个字节。

**解决方案**: 新增字符索引到字节位置的转换函数
```python
def _char_index_to_byte_position(self, line, char_index):
    """将字符索引转换为字节位置，正确处理中文字符"""
    try:
        line_start_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)
        line_text = self.text(line)
        
        if char_index <= 0:
            return line_start_pos
        
        if char_index >= len(line_text):
            return line_start_pos + len(line_text.encode('utf-8'))
        
        # 获取到指定字符索引的文本部分
        text_part = line_text[:char_index]
        # 计算UTF-8编码的字节长度
        byte_length = len(text_part.encode('utf-8'))
        
        return line_start_pos + byte_length
        
    except Exception as e:
        print(f"字符索引转换字节位置时出错: {e}")
        # 回退到简单的字符位置计算
        line_start_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)
        return line_start_pos + char_index
```

### 3. 下划线位置计算改进

**原来的代码**:
```python
# 直接使用字符索引，对中文字符不准确
start_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line) + start
end_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line) + end
```

**修复后的代码**:
```python
# 使用字节位置计算，支持中文字符
start_byte_pos = self._char_index_to_byte_position(line, start)
end_byte_pos = self._char_index_to_byte_position(line, end)
```

### 4. 函数名验证改进

**原来的代码**:
```python
# 只支持英文开头
if function_name and (function_name[0].isalpha() or function_name[0] == '_'):
    return function_name
```

**修复后的代码**:
```python
# 支持中文开头
if function_name and (function_name[0].isalpha() or function_name[0] == '_' or ord(function_name[0]) > 127):
    return function_name
```

## 技术细节

### UTF-8编码处理
- **英文字符**: 1字节 (例如: 'a' = 1字节)
- **中文字符**: 3字节 (例如: '测' = 3字节)
- **混合文本**: "test测试" = 4 + 6 = 10字节

### 字符宽度处理
- **显示宽度**: 中文字符通常显示为英文字符的2倍宽度
- **存储长度**: 中文字符在UTF-8中占用3字节
- **字符计数**: Python中中文字符计为1个字符

### QsciScintilla API
- `SCI_POSITIONFROMLINE`: 获取行的字节起始位置
- `SCI_INDICATORFILLRANGE`: 使用字节位置和字节长度设置指示器

## 测试验证

### 测试文件: `test_chinese_keywords.py`

包含以下测试场景：
1. **纯中文函数名**: `测试函数()`
2. **中英文混合**: `中英文混合函数()`
3. **带下划线**: `带下划线的中文函数_test()`
4. **中文类方法**: `测试类.中文方法()`

### 测试步骤
1. 在TextEditor中打开 `test_chinese_keywords.py`
2. 按住Ctrl键
3. 鼠标悬停在各种中文函数名上
4. 验证下划线是否完全覆盖中文字符
5. 点击测试跳转功能

## 预期效果

### 修复前
- 中文关键字下划线只覆盖一半
- 显示效果不完整

### 修复后
- 中文关键字下划线完全覆盖
- 支持纯中文、中英文混合、带下划线的各种情况
- 与英文关键字具有相同的视觉效果

## 兼容性

- ✅ 完全向后兼容英文函数名
- ✅ 支持中文函数名
- ✅ 支持中英文混合函数名
- ✅ 支持各种Unicode字符
- ✅ 错误处理和回退机制
